import os
import traceback

from PyQt5.QtWidgets import QLabel, QLineEdit, QCheckBox, QPushButton
from global_tools.ui_tools import (
	QLineEditSynchronizer, LineEditMemory, LogOutput, get_widgets_from_layout,
	CheckBoxStateManager, CheckBoxManager,
	LineEditManager, constant, InputCompleterCache,
	QLabelManager,
	QPushButtonManager,
	QCheckBoxSynchronizer,
)
from global_tools.utils import ClassInstanceManager

from anylabeling.customize_ui.helper.const import X_LABEL_LOG, CONFIG_PATH
from anylabeling.customize_ui.layout.layout import Ui_Form
from anylabeling.customize_ui.src.helper.const import LINE_EDIT_MEMORY_FILE_NAME


def line_edit_sync( cls, ui_form: Ui_Form, log_output: LogOutput ):
	""" 
	创建并初始化QLineEditSynchronizer实例，用于管理行编辑器的同步功能。
	
	该方法将为指定的QLineEdit控件添加同步功能，使其能够同步输入内容。
	
	工作流程:
		1. 使用已收集的QLineEdit列表创建QLineEditSynchronizer实例
		2. 设置同步组
	"""
	logger = ClassInstanceManager.get_instance( key=X_LABEL_LOG )
	LINE_EDIT = [
		[
			ui_form.lineEdit_76,
			ui_form.lineEdit_174,
		],
		[
			ui_form.lineEdit_85,
			ui_form.lineEdit_175,
		],
		[
			ui_form.lineEdit_86,
			ui_form.lineEdit_176,
		],
		[
			ui_form.lineEdit_6,
			ui_form.lineEdit_48,
		],
		[
			ui_form.lineEdit_12,
			ui_form.lineEdit_61,
		],
		[
			ui_form.lineEdit_47,
			ui_form.lineEdit_63,
		],
	]
	cls.__qline_edit_synchronizer = QLineEditSynchronizer( sync_groups=LINE_EDIT, log_output=log_output, logger=logger )

def qcheckbox_sync( cls, ui_form: Ui_Form, log_output: LogOutput ):
	"""
	创建并初始化QCheckBoxSynchronizer实例，用于管理复选框的同步功能。
	"""
	logger = ClassInstanceManager.get_instance( key=X_LABEL_LOG )
	CHECK_BOX = [
		[ 
			# segment
			ui_form.rotate_40,
			ui_form.rotate_38,
			ui_form.rotate_146,
			ui_form.rotate_34,
		],
		[
			# obb
			ui_form.rotate_41,
			ui_form.rotate_39,
			ui_form.rotate_147,
			ui_form.rotate_35,
		]
	]
	cls.__qcheckbox_synchronizer = QCheckBoxSynchronizer( sync_groups=CHECK_BOX, log_output=log_output, logger=logger )


class CommonLineEdit:
	LINE_EDIT_LIST: list[ QLineEdit ] = [ ]
	qline_edit_manager: LineEditManager
	qline_edit_memory: LineEditMemory
	qline_edit_synchronizer: QLineEditSynchronizer
	input_completer_cache: InputCompleterCache
	checkbox_state_manager: CheckBoxStateManager
	checkbox_manager: CheckBoxManager
	label_manager: QLabelManager
	push_button_manager: QPushButtonManager

	@staticmethod
	def get_line_edit_list( ui_form: Ui_Form ):
		"""
		获取并存储所有QLineEdit控件的列表。
		
		该方法将从指定的UI表单中收集所有QLineEdit控件，并将它们存储为类的私有静态变量。
		
		参数:
			ui_form: Ui_Form - 包含QLineEdit控件的UI表单
			
		工作流程:
			1. 从指定UI表单中收集所有QLineEdit控件
			2. 将收集到的控件添加到类的私有静态变量中
		"""
		# 收集并存储所有QLineEdit控件
		CommonLineEdit.LINE_EDIT_LIST = [
			*get_widgets_from_layout( layout=ui_form.verticalLayout_112, widget_type=QLineEdit ),
			*get_widgets_from_layout( layout=ui_form.verticalLayout_121, widget_type=QLineEdit ),
			*get_widgets_from_layout( layout=ui_form.verticalLayout, widget_type=QLineEdit ),
		]

	@staticmethod
	def setup_input_validators():
		"""
		为所有需要验证的QLineEdit控件设置输入验证器。
		
		该方法将为特定的QLineEdit控件添加验证功能，如Windows路径验证和数字格式验证。
		这些验证器可以确保用户输入符合预期格式，提高应用程序的稳定性和用户体验。
		
		工作流程:
			1. 定义需要验证的控件列表
			2. 使用列表推导式批量设置验证器
		"""
		if not hasattr( CommonLineEdit, 'qline_edit_manager' ):
			return

		# # 定义需要Windows路径验证的控件列表
		validator_path_qlienedit = [
			"lineEdit_6", "lineEdit_7", "lineEdit_9", "lineEdit_11", "lineEdit_1",
			"lineEdit_4", "lineEdit_46", "lineEdit_47", "lineEdit_61", "lineEdit_66",
			"lineEdit_78",
			"lineEdit_48",
			"lineEdit_63",

		]

		# 批量设置Windows路径验证器
		for name in validator_path_qlienedit:
			try:
				CommonLineEdit.qline_edit_manager.set_input_validator(
					name=name, validator_type="regexp", pattern=constant.WINDOWS_PATH_REGEX
				)
			except Exception as _:
				traceback.print_exc()
		# 如果控件不存在或无法设置验证器，则忽略

		# --------------------------------------------------------------------------------------------------------------------------------------

		# 定义需要比例格式验证的控件列表
		validator_ratio_qlienedit = [ "lineEdit_8", "lineEdit_5" ]

		# 比例格式正则表达式 - 匹配形如85:15的格式
		ratio_regex = r'^\d+:\d+$'

		# 批量设置比例格式验证器
		for name in validator_ratio_qlienedit:
			try:
				result = CommonLineEdit.qline_edit_manager.set_input_validator(
					name=name, validator_type="regexp", pattern=ratio_regex
				)
			except Exception as e:
				traceback.print_exc()
				# 如果控件不存在或无法设置验证器，则忽略
				pass

		# --------------------------------------------------------------------------------------------------------------------------------------

		# 定义需要数字验证的控件列表
		validator_num_qlienedit = [
			"lineEdit_2",
			"lineEdit_3",
			"lineEdit_57",
			"lineEdit_64",
			"lineEdit_65",
			"lineEdit_67",
			"lineEdit_70",
			"lineEdit_71",
			"lineEdit_72",
			"lineEdit_79",
			"lineEdit_74",
			"lineEdit_77",
			"lineEdit_81",
		]

		# 批量设置数字验证器
		for name in validator_num_qlienedit:
			try:
				result = CommonLineEdit.qline_edit_manager.set_input_validator(
					name=name, validator_type="regexp", pattern=constant.FLOAT_REGEX
				)
			except Exception as e:
				traceback.print_exc()
				# 如果控件不存在或无法设置验证器，则忽略
				pass

	@staticmethod
	def line_edit_memory( log_output: LogOutput ):
		"""
		创建并初始化LineEditMemory实例，用于管理行编辑器的记忆功能。
		
		该方法将为所有已收集的QLineEdit控件添加记忆功能，使其能够记住用户之前输入的内容。
		
		参数:
			log_output: LogOutput - 日志输出接口，用于显示操作信息
			
		工作流程:
			1. 从ClassInstanceManager获取日志记录器实例
			2. 使用已收集的QLineEdit列表创建LineEditMemory实例
			3. 将实例保存为类的私有静态变量
		"""
		# 获取全局日志记录器实例
		logger = ClassInstanceManager.get_instance( key=X_LABEL_LOG )
		# 创建LineEditMemory实例，管理所有已收集的行编辑器
		CommonLineEdit.qline_edit_memory = LineEditMemory(
			line_edits=CommonLineEdit.LINE_EDIT_LIST, log_output=log_output, logger=logger, config_path=CONFIG_PATH,
			config_filename=LINE_EDIT_MEMORY_FILE_NAME,
			sync_detection_interval_ms=500
		)

	@staticmethod
	def line_edit_manager():
		"""
		创建并初始化LineEditManager实例，用于管理行编辑器的自动清除功能。
		
		该方法将为所有已收集的QLineEdit控件添加自动清除功能，使其在失去焦点时自动清除输入内容。
		
		工作流程:
			1. 使用已收集的QLineEdit列表创建LineEditManager实例
			2. 设置所有行编辑器自动清除功能为关闭
		"""
		# 创建LineEditManager实例，管理所有已收集的行编辑器

		CommonLineEdit.qline_edit_manager = LineEditManager( *CommonLineEdit.LINE_EDIT_LIST )
		CommonLineEdit.qline_edit_manager.set_all_auto_clear( enabled=False )

	@staticmethod
	def line_edit_completer_cache( ui_form: Ui_Form = None ):
		""" 
		创建并初始化InputCompleterCache实例，用于管理行编辑器的自动补全功能。
		
		该方法将为指定的QLineEdit控件添加自动补全功能，使其能够根据用户输入的历史记录进行自动补全。
		"""

		LINE_EDIT = {
			ui_form.lineEdit_46,
			ui_form.lineEdit_47,
			ui_form.lineEdit_1,
			ui_form.lineEdit_4,
			ui_form.lineEdit_9,
			ui_form.lineEdit_11,
			*get_widgets_from_layout( layout=ui_form.horizontalLayout_26, widget_type=QLineEdit ),
			*get_widgets_from_layout( layout=ui_form.verticalLayout_59, widget_type=QLineEdit )
		}

		CommonLineEdit.input_completer_cache = InputCompleterCache(
			line_edit_or_list=[ *LINE_EDIT ],
			config_path=os.sep.join( [ CONFIG_PATH, "input_completer.json" ] ),
		)

	@staticmethod
	def check_box_manager( ui_form: Ui_Form, log_output: LogOutput ):
		"""
		创建并初始化CheckBoxManager实例，用于管理复选框的同步功能。
		
		该方法将为指定的QCheckBox控件添加同步功能，使其能够同步输入内容。
		"""
		logger = ClassInstanceManager.get_instance( key=X_LABEL_LOG )

		CHECK_BOX = [
			*get_widgets_from_layout( layout=ui_form.verticalLayout, widget_type=QCheckBox ),
			*get_widgets_from_layout( layout=ui_form.verticalLayout_112, widget_type=QCheckBox ),
			*get_widgets_from_layout( layout=ui_form.verticalLayout_121, widget_type=QCheckBox ),
		]

		# 管理PyQt5容器中QCheckBox控件的状态，支持状态持久化和恢复。
		CommonLineEdit.checkbox_state_manager = CheckBoxStateManager(
			container=CHECK_BOX,
			config_dir=CONFIG_PATH,
			log_output=log_output,
			logger=logger
		)
		CommonLineEdit.checkbox_state_manager.load_states()

		# 管理 QCheckBox 控件的状态，如获取选中状态，设置选中状态，设置点击回调等
		CommonLineEdit.checkbox_manager = CheckBoxManager(
			checkboxes=CHECK_BOX,
			logger=logger
		)

	@staticmethod
	def qlabel_manager( ui_form: Ui_Form ):
		"""
		创建并初始化QLabelManager实例，用于管理QLabel控件的同步功能。
		"""
		LABEL_LIST = [
			ui_form.label_587,
			ui_form.label_619,
			ui_form.label_292,
			ui_form.label_34,
			ui_form.label_37,
			ui_form.label_178,
			ui_form.label_69,
			ui_form.label_51,
			ui_form.label_12,
			ui_form.label_8,
			ui_form.label_15,
			ui_form.label_24,
			ui_form.label_27,
			ui_form.label_636,
			ui_form.label_619,
			ui_form.label_75,
			ui_form.label_180,
			ui_form.label_297,
			ui_form.label_261,
			*get_widgets_from_layout( layout=ui_form.verticalLayout_120, widget_type=QLabel )
		]

		CommonLineEdit.label_manager = QLabelManager(
			labels=LABEL_LIST
		)

	@staticmethod
	def qpush_button_manager( ui_form: Ui_Form ):
		"""
		创建并初始化QPushButtonManager实例，用于管理QPushButton控件的同步功能。
		"""
		BUTTON_LIST = [
			*get_widgets_from_layout( layout=ui_form.verticalLayout_112, widget_type=QPushButton ),
			*get_widgets_from_layout( layout=ui_form.verticalLayout_121, widget_type=QPushButton ),
			*get_widgets_from_layout( layout=ui_form.verticalLayout, widget_type=QPushButton ),
		]
		CommonLineEdit.push_button_manager = QPushButtonManager( buttons=BUTTON_LIST )
