#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于坐标相似度的标注比较处理器

严格按照 coordinate_similarity_processor_design.md 文档实现的多维度坐标相似度计算系统。
实现四个核心维度的相似度计算和五层智能决策机制，确保坐标相似度计算的合理性。

核心特性 (新架构):
前提条件检查:
- 尺度相似度计算 - 多尺度特征分析 (阈值≥40%)
- 形状相似度计算 - 基于Hausdorff距离的形状匹配 (阈值≥30%)

核心相似度计算:
1. 几何相似度计算 (权重50%) - 基于Shapely库的精确几何计算 (仅在前提条件满足时)
2. 位置相似度计算 (权重50%) - 多层次位置分析 (始终计算)

关键改进:
- 前提条件检查只影响几何相似度，位置相似度始终正常计算
- 综合相似度 = 几何相似度×50% + 位置相似度×50%
- 五层智能决策机制仅基于几何和位置相似度

作者: AI Assistant
创建时间: 2025-01-21
更新时间: 2025-07-21 (新架构)
版本: 2.0.0
"""

import os
import json
import time
import threading
from typing import Dict, List, Tuple, Any, Optional, Union
from pathlib import Path
import numpy as np

from global_tools.utils import Logger, Colors


class CoordinateSimilarityProcessor:
	"""
	基于坐标相似度的标注比较处理器 (新架构)

	新架构计算体系：
	前提条件检查：
	- 尺度相似度 - 面积比例、周长比例、边界框尺度、形状复杂度 (阈值≥40%)
	- 形状相似度 - Hausdorff距离、轮廓匹配、凸包相似度、描述符相似度 (阈值≥30%)

	核心相似度计算 (仅在前提条件满足时)：
	1. 几何相似度 (50%权重) - IoU、IoS、包含关系、几何中心距离
	2. 位置相似度 (50%权重) - 绝对位置、相对位置、边界框位置、质心偏移

	关键特性：
	- 前提条件检查只影响几何相似度计算，位置相似度始终正常计算
	- 尺度和形状相似度仅作为前提条件，不参与综合相似度计算
	- 综合相似度 = 几何相似度 × 50% + 位置相似度 × 50%

	五层智能决策机制：
	1. 快速排除检查 - 零坐标、极端尺寸差异、完全分离
	2. 高相似度检查 - 综合相似度≥0.85或几何/位置单维度极高
	3. 中等相似度细化判断 - 几何和位置维度一致性验证
	4. 低相似度确认 - 综合相似度≤0.30或关键维度否决
	5. 边界情况处理 - 模糊区间精细化判断

	使用示例:
		processor = CoordinateSimilarityProcessor(
			logger=logger,
			log_output=log_output,
			mode='strict'  # 严格模式
		)

		result = processor.compare_coordinates(
			points1=[[100,100], [200,100], [200,200], [100,200]],
			points2=[[105,105], [205,105], [205,205], [105,205]]
		)

		if result['decision'] == 'skip':
			print(f"坐标相似，跳过: {result['reason']}")
		else:
			print(f"坐标不同，追加: {result['reason']}")
	"""

	def __init__(
			self,
			logger: Optional[ Logger ] = None,
			log_output: Optional[ Any ] = None,
			mode: str = 'strict'  # 'strict' 或 'loose'
	):
		"""
		初始化坐标相似度处理器

		参数:
			logger: 日志记录器
			log_output: 日志输出对象
			mode: 计算模式，'strict'(严格模式)或'loose'(宽松模式)
		"""
		self.__logger = logger or Logger( "CoordinateSimilarityProcessor" )
		self.__log_output = log_output
		self.__mode = mode

		# 缓存管理
		self.__calculation_cache = { }  # 计算结果缓存
		self.__cache_lock = threading.Lock()  # 缓存锁
		self.__max_cache_size = 1000  # 最大缓存数量

		# 相似度权重配置 (新架构)
		# 注意：实际综合相似度计算只使用几何50% + 位置50%
		# 尺度和形状权重仅用于配置显示和兼容性，不参与实际计算
		self.__geometric_weight = 0.50  # 几何相似度权重50% (实际使用)
		self.__position_weight = 0.50  # 位置相似度权重50% (实际使用)
		self.__scale_weight = 0.00  # 尺度相似度权重0% (仅作前提条件)
		self.__shape_weight = 0.00  # 形状相似度权重0% (仅作前提条件)

		# 决策阈值配置 (根据模式设置)
		if mode == 'strict':
			# 严格模式阈值 (默认)
			self.__extreme_similarity_threshold = 0.90  # 极高相似度阈值
			self.__high_similarity_threshold = 0.75  # 高相似度阈值
			self.__medium_similarity_threshold = 0.60  # 中等相似度阈值
			self.__low_similarity_threshold = 0.40  # 低相似度阈值
		else:
			# 宽松模式阈值
			self.__extreme_similarity_threshold = 0.85
			self.__high_similarity_threshold = 0.70
			self.__medium_similarity_threshold = 0.55
			self.__low_similarity_threshold = 0.35

		# 细化判断阈值配置 (五层决策机制)
		self.__single_dimension_high_threshold = 0.95  # 单维度极高相似度阈值
		self.__multi_dimension_threshold = 0.75  # 多维度一致性阈值
		self.__key_dimension_geometric_threshold = 0.80  # 关键维度几何阈值
		self.__key_dimension_position_threshold = 0.75  # 关键维度位置阈值
		self.__geometric_veto_threshold = 0.30  # 几何维度否决阈值
		self.__position_veto_threshold = 0.20  # 位置维度否决阈值

		# 几何计算参数
		self.__min_area_threshold = 1e-6  # 最小面积阈值
		self.__max_size_ratio = 100.0  # 最大尺寸比例
		self.__distance_threshold_factor = 0.1  # 距离阈值因子
		self.__log_threshold = 2.0  # 对数阈值(允许4倍面积差异)

		# 前提条件阈值配置 (新增)
		self.__scale_prerequisite_threshold = 0.40  # 尺度相似度前提阈值 (40%)
		self.__shape_prerequisite_threshold = 0.30  # 形状相似度前提阈值 (30%)

		# 新架构决策阈值配置 (新增)
		self.__new_extreme_similarity_threshold = 0.85  # 新架构极高相似度阈值
		self.__new_low_similarity_threshold = 0.30  # 新架构低相似度阈值
		self.__new_high_similarity_threshold = 0.65  # 新架构接近高相似度阈值
		self.__new_single_dimension_high_threshold = 0.95  # 新架构单维度极高阈值
		self.__new_core_dimension_threshold = 0.70  # 新架构核心维度基本要求阈值
		self.__new_geometric_veto_threshold = 0.25  # 新架构几何维度否决阈值
		self.__new_position_veto_threshold = 0.20  # 新架构位置维度否决阈值
		self.__new_dimension_diff_threshold = 0.4  # 新架构维度差异阈值

		# 性能监控
		self.__calculation_count = 0
		self.__total_calculation_time = 0.0
		self.__performance_lock = threading.Lock()

		# 初始化L1快速筛选模块 (新架构)
		self.__initialize_quick_filter_module()

		# 初始化L2重叠度计算器 (新架构)
		self.__initialize_overlap_calculator()

		# 初始化L2空间位置计算器 (新架构)
		self.__initialize_spatial_calculator()

		# 初始化L2形态特征计算器 (新架构)
		self.__initialize_morphology_calculator()

		# 初始化L3自适应融合模块 (新架构)
		self.__initialize_adaptive_fusion()

		# 初始化新决策引擎 (新架构)
		self.__initialize_decision_engine()

		self.__logger.info( f"坐标相似度处理器初始化完成 - 模式: {mode}" )
		if self.__log_output:
			self.__log_output.append( f"坐标相似度处理器初始化完成 - 模式: {mode}", Colors.GREEN )

	def compare_coordinates(
			self,
			points1: List[ List[ float ] ],
			points2: List[ List[ float ] ],
			similarity_threshold: Optional[ float ] = 0.30
	) -> Dict[ str, Any ]:
		"""
		比较两个坐标集合的相似度 (新架构 - 向后兼容)

		新架构计算流程:
		L1: 快速筛选层 - 高效筛选明显不相似的坐标对
		L2: 精细计算层 - 三个独立模块(重叠度、空间位置、形态特征)
		L3: 自适应融合层 - 注意力机制动态权重分配
		决策引擎: 智能决策机制 (支持用户阈值和多层决策)

		错误处理:
		如果新架构任何一层计算失败，返回保守的追加决策，确保系统稳定性

		参数:
			points1: 第一个坐标集合 [[x1,y1], [x2,y2], ...]
			points2: 第二个坐标集合 [[x1,y1], [x2,y2], ...]
			similarity_threshold: 相似度阈值，默认0.30
			                     - 数值 (0.0-1.0): 只有综合相似度低于此阈值才追加标注数据
			                     - None: 使用默认的智能决策，不应用用户阈值检查

		返回:
			包含相似度详情和决策结果的字典 (保持向后兼容格式)
			{
				'geometric_similarity': float,      # 重叠度相似度 (新架构L2)
				'position_similarity': float,       # 空间位置相似度 (新架构L2)
				'scale_similarity': float,          # 尺度相似度 (形态特征的一部分)
				'shape_similarity': float,          # 形状相似度 (形态特征的一部分)
				'comprehensive_similarity': float,  # 自适应融合相似度 (新架构L3)
				'decision': str,                    # 决策结果 ('add'/'skip')
				'reason': str,                      # 决策原因说明
				'processing_time': float,           # 处理时间(秒)
				'layer_decision': str,              # 决策层级说明
				'details': dict                     # 详细计算信息 (新架构扩展)
			}

		新架构扩展信息 (details字段):
			{
				'new_architecture': bool,           # 是否成功使用新架构
				'l2_calculations': dict,            # L2层各模块计算详情
				'l3_fusion': dict,                  # L3层融合详情
				'decision_engine': dict,            # 决策引擎详情
				'fallback_reason': str              # 降级原因(如果适用)
			}

		使用示例:
			# 基本使用 (与旧版本完全兼容)
			result = processor.compare_coordinates(
				points1=[[100,100], [200,100], [200,200], [100,200]],
				points2=[[105,105], [205,105], [205,205], [105,205]]
			)

			# 查看各维度相似度 (接口保持不变)
			print(f"几何相似度: {result['geometric_similarity']:.3f}")
			print(f"位置相似度: {result['position_similarity']:.3f}")
			print(f"综合相似度: {result['comprehensive_similarity']:.3f}")

			# 检查是否使用了新架构
			if result['details'].get('new_architecture', False):
				print("使用了新架构计算")
				fusion_weights = result['details']['l3_fusion']['fusion_weights']
				print(f"融合权重: {fusion_weights}")
			else:
				print(f"使用了降级方案: {result['details'].get('fallback_reason', '未知')}")
		"""
		# 参数验证 (在try块外，确保异常能正确抛出)
		if similarity_threshold is not None and not 0.0 <= similarity_threshold <= 1.0:
			raise ValueError( f"similarity_threshold必须在0.0-1.0范围内，当前值: {similarity_threshold}" )

		start_time = time.time()

		try:

			# 生成缓存键
			cache_key = self.__generate_cache_key( points1, points2, similarity_threshold )

			# 检查缓存
			with self.__cache_lock:
				if cache_key in self.__calculation_cache:
					cached_result = self.__calculation_cache[ cache_key ].copy()
					cached_result[ 'processing_time' ] = time.time() - start_time
					cached_result[ 'from_cache' ] = True
					return cached_result

			# L1: 快速筛选层 (新架构)
			quick_check_result = self.__quick_exclusion_check( points1, points2 )
			if quick_check_result:
				processing_time = time.time() - start_time
				result = self.__create_result(
					decision=quick_check_result[ 'decision' ],
					reason=quick_check_result[ 'reason' ],
					processing_time=processing_time,
					layer_decision=quick_check_result[ 'reason' ]
				)
				self.__cache_result( cache_key, result )
				return result

			# 坐标预处理和标准化
			processed_points1 = self.__preprocess_coordinates( points1 )
			processed_points2 = self.__preprocess_coordinates( points2 )

			if processed_points1 is None or processed_points2 is None:
				processing_time = time.time() - start_time
				result = self.__create_result(
					decision='add',
					reason='坐标预处理失败，默认追加',
					processing_time=processing_time,
					layer_decision='坐标预处理阶段'
				)
				return result

			# L2: 精细计算层 - 三个独立模块计算 (新架构)
			try:
				self.__logger.debug( "开始L2精细计算层 - 三个独立模块计算" )

				# L2.1: 重叠度相似度计算
				try:
					overlap_result = self.__overlap_calculator.calculate( processed_points1, processed_points2 )
					self.__logger.debug( f"L2.1重叠度计算完成: overall={overlap_result.get( 'overall', 0.0 ):.3f}" )
				except Exception as e:
					self.__logger.warning( f"L2.1重叠度计算失败: {str( e )}" )
					raise

				# L2.2: 空间位置相似度计算
				try:
					spatial_result = self.__spatial_calculator.calculate( processed_points1, processed_points2 )
					self.__logger.debug( f"L2.2空间位置计算完成: overall={spatial_result.get( 'overall', 0.0 ):.3f}" )
				except Exception as e:
					self.__logger.warning( f"L2.2空间位置计算失败: {str( e )}" )
					raise

				# L2.3: 形态特征相似度计算
				try:
					morphology_result = self.__morphology_calculator.calculate( processed_points1, processed_points2 )
					self.__logger.debug( f"L2.3形态特征计算完成: overall={morphology_result.get( 'overall', 0.0 ):.3f}" )
				except Exception as e:
					self.__logger.warning( f"L2.3形态特征计算失败: {str( e )}" )
					raise

				# 验证L2计算结果的完整性
				if not all(
						key in result and 'overall' in result for key, result in
						[ ('overlap', overlap_result), ('spatial', spatial_result), ('morphology', morphology_result) ]
				):
					raise ValueError( "L2计算器返回结果格式不完整" )

				# 构建L2计算结果
				l2_similarities = {
					'overlap': overlap_result,
					'spatial': spatial_result,
					'morphology': morphology_result
				}

				self.__logger.debug( "L2精细计算层完成，所有模块计算成功" )

				# L3: 自适应融合层 - 注意力机制动态权重分配 (新架构)
				try:
					self.__logger.debug( "开始L3自适应融合层 - 注意力机制动态权重分配" )
					fusion_result = self.__adaptive_fusion.fuse( l2_similarities, processed_points1, processed_points2 )

					# 验证融合结果的完整性
					required_keys = [ 'similarity', 'confidence', 'weights', 'components', 'fusion_reason' ]
					if not all( key in fusion_result for key in required_keys ):
						raise ValueError( f"L3融合结果格式不完整，缺少必要字段: {required_keys}" )

					# 验证融合相似度和置信度的有效性
					similarity = fusion_result[ 'similarity' ]
					confidence = fusion_result[ 'confidence' ]
					if not (0.0 <= similarity <= 1.0) or not (0.0 <= confidence <= 1.0):
						raise ValueError( f"L3融合结果数值无效: similarity={similarity}, confidence={confidence}" )

					self.__logger.debug( f"L3自适应融合完成: similarity={similarity:.3f}, confidence={confidence:.3f}" )
					self.__logger.debug( f"融合权重: {fusion_result[ 'weights' ]}" )

				except Exception as e:
					self.__logger.warning( f"L3自适应融合失败: {str( e )}" )
					raise

				# 决策引擎: 智能决策机制 (新架构)
				try:
					self.__logger.debug( "开始决策引擎 - 智能决策机制" )
					decision_result = self.__decision_engine.decide( fusion_result, similarity_threshold )

					# 验证决策结果的完整性
					required_decision_keys = [ 'action', 'reason', 'confidence', 'details' ]
					if not all( key in decision_result for key in required_decision_keys ):
						raise ValueError( f"决策引擎结果格式不完整，缺少必要字段: {required_decision_keys}" )

					# 验证决策动作的有效性
					if decision_result[ 'action' ] not in [ 'add', 'skip' ]:
						raise ValueError( f"决策引擎返回无效动作: {decision_result[ 'action' ]}" )

					self.__logger.debug( f"决策引擎完成: action={decision_result[ 'action' ]}, reason={decision_result[ 'reason' ]}" )

				except Exception as e:
					self.__logger.warning( f"决策引擎失败: {str( e )}" )
					raise

				# 向后兼容性映射 - 将新架构结果映射到旧接口格式
				geometric_sim = overlap_result[ 'overall' ]  # 重叠度 -> 几何相似度
				position_sim = spatial_result[ 'overall' ]  # 空间位置 -> 位置相似度
				scale_sim = morphology_result[ 'scale' ]  # 形态特征中的尺度相似度
				shape_sim = morphology_result[ 'shape' ]  # 形态特征中的形状相似度
				comprehensive_sim = fusion_result[ 'similarity' ]  # 自适应融合结果

				# 决策结果映射
				decision = decision_result[ 'action' ]
				reason = decision_result[ 'reason' ]
				layer_info = decision_result[ 'details' ].get( 'decision_layer', '新架构决策' )

			except Exception as e:
				# L2/L3/决策引擎失败时的错误处理：返回保守的追加决策
				self.__logger.warning( f"新架构计算失败，返回保守追加决策: {str( e )}" )

				# 设置保守的默认值
				geometric_sim = 0.0
				position_sim = 0.0
				scale_sim = 0.0
				shape_sim = 0.0
				comprehensive_sim = 0.0

				# 保守决策逻辑：计算失败时总是追加
				decision = 'add'
				reason = f'新架构计算失败，保守追加: {str( e )}'
				layer_info = '错误处理 - 保守追加'

			processing_time = time.time() - start_time

			# 更新性能统计
			with self.__performance_lock:
				self.__calculation_count += 1
				self.__total_calculation_time += processing_time

			# 构建详细信息 - 根据是否使用新架构决定内容
			details = {
				'mode': self.__mode,
				'user_threshold': similarity_threshold,
				'decision_layer': layer_info
			}

			# 检查是否成功使用了新架构
			try:
				# 如果新架构变量存在，说明使用了新架构
				if 'l2_similarities' in locals() and 'fusion_result' in locals() and 'decision_result' in locals():
					details.update(
						{
							'new_architecture': True,  # 标识使用新架构
							'architecture_version': '2.0.0',  # 新架构版本号
							'computation_flow': 'L1_QuickFilter -> L2_PreciseCalculation -> L3_AdaptiveFusion -> DecisionEngine',
							'l1_quick_filter': {  # L1层快速筛选详情
								'passed': True,  # 通过了快速筛选
								'filter_reason': '通过快速筛选，进入精细计算'
							},
							'l2_calculations': {  # L2层计算详情
								'overlap_similarity': l2_similarities[ 'overlap' ],
								'spatial_similarity': l2_similarities[ 'spatial' ],
								'morphology_similarity': l2_similarities[ 'morphology' ],
								'calculation_success': True,
								'individual_scores': {
									'overlap_overall': l2_similarities[ 'overlap' ][ 'overall' ],
									'spatial_overall': l2_similarities[ 'spatial' ][ 'overall' ],
									'morphology_overall': l2_similarities[ 'morphology' ][ 'overall' ]
								}
							},
							'l3_fusion': {  # L3层融合详情
								'fusion_similarity': fusion_result[ 'similarity' ],
								'fusion_confidence': fusion_result[ 'confidence' ],
								'fusion_weights': fusion_result[ 'weights' ],
								'fusion_reason': fusion_result[ 'fusion_reason' ],
								'fusion_strategy': 'attention_mechanism',
								'components_used': list( fusion_result[ 'weights' ].keys() )
							},
							'decision_engine': {  # 决策引擎详情
								'decision_confidence': decision_result[ 'confidence' ],
								'decision_details': decision_result[ 'details' ],
								'engine_version': 'simplified_three_layer',
								'user_threshold_applied': similarity_threshold is not None,
								'user_threshold_value': similarity_threshold
							},
							'performance_metrics': {  # 性能指标
								'total_processing_time': processing_time,
								'architecture_overhead': 'minimal',  # 新架构相对于降级方案的开销
								'cache_used': False
							}
						}
					)
				else:
					# 使用了错误处理方案
					details.update(
						{
							'new_architecture': False,  # 标识新架构计算失败
							'architecture_version': '2.0.0',  # 仍然是新架构版本，但计算失败
							'computation_flow': 'L1_QuickFilter -> L2/L3/DecisionEngine_Failed -> ErrorHandling',
							'error_reason': '新架构计算失败，返回保守追加决策',
							'error_details': {
								'error_trigger': 'L2/L3/DecisionEngine_Exception',
								'error_strategy': 'conservative_add',
								'error_handling_success': True
							},
							'conservative_mode': True,  # 保守模式
							'conservative_details': {
								'all_similarities_zero': True,
								'decision_forced_add': True,
								'reason': '计算失败时的保守策略'
							},
							'decision_logic': {
								'decision_type': 'conservative_error_handling',
								'forced_decision': 'add',
								'user_threshold_applied': False,  # 错误时不应用用户阈值
								'user_threshold_value': similarity_threshold
							},
							'performance_metrics': {  # 性能指标
								'total_processing_time': processing_time,
								'error_overhead': 'minimal',
								'cache_used': False
							}
						}
					)
			except Exception:
				# 详细信息构建失败时的最小信息
				details.update(
					{
						'new_architecture': False,
						'fallback_reason': '详细信息构建失败'
					}
				)

			# 向后兼容性验证 - 确保所有相似度值在[0,1]范围内
			geometric_sim = max( 0.0, min( 1.0, geometric_sim ) )
			position_sim = max( 0.0, min( 1.0, position_sim ) )
			scale_sim = max( 0.0, min( 1.0, scale_sim ) )
			shape_sim = max( 0.0, min( 1.0, shape_sim ) )
			comprehensive_sim = max( 0.0, min( 1.0, comprehensive_sim ) )

			# 验证决策字段的有效性
			if decision not in [ 'add', 'skip' ]:
				self.__logger.warning( f"无效的决策值: {decision}，重置为'add'" )
				decision = 'add'
				reason = f"决策值无效，重置为追加: {reason}"

			result = {
				'geometric_similarity': float( geometric_sim ),
				'position_similarity': float( position_sim ),
				'scale_similarity': float( scale_sim ),
				'shape_similarity': float( shape_sim ),
				'comprehensive_similarity': float( comprehensive_sim ),
				'decision': decision,
				'reason': reason,
				'processing_time': float( processing_time ),
				'layer_decision': layer_info,
				'details': details,
				'from_cache': False
			}

			# 记录计算结果日志 - 区分新架构和错误处理
			architecture_info = "新架构" if details.get( 'new_architecture', False ) else "错误处理"
			self.__logger.debug(
				f"坐标相似度计算完成({architecture_info}): 几何={geometric_sim:.3f}, 位置={position_sim:.3f}, "
				f"尺度={scale_sim:.3f}, 形状={shape_sim:.3f}, 综合={comprehensive_sim:.3f}, "
				f"决策={decision}, 耗时={processing_time:.3f}s"
			)

			# 缓存结果
			self.__cache_result( cache_key, result )

			return result

		except Exception as e:
			processing_time = time.time() - start_time
			error_msg = f"坐标相似度计算失败: {str( e )}"
			self.__logger.error( error_msg )
			return self.__create_result(
				decision='add',
				reason=error_msg,
				processing_time=processing_time,
				layer_decision='异常处理'
			)

	def __generate_cache_key( self, points1: List[ List[ float ] ], points2: List[ List[ float ] ], similarity_threshold: Optional[ float ] ) -> str:
		"""生成缓存键"""
		import hashlib
		# 将坐标点排序后生成唯一键
		sorted_points1 = sorted( [ tuple( p ) for p in points1 ] )
		sorted_points2 = sorted( [ tuple( p ) for p in points2 ] )
		threshold_str = "None" if similarity_threshold is None else str( similarity_threshold )
		content = f"{sorted_points1}:{sorted_points2}:{self.__mode}:{threshold_str}"
		return hashlib.md5( content.encode() ).hexdigest()

	def __cache_result( self, cache_key: str, result: Dict[ str, Any ] ) -> None:
		"""缓存计算结果"""
		with self.__cache_lock:
			if len( self.__calculation_cache ) >= self.__max_cache_size:
				# 简单的LRU：删除第一个元素
				first_key = next( iter( self.__calculation_cache ) )
				del self.__calculation_cache[ first_key ]

			# 缓存时移除不需要缓存的字段
			cache_result = result.copy()
			cache_result.pop( 'processing_time', None )
			cache_result.pop( 'from_cache', None )
			self.__calculation_cache[ cache_key ] = cache_result

	def __create_result(
			self, decision: str, reason: str, processing_time: float = 0.0,
			layer_decision: str = "", **kwargs
	) -> Dict[ str, Any ]:
		"""创建标准化的结果字典"""
		result = {
			'geometric_similarity': 0.0,
			'position_similarity': 0.0,
			'scale_similarity': 0.0,
			'shape_similarity': 0.0,
			'comprehensive_similarity': 0.0,
			'decision': decision,
			'reason': reason,
			'processing_time': processing_time,
			'layer_decision': layer_decision,
			'details': { },
			'from_cache': False
		}
		result.update( kwargs )
		return result

	def __quick_exclusion_check(
			self, points1: List[ List[ float ] ],
			points2: List[ List[ float ] ]
	) -> Optional[ Dict[ str, str ] ]:
		"""
		L1: 快速筛选层 (新架构)

		优化的快速筛选逻辑：
		1. 基础验证 - 坐标有效性检查
		2. 几何特征提取 - O(1)时间复杂度的特征计算
		3. 多维度快速筛选 - 面积比、边界框重叠、中心距离、长宽比
		4. 详细筛选原因 - 提供可解释的筛选依据

		返回:
			如果需要快速排除，返回决策字典；否则返回None继续后续计算
		"""
		try:
			# 调用新的快速筛选模块
			should_proceed, filter_reason = self.__quick_filter_module.should_proceed( points1, points2 )

			if not should_proceed:
				return {
					'decision': 'add',
					'reason': f'L1快速筛选: {filter_reason}'
				}

			# 通过快速筛选，继续后续计算
			return None

		except Exception as e:
			self.__logger.warning( f"快速筛选失败: {str( e )}" )
			return None

	def __initialize_quick_filter_module( self ):
		"""初始化快速筛选模块"""
		filter_config = {
			'min_area_ratio': 0.01,  # 最小面积比1%
			'max_area_ratio': 100.0,  # 最大面积比100:1
			'max_center_distance_ratio': 3.0,  # 最大中心距离比
			'min_bbox_overlap': 0.0,  # 最小边界框重叠
			'min_points': 3,  # 最小点数
			'max_aspect_ratio_diff': 10.0  # 最大长宽比差异
		}
		self.__quick_filter_module = self.QuickFilterModule( filter_config )

	def __initialize_overlap_calculator( self ):
		"""初始化重叠度计算器 (L2新架构)"""
		overlap_config = {
			'use_shapely': True,  # 优先使用Shapely
			'min_area_threshold': 1e-6,  # 最小面积阈值
			'iou_weight': 0.4,  # IoU权重40%
			'ios_weight': 0.3,  # IoS权重30%
			'containment_weight': 0.2,  # 包含关系权重20%
			'overlap_ratio_weight': 0.1  # 重叠面积比权重10%
		}
		self.__overlap_calculator = self.OverlapSimilarityCalculator( overlap_config, self.__logger )

	def __initialize_spatial_calculator( self ):
		"""初始化空间位置计算器 (L2新架构)"""
		spatial_config = {
			'distance_factor': 0.1,  # 距离因子
			'max_distance_threshold': 100.0,  # 最大距离阈值
			'min_distance_threshold': 5.0,  # 最小距离阈值
			'absolute_weight': 0.4,  # 绝对位置权重40%
			'relative_weight': 0.3,  # 相对位置权重30%
			'directional_weight': 0.2,  # 方向相似度权重20%
			'scale_aware_weight': 0.1  # 尺度感知权重10%
		}
		self.__spatial_calculator = self.SpatialSimilarityCalculator( spatial_config, self.__logger )

	def __initialize_morphology_calculator( self ):
		"""初始化形态特征计算器 (L2新架构)"""
		morphology_config = {
			'hausdorff_weight': 0.3,  # Hausdorff距离权重30%
			'scale_weight': 0.3,  # 尺度相似度权重30%
			'complexity_weight': 0.2,  # 复杂度相似度权重20%
			'symmetry_weight': 0.2,  # 对称性相似度权重20%
			'min_area_threshold': 1e-6,  # 最小面积阈值
			'log_threshold': 2.0  # 对数阈值
		}
		self.__morphology_calculator = self.MorphologySimilarityCalculator( morphology_config, self.__logger )

	def __initialize_adaptive_fusion( self ):
		"""初始化自适应融合模块 (L3新架构)"""
		fusion_config = {
			'fusion_strategy': 'attention',  # 注意力机制融合
			'confidence_threshold': 0.8,  # 置信度阈值
			'min_weight': 0.1,  # 最小权重限制10%
			'max_weight': 0.8,  # 最大权重限制80%
			'stability_factor': 0.1  # 稳定性因子
		}
		self.__adaptive_fusion = self.AdaptiveFusionModule( fusion_config, self.__logger )

	def __initialize_decision_engine( self ):
		"""初始化决策引擎 (新架构)"""
		decision_config = {
			'high_threshold': 0.75,  # 高相似度阈值75%
			'low_threshold': 0.25,  # 低相似度阈值25%
			'confidence_threshold': 0.6,  # 置信度阈值60%
			'medium_threshold_upper': 0.6,  # 中等相似度上限60%
			'medium_threshold_lower': 0.4  # 中等相似度下限40%
		}
		self.__decision_engine = self.DecisionEngine( decision_config, self.__logger )

	class QuickFilterModule:
		"""
		L1: 快速筛选模块

		目标：在O(1)时间内快速排除明显不相似的坐标对
		特点：高效、准确、可解释
		"""

		def __init__( self, config: dict ):
			# 筛选阈值配置
			self.min_area_ratio = config.get( 'min_area_ratio', 0.01 )
			self.max_area_ratio = config.get( 'max_area_ratio', 100.0 )
			self.max_center_distance_ratio = config.get( 'max_center_distance_ratio', 3.0 )
			self.min_bbox_overlap = config.get( 'min_bbox_overlap', 0.0 )
			self.min_points = config.get( 'min_points', 3 )
			self.max_aspect_ratio_diff = config.get( 'max_aspect_ratio_diff', 10.0 )

		def should_proceed( self, points1: List[ List[ float ] ], points2: List[ List[ float ] ] ) -> tuple:
			"""
			快速筛选判断

			返回:
				(是否继续处理, 筛选原因)
			"""
			# 1. 基础验证
			validation_result = self._basic_validation( points1, points2 )
			if not validation_result[ 0 ]:
				return validation_result

			# 2. 提取基础特征 (O(1)复杂度)
			try:
				features1 = self._extract_basic_features( points1 )
				features2 = self._extract_basic_features( points2 )
			except Exception as e:
				return False, f"特征提取失败: {str( e )}"

			# 3. 面积比例检查
			if features1[ 'area' ] > 0 and features2[ 'area' ] > 0:
				area_ratio = min( features1[ 'area' ], features2[ 'area' ] ) / max( features1[ 'area' ], features2[ 'area' ] )
				if area_ratio < self.min_area_ratio:
					return False, f"面积差异过大: 比例={area_ratio:.4f} < {self.min_area_ratio}"

			# 4. 边界框重叠检查
			bbox_overlap = self._calculate_bbox_overlap( features1[ 'bbox' ], features2[ 'bbox' ] )
			if bbox_overlap < self.min_bbox_overlap:
				return False, f"边界框无重叠: 重叠度={bbox_overlap:.3f}"

			# 5. 中心距离检查
			center_distance = self._calculate_center_distance( features1[ 'center' ], features2[ 'center' ] )
			avg_diagonal = (features1[ 'diagonal' ] + features2[ 'diagonal' ]) / 2
			if avg_diagonal > 0:
				distance_ratio = center_distance / avg_diagonal
				if distance_ratio > self.max_center_distance_ratio:
					return False, f"中心距离过远: 距离比例={distance_ratio:.3f} > {self.max_center_distance_ratio}"

			# 6. 长宽比差异检查
			aspect_ratio_diff = abs( features1[ 'aspect_ratio' ] - features2[ 'aspect_ratio' ] )
			if aspect_ratio_diff > self.max_aspect_ratio_diff:
				return False, f"长宽比差异过大: 差异={aspect_ratio_diff:.3f} > {self.max_aspect_ratio_diff}"

			return True, "通过快速筛选"

		def _basic_validation( self, points1, points2 ) -> tuple:
			"""基础验证检查 (支持numpy数组)"""
			# 转换为numpy数组以统一处理
			try:
				import numpy as np
				if not isinstance( points1, np.ndarray ):
					points1 = np.array( points1 )
				if not isinstance( points2, np.ndarray ):
					points2 = np.array( points2 )
			except Exception:
				return False, "坐标转换失败"

			# 1. 空坐标检查
			if len( points1 ) == 0 or len( points2 ) == 0:
				return False, "存在空坐标"

			# 2. 点数检查
			if len( points1 ) < self.min_points or len( points2 ) < self.min_points:
				return False, f"坐标点不足{self.min_points}个"

			# 3. 格式检查
			try:
				# 检查维度
				if points1.ndim != 2 or points2.ndim != 2:
					return False, "坐标维度错误"
				if points1.shape[ 1 ] < 2 or points2.shape[ 1 ] < 2:
					return False, "坐标维度不足"

				# 检查数据类型
				if not np.issubdtype( points1.dtype, np.number ) or not np.issubdtype( points2.dtype, np.number ):
					return False, "坐标数据类型错误"

			except Exception:
				return False, "坐标格式验证失败"

			return True, "基础验证通过"

		def _extract_basic_features( self, points ) -> dict:
			"""提取基础几何特征 (O(1)复杂度，支持numpy数组)"""
			try:
				import numpy as np
				if not isinstance( points, np.ndarray ):
					points = np.array( points )

				# 计算边界框
				xs = points[ :, 0 ]
				ys = points[ :, 1 ]
				bbox = {
					'min_x': float( np.min( xs ) ), 'max_x': float( np.max( xs ) ),
					'min_y': float( np.min( ys ) ), 'max_y': float( np.max( ys ) )
				}

				# 计算基础特征
				width = bbox[ 'max_x' ] - bbox[ 'min_x' ]
				height = bbox[ 'max_y' ] - bbox[ 'min_y' ]
				area = self._calculate_polygon_area_fast( points )
				center = ((bbox[ 'min_x' ] + bbox[ 'max_x' ]) / 2, (bbox[ 'min_y' ] + bbox[ 'max_y' ]) / 2)
				diagonal = (width ** 2 + height ** 2) ** 0.5

				return {
					'bbox': bbox,
					'area': area,
					'center': center,
					'diagonal': diagonal,
					'width': width,
					'height': height,
					'aspect_ratio': width / height if height > 0 else float( 'inf' )
				}
			except Exception:
				# 降级处理
				return {
					'bbox': { 'min_x': 0, 'max_x': 0, 'min_y': 0, 'max_y': 0 },
					'area': 0.0, 'center': (0, 0), 'diagonal': 0.0,
					'width': 0.0, 'height': 0.0, 'aspect_ratio': 1.0
				}

		def _calculate_polygon_area_fast( self, points ) -> float:
			"""快速多边形面积计算 (鞋带公式，支持numpy数组)"""
			try:
				import numpy as np
				if not isinstance( points, np.ndarray ):
					points = np.array( points )

				if len( points ) < 3:
					return 0.0

				area = 0.0
				n = len( points )
				for i in range( n ):
					j = (i + 1) % n
					area += points[ i ][ 0 ] * points[ j ][ 1 ]
					area -= points[ j ][ 0 ] * points[ i ][ 1 ]
				return abs( area ) / 2.0
			except Exception:
				return 0.0

		def _calculate_bbox_overlap( self, bbox1: dict, bbox2: dict ) -> float:
			"""计算边界框重叠度"""
			# 计算重叠区域
			overlap_left = max( bbox1[ 'min_x' ], bbox2[ 'min_x' ] )
			overlap_right = min( bbox1[ 'max_x' ], bbox2[ 'max_x' ] )
			overlap_top = max( bbox1[ 'min_y' ], bbox2[ 'min_y' ] )
			overlap_bottom = min( bbox1[ 'max_y' ], bbox2[ 'max_y' ] )

			# 检查是否有重叠
			if overlap_left >= overlap_right or overlap_top >= overlap_bottom:
				return 0.0

			# 计算重叠面积
			overlap_area = (overlap_right - overlap_left) * (overlap_bottom - overlap_top)

			# 计算边界框面积
			area1 = (bbox1[ 'max_x' ] - bbox1[ 'min_x' ]) * (bbox1[ 'max_y' ] - bbox1[ 'min_y' ])
			area2 = (bbox2[ 'max_x' ] - bbox2[ 'min_x' ]) * (bbox2[ 'max_y' ] - bbox2[ 'min_y' ])

			# 计算重叠比例 (相对于较小的边界框)
			min_area = min( area1, area2 )
			return overlap_area / min_area if min_area > 0 else 0.0

		def _calculate_center_distance( self, center1: tuple, center2: tuple ) -> float:
			"""计算中心距离"""
			return ((center1[ 0 ] - center2[ 0 ]) ** 2 + (center1[ 1 ] - center2[ 1 ]) ** 2) ** 0.5

	class OverlapSimilarityCalculator:
		"""
		L2: 重叠度相似度计算器 (新架构)

		专注：几何重叠度分析，不涉及位置和形态
		算法：IoU、IoS、包含关系、重叠面积比
		特点：独立计算、精确分析、可降级
		"""

		def __init__( self, config: dict, logger=None ):
			self.use_shapely = config.get( 'use_shapely', True )
			self.min_area_threshold = config.get( 'min_area_threshold', 1e-6 )
			self.iou_weight = config.get( 'iou_weight', 0.4 )
			self.ios_weight = config.get( 'ios_weight', 0.3 )
			self.containment_weight = config.get( 'containment_weight', 0.2 )
			self.overlap_ratio_weight = config.get( 'overlap_ratio_weight', 0.1 )
			self.logger = logger

		def calculate( self, points1: np.ndarray, points2: np.ndarray ) -> dict:
			"""
			计算重叠度相似度

			返回:
				{
					'iou': float,           # IoU相似度
					'ios': float,           # IoS相似度
					'containment': float,   # 包含关系
					'overlap_ratio': float, # 重叠面积比
					'overall': float        # 综合重叠度
				}
			"""
			try:
				if self.use_shapely:
					return self._calculate_with_shapely( points1, points2 )
				else:
					return self._calculate_fallback( points1, points2 )
			except Exception as e:
				if self.logger:
					self.logger.warning( f"重叠度计算失败，使用降级方案: {str( e )}" )
				# 降级到简单计算
				return self._calculate_fallback( points1, points2 )

		def _calculate_with_shapely( self, points1: np.ndarray, points2: np.ndarray ) -> dict:
			"""使用Shapely进行精确重叠度计算"""
			try:
				from shapely.geometry import Polygon
				from shapely.validation import make_valid

				# 创建和验证多边形
				poly1 = make_valid( Polygon( points1 ) )
				poly2 = make_valid( Polygon( points2 ) )

				# 如果修复后仍然无效，使用buffer方法
				if not poly1.is_valid:
					poly1 = poly1.buffer( 0 )
				if not poly2.is_valid:
					poly2 = poly2.buffer( 0 )

				# 计算面积
				area1 = poly1.area
				area2 = poly2.area

				if area1 < self.min_area_threshold or area2 < self.min_area_threshold:
					return self._zero_result()

				# 计算交集和并集
				intersection = poly1.intersection( poly2 )
				union = poly1.union( poly2 )

				intersection_area = intersection.area if hasattr( intersection, 'area' ) else 0.0
				union_area = union.area if hasattr( union, 'area' ) else (area1 + area2)

				# 计算各种重叠度指标
				iou = intersection_area / union_area if union_area > 0 else 0.0
				ios = intersection_area / min( area1, area2 ) if min( area1, area2 ) > 0 else 0.0

				# 包含关系分析
				containment = self._analyze_containment( area1, area2, intersection_area )

				# 重叠面积比
				overlap_ratio = intersection_area / max( area1, area2 ) if max( area1, area2 ) > 0 else 0.0

				# 综合重叠度 (权重可配置)
				overall = (self.iou_weight * iou +
				           self.ios_weight * ios +
				           self.containment_weight * containment +
				           self.overlap_ratio_weight * overlap_ratio)

				return {
					'iou': float( max( 0.0, min( 1.0, iou ) ) ),
					'ios': float( max( 0.0, min( 1.0, ios ) ) ),
					'containment': float( max( 0.0, min( 1.0, containment ) ) ),
					'overlap_ratio': float( max( 0.0, min( 1.0, overlap_ratio ) ) ),
					'overall': float( max( 0.0, min( 1.0, overall ) ) )
				}

			except ImportError:
				if self.logger:
					self.logger.warning( "Shapely库不可用，使用降级方案" )
				return self._calculate_fallback( points1, points2 )
			except Exception as e:
				if self.logger:
					self.logger.warning( f"Shapely计算失败: {str( e )}" )
				return self._calculate_fallback( points1, points2 )

		def _calculate_fallback( self, points1: np.ndarray, points2: np.ndarray ) -> dict:
			"""降级方案：使用简单几何计算"""
			try:
				# 计算边界框
				bbox1 = self._calculate_bbox( points1 )
				bbox2 = self._calculate_bbox( points2 )

				# 计算面积
				area1 = self._calculate_polygon_area( points1 )
				area2 = self._calculate_polygon_area( points2 )

				if area1 < self.min_area_threshold or area2 < self.min_area_threshold:
					return self._zero_result()

				# 计算边界框重叠
				bbox_overlap_area = self._calculate_bbox_overlap_area( bbox1, bbox2 )

				# 估算重叠度指标
				union_area = area1 + area2 - bbox_overlap_area
				iou = bbox_overlap_area / union_area if union_area > 0 else 0.0
				ios = bbox_overlap_area / min( area1, area2 ) if min( area1, area2 ) > 0 else 0.0

				# 简化的包含关系
				containment = self._analyze_containment( area1, area2, bbox_overlap_area )

				# 重叠面积比
				overlap_ratio = bbox_overlap_area / max( area1, area2 ) if max( area1, area2 ) > 0 else 0.0

				# 综合重叠度
				overall = (self.iou_weight * iou +
				           self.ios_weight * ios +
				           self.containment_weight * containment +
				           self.overlap_ratio_weight * overlap_ratio)

				return {
					'iou': float( max( 0.0, min( 1.0, iou ) ) ),
					'ios': float( max( 0.0, min( 1.0, ios ) ) ),
					'containment': float( max( 0.0, min( 1.0, containment ) ) ),
					'overlap_ratio': float( max( 0.0, min( 1.0, overlap_ratio ) ) ),
					'overall': float( max( 0.0, min( 1.0, overall ) ) )
				}

			except Exception as e:
				if self.logger:
					self.logger.warning( f"降级计算失败: {str( e )}" )
				return self._zero_result()

		def _analyze_containment( self, area1: float, area2: float, intersection_area: float ) -> float:
			"""分析包含关系"""
			if intersection_area <= 0:
				return 0.0  # 完全分离

			# 计算包含比例
			contain_ratio1 = intersection_area / area1 if area1 > 0 else 0.0
			contain_ratio2 = intersection_area / area2 if area2 > 0 else 0.0
			max_contain_ratio = max( contain_ratio1, contain_ratio2 )

			# 根据包含比例确定得分
			if max_contain_ratio >= 0.95:
				return 1.0  # 几乎完全包含
			elif max_contain_ratio >= 0.80:
				return 0.8  # 高度包含
			elif max_contain_ratio >= 0.60:
				return 0.6  # 中度包含
			elif max_contain_ratio >= 0.30:
				return 0.4  # 低度包含
			else:
				return 0.2  # 轻微重叠

		def _calculate_bbox( self, points: np.ndarray ) -> dict:
			"""计算边界框"""
			xs = points[ :, 0 ]
			ys = points[ :, 1 ]
			return {
				'min_x': float( np.min( xs ) ), 'max_x': float( np.max( xs ) ),
				'min_y': float( np.min( ys ) ), 'max_y': float( np.max( ys ) )
			}

		def _calculate_polygon_area( self, points: np.ndarray ) -> float:
			"""计算多边形面积 (鞋带公式)"""
			if len( points ) < 3:
				return 0.0

			area = 0.0
			n = len( points )
			for i in range( n ):
				j = (i + 1) % n
				area += points[ i ][ 0 ] * points[ j ][ 1 ]
				area -= points[ j ][ 0 ] * points[ i ][ 1 ]
			return abs( area ) / 2.0

		def _calculate_bbox_overlap_area( self, bbox1: dict, bbox2: dict ) -> float:
			"""计算边界框重叠面积"""
			# 计算重叠区域
			overlap_left = max( bbox1[ 'min_x' ], bbox2[ 'min_x' ] )
			overlap_right = min( bbox1[ 'max_x' ], bbox2[ 'max_x' ] )
			overlap_top = max( bbox1[ 'min_y' ], bbox2[ 'min_y' ] )
			overlap_bottom = min( bbox1[ 'max_y' ], bbox2[ 'max_y' ] )

			# 检查是否有重叠
			if overlap_left >= overlap_right or overlap_top >= overlap_bottom:
				return 0.0

			# 计算重叠面积
			return (overlap_right - overlap_left) * (overlap_bottom - overlap_top)

		def _zero_result( self ) -> dict:
			"""返回零结果"""
			return {
				'iou': 0.0,
				'ios': 0.0,
				'containment': 0.0,
				'overlap_ratio': 0.0,
				'overall': 0.0
			}

	class SpatialSimilarityCalculator:
		"""
		L2: 空间位置相似度计算器 (新架构)

		专注：纯空间位置关系，不涉及重叠度和形态
		算法：多尺度位置分析、自适应距离阈值
		特点：独立计算、多维度分析、自适应阈值
		"""

		def __init__( self, config: dict, logger=None ):
			self.distance_factor = config.get( 'distance_factor', 0.1 )
			self.max_distance_threshold = config.get( 'max_distance_threshold', 100.0 )
			self.min_distance_threshold = config.get( 'min_distance_threshold', 5.0 )
			self.absolute_weight = config.get( 'absolute_weight', 0.4 )
			self.relative_weight = config.get( 'relative_weight', 0.3 )
			self.directional_weight = config.get( 'directional_weight', 0.2 )
			self.scale_aware_weight = config.get( 'scale_aware_weight', 0.1 )
			self.logger = logger

		def calculate( self, points1: np.ndarray, points2: np.ndarray ) -> dict:
			"""
			计算空间位置相似度

			返回:
				{
					'absolute': float,      # 绝对位置相似度
					'relative': float,      # 相对位置相似度
					'directional': float,   # 方向相似度
					'scale_aware': float,   # 尺度感知位置相似度
					'overall': float        # 综合位置相似度
				}
			"""
			try:
				# 计算几何中心
				center1 = self._calculate_centroid( points1 )
				center2 = self._calculate_centroid( points2 )

				# 计算特征尺度
				scale1 = self._calculate_characteristic_scale( points1 )
				scale2 = self._calculate_characteristic_scale( points2 )
				avg_scale = (scale1 + scale2) / 2

				# 计算中心距离
				center_distance = np.sqrt( (center1[ 0 ] - center2[ 0 ]) ** 2 + (center1[ 1 ] - center2[ 1 ]) ** 2 )

				# 1. 绝对位置相似度 (调整阈值使其更敏感)
				distance_threshold = max(
					min( avg_scale * self.distance_factor, self.max_distance_threshold ),
					self.min_distance_threshold
				)
				# 使用更宽松的阈值，让相近位置有更高的相似度
				absolute_sim = np.exp( -center_distance / (distance_threshold * 2.0) )

				# 2. 相对位置相似度 (归一化)
				normalized_distance = center_distance / avg_scale if avg_scale > 0 else 0
				relative_sim = np.exp( -normalized_distance * 1.5 )  # 降低衰减速度

				# 3. 方向相似度 (基于主轴方向)
				directional_sim = self._calculate_directional_similarity( points1, points2 )

				# 4. 尺度感知位置相似度 (修复计算逻辑)
				if max( scale1, scale2 ) > 0:
					scale_factor = min( scale1, scale2 ) / max( scale1, scale2 )
					# 尺度感知相似度应该结合位置和尺度信息
					scale_aware_sim = absolute_sim * (0.5 + 0.5 * scale_factor)
				else:
					scale_aware_sim = absolute_sim * 0.5

				# 综合位置相似度
				overall = (self.absolute_weight * absolute_sim +
				           self.relative_weight * relative_sim +
				           self.directional_weight * directional_sim +
				           self.scale_aware_weight * scale_aware_sim)

				return {
					'absolute': float( max( 0.0, min( 1.0, absolute_sim ) ) ),
					'relative': float( max( 0.0, min( 1.0, relative_sim ) ) ),
					'directional': float( max( 0.0, min( 1.0, directional_sim ) ) ),
					'scale_aware': float( max( 0.0, min( 1.0, scale_aware_sim ) ) ),
					'overall': float( max( 0.0, min( 1.0, overall ) ) )
				}

			except Exception as e:
				if self.logger:
					self.logger.warning( f"空间位置相似度计算失败: {str( e )}" )
				return self._zero_result()

		def _calculate_centroid( self, points: np.ndarray ) -> tuple:
			"""计算多边形质心"""
			if len( points ) < 3:
				# 对于少于3个点的情况，返回平均坐标
				return (float( np.mean( points[ :, 0 ] ) ), float( np.mean( points[ :, 1 ] ) ))

			# 使用多边形质心公式
			area = 0.0
			cx = 0.0
			cy = 0.0
			n = len( points )

			for i in range( n ):
				j = (i + 1) % n
				cross = points[ i ][ 0 ] * points[ j ][ 1 ] - points[ j ][ 0 ] * points[ i ][ 1 ]
				area += cross
				cx += (points[ i ][ 0 ] + points[ j ][ 0 ]) * cross
				cy += (points[ i ][ 1 ] + points[ j ][ 1 ]) * cross

			area = area / 2.0
			if abs( area ) < 1e-10:
				# 面积为零时，返回平均坐标
				return (float( np.mean( points[ :, 0 ] ) ), float( np.mean( points[ :, 1 ] ) ))

			cx = cx / (6.0 * area)
			cy = cy / (6.0 * area)
			return (float( cx ), float( cy ))

		def _calculate_characteristic_scale( self, points: np.ndarray ) -> float:
			"""计算特征尺度（边界框对角线长度）"""
			xs = points[ :, 0 ]
			ys = points[ :, 1 ]
			width = float( np.max( xs ) - np.min( xs ) )
			height = float( np.max( ys ) - np.min( ys ) )
			return (width ** 2 + height ** 2) ** 0.5

		def _calculate_directional_similarity( self, points1: np.ndarray, points2: np.ndarray ) -> float:
			"""计算方向相似度（基于主轴方向）"""
			try:
				# 计算主轴方向
				angle1 = self._calculate_principal_axis_angle( points1 )
				angle2 = self._calculate_principal_axis_angle( points2 )

				# 计算角度差异
				angle_diff = abs( angle1 - angle2 )
				# 处理角度周期性（0-180度）
				angle_diff = min( angle_diff, 180 - angle_diff )

				# 转换为相似度（角度差异越小，相似度越高）
				directional_sim = 1.0 - (angle_diff / 90.0)  # 归一化到0-1
				return max( 0.0, min( 1.0, directional_sim ) )

			except Exception:
				# 计算失败时返回中等相似度
				return 0.5

		def _calculate_principal_axis_angle( self, points: np.ndarray ) -> float:
			"""计算主轴角度"""
			try:
				# 计算质心
				centroid = self._calculate_centroid( points )

				# 计算协方差矩阵
				centered_points = points - np.array( centroid )
				cov_matrix = np.cov( centered_points.T )

				# 计算特征值和特征向量
				eigenvalues, eigenvectors = np.linalg.eig( cov_matrix )

				# 找到最大特征值对应的特征向量（主轴方向）
				max_eigenvalue_index = np.argmax( eigenvalues )
				principal_axis = eigenvectors[ :, max_eigenvalue_index ]

				# 计算角度（弧度转角度）
				angle = np.arctan2( principal_axis[ 1 ], principal_axis[ 0 ] ) * 180 / np.pi

				# 确保角度在0-180度范围内
				if angle < 0:
					angle += 180

				return float( angle )

			except Exception:
				# 计算失败时返回0度
				return 0.0

		def _zero_result( self ) -> dict:
			"""返回零结果"""
			return {
				'absolute': 0.0,
				'relative': 0.0,
				'directional': 0.0,
				'scale_aware': 0.0,
				'overall': 0.0
			}

	class MorphologySimilarityCalculator:
		"""
		L2: 形态特征相似度计算器 (新架构)

		专注：形状和尺度特征，不涉及位置和重叠
		算法：形状描述符、尺度分析、复杂度评估
		特点：全面分析、高精度、可扩展
		"""

		def __init__( self, config: dict, logger=None ):
			self.hausdorff_weight = config.get( 'hausdorff_weight', 0.3 )
			self.scale_weight = config.get( 'scale_weight', 0.3 )
			self.complexity_weight = config.get( 'complexity_weight', 0.2 )
			self.symmetry_weight = config.get( 'symmetry_weight', 0.2 )
			self.min_area_threshold = config.get( 'min_area_threshold', 1e-6 )
			self.log_threshold = config.get( 'log_threshold', 2.0 )
			self.logger = logger

		def calculate( self, points1: np.ndarray, points2: np.ndarray ) -> dict:
			"""
			计算形态特征相似度

			返回:
				{
					'shape': float,         # 形状相似度
					'scale': float,         # 尺度相似度
					'complexity': float,    # 复杂度相似度
					'symmetry': float,      # 对称性相似度
					'overall': float        # 综合形态相似度
				}
			"""
			try:
				# 1. 形状相似度 (Hausdorff距离 + 轮廓匹配)
				shape_sim = self._calculate_shape_similarity( points1, points2 )

				# 2. 尺度相似度 (面积、周长、边界框)
				scale_sim = self._calculate_scale_similarity( points1, points2 )

				# 3. 复杂度相似度 (凸包比、边数、曲率)
				complexity_sim = self._calculate_complexity_similarity( points1, points2 )

				# 4. 对称性相似度
				symmetry_sim = self._calculate_symmetry_similarity( points1, points2 )

				# 综合形态相似度
				overall = (self.hausdorff_weight * shape_sim +
				           self.scale_weight * scale_sim +
				           self.complexity_weight * complexity_sim +
				           self.symmetry_weight * symmetry_sim)

				return {
					'shape': float( max( 0.0, min( 1.0, shape_sim ) ) ),
					'scale': float( max( 0.0, min( 1.0, scale_sim ) ) ),
					'complexity': float( max( 0.0, min( 1.0, complexity_sim ) ) ),
					'symmetry': float( max( 0.0, min( 1.0, symmetry_sim ) ) ),
					'overall': float( max( 0.0, min( 1.0, overall ) ) )
				}

			except Exception as e:
				if self.logger:
					self.logger.warning( f"形态特征相似度计算失败: {str( e )}" )
				return self._zero_result()

		def _calculate_shape_similarity( self, points1: np.ndarray, points2: np.ndarray ) -> float:
			"""计算形状相似度（基于Hausdorff距离）"""
			try:
				# 使用简化的Hausdorff距离计算
				hausdorff_dist = self._calculate_hausdorff_distance( points1, points2 )

				# 计算特征尺度用于归一化
				scale1 = self._calculate_characteristic_scale( points1 )
				scale2 = self._calculate_characteristic_scale( points2 )
				avg_scale = (scale1 + scale2) / 2

				if avg_scale > 0:
					# 归一化Hausdorff距离
					normalized_hausdorff = hausdorff_dist / avg_scale
					# 转换为相似度 (降低衰减速度，提高相似形状的得分)
					shape_similarity = np.exp( -normalized_hausdorff * 0.5 )
				else:
					shape_similarity = 0.0

				return max( 0.0, min( 1.0, shape_similarity ) )

			except Exception:
				return 0.5  # 计算失败时返回中等相似度

		def _calculate_scale_similarity( self, points1: np.ndarray, points2: np.ndarray ) -> float:
			"""计算尺度相似度（面积、周长、边界框）"""
			try:
				# 计算基本几何特征
				area1 = self._calculate_polygon_area( points1 )
				area2 = self._calculate_polygon_area( points2 )
				perimeter1 = self._calculate_polygon_perimeter( points1 )
				perimeter2 = self._calculate_polygon_perimeter( points2 )

				if area1 < self.min_area_threshold or area2 < self.min_area_threshold:
					return 0.0

				# 1. 面积比例相似度 (50%权重)
				area_ratio = max( area1, area2 ) / min( area1, area2 )
				if area_ratio > 100:  # 极端比例
					area_similarity = 0.0
				else:
					area_similarity = 1.0 - min( 1.0, abs( np.log( area_ratio ) ) / self.log_threshold )

				# 2. 周长比例相似度 (30%权重)
				if perimeter1 > 0 and perimeter2 > 0:
					perimeter_ratio = max( perimeter1, perimeter2 ) / min( perimeter1, perimeter2 )
					if perimeter_ratio > 100:
						perimeter_similarity = 0.0
					else:
						perimeter_similarity = 1.0 - min( 1.0, abs( np.log( perimeter_ratio ) ) / self.log_threshold )
				else:
					perimeter_similarity = 0.0

				# 3. 长宽比相似度 (20%权重)
				bbox1 = self._calculate_bbox( points1 )
				bbox2 = self._calculate_bbox( points2 )

				width1 = bbox1[ 'max_x' ] - bbox1[ 'min_x' ]
				height1 = bbox1[ 'max_y' ] - bbox1[ 'min_y' ]
				width2 = bbox2[ 'max_x' ] - bbox2[ 'min_x' ]
				height2 = bbox2[ 'max_y' ] - bbox2[ 'min_y' ]

				if width1 > 0 and height1 > 0 and width2 > 0 and height2 > 0:
					aspect_ratio1 = width1 / height1
					aspect_ratio2 = width2 / height2
					aspect_ratio_diff = abs( aspect_ratio1 - aspect_ratio2 ) / max( aspect_ratio1, aspect_ratio2 )
					aspect_similarity = 1.0 - min( 1.0, aspect_ratio_diff )
				else:
					aspect_similarity = 0.0

				# 综合尺度相似度
				scale_similarity = (0.5 * area_similarity +
				                    0.3 * perimeter_similarity +
				                    0.2 * aspect_similarity)

				return max( 0.0, min( 1.0, scale_similarity ) )

			except Exception:
				return 0.0

		def _calculate_complexity_similarity( self, points1: np.ndarray, points2: np.ndarray ) -> float:
			"""计算复杂度相似度（凸包比、边数、曲率）"""
			try:
				# 1. 凸包复杂度 (50%权重)
				convex_complexity1 = self._calculate_convex_complexity( points1 )
				convex_complexity2 = self._calculate_convex_complexity( points2 )
				convex_diff = abs( convex_complexity1 - convex_complexity2 )
				convex_similarity = 1.0 - min( 1.0, convex_diff )

				# 2. 边数复杂度 (30%权重) - 提高敏感度
				edge_count1 = len( points1 )
				edge_count2 = len( points2 )
				edge_ratio = max( edge_count1, edge_count2 ) / min( edge_count1, edge_count2 ) if min( edge_count1, edge_count2 ) > 0 else 1.0
				# 边数差异越大，相似度越低
				edge_similarity = 1.0 / edge_ratio if edge_ratio > 0 else 0.0

				# 3. 形状紧凑度 (20%权重)
				compactness1 = self._calculate_compactness( points1 )
				compactness2 = self._calculate_compactness( points2 )
				compactness_diff = abs( compactness1 - compactness2 )
				compactness_similarity = 1.0 - min( 1.0, compactness_diff )

				# 综合复杂度相似度
				complexity_similarity = (0.5 * convex_similarity +
				                         0.3 * edge_similarity +
				                         0.2 * compactness_similarity)

				return max( 0.0, min( 1.0, complexity_similarity ) )

			except Exception:
				return 0.5

		def _calculate_symmetry_similarity( self, points1: np.ndarray, points2: np.ndarray ) -> float:
			"""计算对称性相似度"""
			try:
				# 计算水平和垂直对称性
				h_symmetry1 = self._calculate_horizontal_symmetry( points1 )
				h_symmetry2 = self._calculate_horizontal_symmetry( points2 )
				v_symmetry1 = self._calculate_vertical_symmetry( points1 )
				v_symmetry2 = self._calculate_vertical_symmetry( points2 )

				# 对称性差异
				h_diff = abs( h_symmetry1 - h_symmetry2 )
				v_diff = abs( v_symmetry1 - v_symmetry2 )

				# 综合对称性相似度
				symmetry_similarity = 1.0 - (0.5 * h_diff + 0.5 * v_diff)

				return max( 0.0, min( 1.0, symmetry_similarity ) )

			except Exception:
				return 0.5

		def _calculate_hausdorff_distance( self, points1: np.ndarray, points2: np.ndarray ) -> float:
			"""计算简化的Hausdorff距离"""
			try:
				# 计算从points1到points2的最大最小距离
				max_min_dist1 = 0.0
				for p1 in points1:
					min_dist = float( 'inf' )
					for p2 in points2:
						dist = np.sqrt( (p1[ 0 ] - p2[ 0 ]) ** 2 + (p1[ 1 ] - p2[ 1 ]) ** 2 )
						min_dist = min( min_dist, dist )
					max_min_dist1 = max( max_min_dist1, min_dist )

				# 计算从points2到points1的最大最小距离
				max_min_dist2 = 0.0
				for p2 in points2:
					min_dist = float( 'inf' )
					for p1 in points1:
						dist = np.sqrt( (p1[ 0 ] - p2[ 0 ]) ** 2 + (p1[ 1 ] - p2[ 1 ]) ** 2 )
						min_dist = min( min_dist, dist )
					max_min_dist2 = max( max_min_dist2, min_dist )

				# Hausdorff距离是两个方向的最大值
				return max( max_min_dist1, max_min_dist2 )

			except Exception:
				return float( 'inf' )

		def _calculate_characteristic_scale( self, points: np.ndarray ) -> float:
			"""计算特征尺度（边界框对角线长度）"""
			xs = points[ :, 0 ]
			ys = points[ :, 1 ]
			width = float( np.max( xs ) - np.min( xs ) )
			height = float( np.max( ys ) - np.min( ys ) )
			return (width ** 2 + height ** 2) ** 0.5

		def _calculate_polygon_area( self, points: np.ndarray ) -> float:
			"""计算多边形面积 (鞋带公式)"""
			if len( points ) < 3:
				return 0.0

			area = 0.0
			n = len( points )
			for i in range( n ):
				j = (i + 1) % n
				area += points[ i ][ 0 ] * points[ j ][ 1 ]
				area -= points[ j ][ 0 ] * points[ i ][ 1 ]
			return abs( area ) / 2.0

		def _calculate_polygon_perimeter( self, points: np.ndarray ) -> float:
			"""计算多边形周长"""
			if len( points ) < 2:
				return 0.0

			perimeter = 0.0
			n = len( points )
			for i in range( n ):
				j = (i + 1) % n
				dist = np.sqrt( (points[ j ][ 0 ] - points[ i ][ 0 ]) ** 2 + (points[ j ][ 1 ] - points[ i ][ 1 ]) ** 2 )
				perimeter += dist
			return perimeter

		def _calculate_bbox( self, points: np.ndarray ) -> dict:
			"""计算边界框"""
			xs = points[ :, 0 ]
			ys = points[ :, 1 ]
			return {
				'min_x': float( np.min( xs ) ), 'max_x': float( np.max( xs ) ),
				'min_y': float( np.min( ys ) ), 'max_y': float( np.max( ys ) )
			}

		def _calculate_convex_complexity( self, points: np.ndarray ) -> float:
			"""计算凸包复杂度（多边形面积与凸包面积的比值）"""
			try:
				polygon_area = self._calculate_polygon_area( points )
				convex_area = self._calculate_convex_hull_area( points )

				if convex_area > 0:
					return polygon_area / convex_area
				else:
					return 0.0
			except Exception:
				return 0.0

		def _calculate_convex_hull_area( self, points: np.ndarray ) -> float:
			"""计算凸包面积"""
			try:
				from scipy.spatial import ConvexHull
				if len( points ) < 3:
					return 0.0
				hull = ConvexHull( points )
				return hull.volume  # 在2D中，volume就是面积
			except Exception:
				# 降级方案：使用边界框面积
				bbox = self._calculate_bbox( points )
				width = bbox[ 'max_x' ] - bbox[ 'min_x' ]
				height = bbox[ 'max_y' ] - bbox[ 'min_y' ]
				return width * height

		def _calculate_compactness( self, points: np.ndarray ) -> float:
			"""计算形状紧凑度（4π×面积/周长²）"""
			try:
				area = self._calculate_polygon_area( points )
				perimeter = self._calculate_polygon_perimeter( points )

				if perimeter > 0:
					compactness = (4 * np.pi * area) / (perimeter ** 2)
					return min( 1.0, compactness )  # 圆形的紧凑度为1
				else:
					return 0.0
			except Exception:
				return 0.0

		def _calculate_horizontal_symmetry( self, points: np.ndarray ) -> float:
			"""计算水平对称性"""
			try:
				# 计算质心
				centroid_y = np.mean( points[ :, 1 ] )

				# 计算每个点关于水平轴的对称性
				symmetry_score = 0.0
				for point in points:
					# 找到关于水平轴对称的点
					symmetric_y = 2 * centroid_y - point[ 1 ]

					# 找到最近的实际点
					min_dist = float( 'inf' )
					for other_point in points:
						dist = abs( other_point[ 1 ] - symmetric_y ) + abs( other_point[ 0 ] - point[ 0 ] )
						min_dist = min( min_dist, dist )

					# 计算对称性得分（距离越小，对称性越好）
					char_scale = self._calculate_characteristic_scale( points )
					if char_scale > 0:
						symmetry_score += np.exp( -min_dist / (char_scale * 0.1) )

				return symmetry_score / len( points ) if len( points ) > 0 else 0.0

			except Exception:
				return 0.5

		def _calculate_vertical_symmetry( self, points: np.ndarray ) -> float:
			"""计算垂直对称性"""
			try:
				# 计算质心
				centroid_x = np.mean( points[ :, 0 ] )

				# 计算每个点关于垂直轴的对称性
				symmetry_score = 0.0
				for point in points:
					# 找到关于垂直轴对称的点
					symmetric_x = 2 * centroid_x - point[ 0 ]

					# 找到最近的实际点
					min_dist = float( 'inf' )
					for other_point in points:
						dist = abs( other_point[ 0 ] - symmetric_x ) + abs( other_point[ 1 ] - point[ 1 ] )
						min_dist = min( min_dist, dist )

					# 计算对称性得分
					char_scale = self._calculate_characteristic_scale( points )
					if char_scale > 0:
						symmetry_score += np.exp( -min_dist / (char_scale * 0.1) )

				return symmetry_score / len( points ) if len( points ) > 0 else 0.0

			except Exception:
				return 0.5

		def _zero_result( self ) -> dict:
			"""返回零结果"""
			return {
				'shape': 0.0,
				'scale': 0.0,
				'complexity': 0.0,
				'symmetry': 0.0,
				'overall': 0.0
			}

	class AdaptiveFusionModule:
		"""
		L3: 自适应融合层 (新架构)

		核心：注意力机制动态分配权重
		特点：可解释、自适应、稳定
		功能：替换固化的50%-50%权重分配
		"""

		def __init__( self, config: dict, logger=None ):
			self.fusion_strategy = config.get( 'fusion_strategy', 'attention' )  # 'attention', 'fixed', 'adaptive'
			self.confidence_threshold = config.get( 'confidence_threshold', 0.8 )
			self.min_weight = config.get( 'min_weight', 0.1 )  # 最小权重限制
			self.max_weight = config.get( 'max_weight', 0.8 )  # 最大权重限制
			self.stability_factor = config.get( 'stability_factor', 0.1 )  # 稳定性因子
			self.logger = logger

		def fuse( self, similarities: dict, points1: np.ndarray, points2: np.ndarray ) -> dict:
			"""
			自适应融合相似度

			参数:
				similarities: {
					'overlap': dict,     # 重叠度相似度结果
					'spatial': dict,     # 空间位置相似度结果
					'morphology': dict   # 形态特征相似度结果
				}

			返回:
				{
					'similarity': float,        # 融合后的综合相似度
					'confidence': float,        # 融合置信度
					'weights': dict,           # 各维度权重
					'components': dict,        # 各维度详细结果
					'fusion_reason': str       # 融合策略说明
				}
			"""
			try:
				# 计算注意力权重
				attention_weights = self._calculate_attention_weights( similarities, points1, points2 )

				# 执行加权融合
				fused_similarity = sum(
					attention_weights[ key ] * similarities[ key ][ 'overall' ]
					for key in similarities.keys()
				)

				# 计算融合置信度
				confidence = self._calculate_fusion_confidence( similarities, attention_weights )

				# 生成融合说明
				fusion_reason = self._generate_fusion_reason( attention_weights, similarities )

				return {
					'similarity': float( max( 0.0, min( 1.0, fused_similarity ) ) ),
					'confidence': float( max( 0.0, min( 1.0, confidence ) ) ),
					'weights': attention_weights,
					'components': similarities,
					'fusion_reason': fusion_reason
				}

			except Exception as e:
				if self.logger:
					self.logger.warning( f"自适应融合失败: {str( e )}" )
				# 标记为降级融合
				fallback_result = self._fallback_fusion( similarities )
				fallback_result[ 'fusion_reason' ] = f"降级融合: {fallback_result[ 'fusion_reason' ]}"
				return fallback_result

		def _calculate_attention_weights( self, similarities: dict, points1: np.ndarray, points2: np.ndarray ) -> dict:
			"""计算注意力权重（基于几何特征的自适应权重）"""
			try:
				# 分析几何特征
				geometry_analysis = self._analyze_geometry_characteristics( points1, points2 )

				# 基于几何特征的自适应权重
				if geometry_analysis[ 'area_ratio' ] < 0.3:
					# 尺寸差异大，更关注形态特征
					base_weights = { 'overlap': 0.2, 'spatial': 0.3, 'morphology': 0.5 }
					reason = "尺寸差异大，重点关注形态特征"
				elif geometry_analysis[ 'shape_complexity' ] > 0.8:
					# 形状复杂，更关注重叠度
					base_weights = { 'overlap': 0.5, 'spatial': 0.3, 'morphology': 0.2 }
					reason = "形状复杂，重点关注重叠度"
				elif geometry_analysis[ 'center_distance_ratio' ] > 2.0:
					# 距离较远，更关注空间位置
					base_weights = { 'overlap': 0.3, 'spatial': 0.5, 'morphology': 0.2 }
					reason = "距离较远，重点关注空间位置"
				else:
					# 一般情况，均衡权重
					base_weights = { 'overlap': 0.4, 'spatial': 0.4, 'morphology': 0.2 }
					reason = "一般情况，均衡权重分配"

				# 基于相似度置信度的权重调整
				adjusted_weights = self._adjust_weights_by_confidence( base_weights, similarities )

				# 应用权重约束
				final_weights = self._apply_weight_constraints( adjusted_weights )

				# 记录权重决策原因
				if hasattr( self, '_weight_decision_reason' ):
					self._weight_decision_reason = reason

				return final_weights

			except Exception as e:
				if self.logger:
					self.logger.warning( f"注意力权重计算失败: {str( e )}" )
				# 降级到均衡权重
				return { 'overlap': 0.4, 'spatial': 0.4, 'morphology': 0.2 }

		def _analyze_geometry_characteristics( self, points1: np.ndarray, points2: np.ndarray ) -> dict:
			"""分析几何特征"""
			try:
				# 计算面积
				area1 = self._calculate_polygon_area( points1 )
				area2 = self._calculate_polygon_area( points2 )
				area_ratio = min( area1, area2 ) / max( area1, area2 ) if max( area1, area2 ) > 0 else 0.0

				# 计算形状复杂度（边数归一化，更敏感的阈值）
				complexity1 = min( len( points1 ) / 6.0, 1.0 )  # 降低阈值，6个点以上认为复杂
				complexity2 = min( len( points2 ) / 6.0, 1.0 )
				shape_complexity = max( complexity1, complexity2 )  # 使用最大值而不是平均值

				# 计算中心距离比
				center1 = (np.mean( points1[ :, 0 ] ), np.mean( points1[ :, 1 ] ))
				center2 = (np.mean( points2[ :, 0 ] ), np.mean( points2[ :, 1 ] ))
				center_distance = np.sqrt( (center1[ 0 ] - center2[ 0 ]) ** 2 + (center1[ 1 ] - center2[ 1 ]) ** 2 )

				# 计算特征尺度
				scale1 = self._calculate_characteristic_scale( points1 )
				scale2 = self._calculate_characteristic_scale( points2 )
				avg_scale = (scale1 + scale2) / 2
				center_distance_ratio = center_distance / avg_scale if avg_scale > 0 else 0.0

				return {
					'area_ratio': area_ratio,
					'shape_complexity': shape_complexity,
					'center_distance_ratio': center_distance_ratio,
					'avg_scale': avg_scale
				}

			except Exception:
				# 返回默认值
				return {
					'area_ratio': 0.5,
					'shape_complexity': 0.5,
					'center_distance_ratio': 1.0,
					'avg_scale': 100.0
				}

		def _adjust_weights_by_confidence( self, base_weights: dict, similarities: dict ) -> dict:
			"""基于相似度置信度调整权重"""
			try:
				adjusted_weights = base_weights.copy()

				# 计算各维度的置信度（基于相似度的一致性）
				for key in similarities.keys():
					if key in adjusted_weights:
						sim_values = list( similarities[ key ].values() )
						if len( sim_values ) > 1:
							# 计算相似度值的方差作为置信度指标
							variance = np.var( sim_values )
							confidence = np.exp( -variance * 5.0 )  # 方差越小，置信度越高

							# 根据置信度调整权重
							if confidence > 0.8:
								adjusted_weights[ key ] *= 1.1  # 高置信度增加权重
							elif confidence < 0.3:
								adjusted_weights[ key ] *= 0.9  # 低置信度减少权重

				# 归一化权重
				total_weight = sum( adjusted_weights.values() )
				if total_weight > 0:
					for key in adjusted_weights:
						adjusted_weights[ key ] /= total_weight

				return adjusted_weights

			except Exception:
				return base_weights

		def _apply_weight_constraints( self, weights: dict ) -> dict:
			"""应用权重约束（防止极端权重分配）"""
			constrained_weights = { }

			for key, weight in weights.items():
				# 应用最小和最大权重限制
				constrained_weights[ key ] = max( self.min_weight, min( self.max_weight, weight ) )

			# 重新归一化
			total_weight = sum( constrained_weights.values() )
			if total_weight > 0:
				for key in constrained_weights:
					constrained_weights[ key ] /= total_weight

			return constrained_weights

		def _calculate_fusion_confidence( self, similarities: dict, weights: dict ) -> float:
			"""计算融合置信度"""
			try:
				# 基于各维度相似度的一致性计算置信度
				overall_similarities = [ similarities[ key ][ 'overall' ] for key in similarities.keys() ]

				if len( overall_similarities ) < 2:
					return 0.5

				# 计算相似度的标准差
				std_dev = np.std( overall_similarities )

				# 标准差越小，置信度越高
				consistency_confidence = np.exp( -std_dev * 3.0 )

				# 基于权重分布的置信度
				weight_values = list( weights.values() )
				weight_entropy = -sum( w * np.log( w + 1e-10 ) for w in weight_values )
				max_entropy = np.log( len( weight_values ) )
				weight_confidence = 1.0 - (weight_entropy / max_entropy) if max_entropy > 0 else 0.5

				# 综合置信度
				overall_confidence = 0.7 * consistency_confidence + 0.3 * weight_confidence

				return max( 0.0, min( 1.0, overall_confidence ) )

			except Exception:
				return 0.5

		def _generate_fusion_reason( self, weights: dict, similarities: dict ) -> str:
			"""生成融合策略说明"""
			try:
				# 找到权重最高的维度
				max_weight_key = max( weights.keys(), key=lambda k: weights[ k ] )
				max_weight = weights[ max_weight_key ]

				# 生成说明
				dimension_names = {
					'overlap': '重叠度',
					'spatial': '空间位置',
					'morphology': '形态特征'
				}

				if max_weight > 0.5:
					reason = f"主要基于{dimension_names.get( max_weight_key, max_weight_key )}相似度"
				else:
					reason = "均衡融合多维度相似度"

				# 添加权重分布信息
				weight_info = ", ".join( [ f"{dimension_names.get( k, k )}:{v:.1%}" for k, v in weights.items() ] )
				reason += f" (权重分布: {weight_info})"

				return reason

			except Exception:
				return "标准融合策略"

		def _fallback_fusion( self, similarities: dict ) -> dict:
			"""降级融合方案"""
			try:
				# 使用简单平均
				overall_similarities = [ similarities[ key ][ 'overall' ] for key in similarities.keys() ]
				avg_similarity = sum( overall_similarities ) / len( overall_similarities ) if overall_similarities else 0.0

				# 均匀权重
				uniform_weights = { key: 1.0 / len( similarities ) for key in similarities.keys() }

				return {
					'similarity': float( max( 0.0, min( 1.0, avg_similarity ) ) ),
					'confidence': 0.3,  # 降级方案置信度较低
					'weights': uniform_weights,
					'components': similarities,
					'fusion_reason': '降级融合：简单平均'
				}

			except Exception:
				return {
					'similarity': 0.0,
					'confidence': 0.0,
					'weights': { },
					'components': { },
					'fusion_reason': '融合失败'
				}

		def _calculate_polygon_area( self, points: np.ndarray ) -> float:
			"""计算多边形面积 (鞋带公式)"""
			if len( points ) < 3:
				return 0.0

			area = 0.0
			n = len( points )
			for i in range( n ):
				j = (i + 1) % n
				area += points[ i ][ 0 ] * points[ j ][ 1 ]
				area -= points[ j ][ 0 ] * points[ i ][ 1 ]
			return abs( area ) / 2.0

		def _calculate_characteristic_scale( self, points: np.ndarray ) -> float:
			"""计算特征尺度（边界框对角线长度）"""
			xs = points[ :, 0 ]
			ys = points[ :, 1 ]
			width = float( np.max( xs ) - np.min( xs ) )
			height = float( np.max( ys ) - np.min( ys ) )
			return (width ** 2 + height ** 2) ** 0.5

	class DecisionEngine:
		"""
		新决策引擎 (新架构)

		原则：简单、可配置、可解释
		功能：替换复杂的五层决策机制
		特点：基于融合相似度和置信度的智能决策
		"""

		def __init__( self, config: dict, logger=None ):
			self.high_threshold = config.get( 'high_threshold', 0.75 )
			self.low_threshold = config.get( 'low_threshold', 0.25 )
			self.confidence_threshold = config.get( 'confidence_threshold', 0.6 )
			self.medium_threshold_upper = config.get( 'medium_threshold_upper', 0.6 )
			self.medium_threshold_lower = config.get( 'medium_threshold_lower', 0.4 )
			self.logger = logger

		def decide( self, fusion_result: dict, user_threshold: float = None ) -> dict:
			"""
			做出最终决策

			参数:
				fusion_result: 自适应融合的结果
				user_threshold: 用户指定的相似度阈值

			返回:
				{
					'action': str,          # 'skip' 或 'add'
					'reason': str,          # 决策原因
					'confidence': float,    # 决策置信度
					'details': dict         # 详细信息
				}
			"""
			try:
				similarity = fusion_result[ 'similarity' ]
				confidence = fusion_result[ 'confidence' ]

				# 用户阈值优先检查
				if user_threshold is not None:
					if similarity < user_threshold:
						return {
							'action': 'add',
							'reason': f'低于用户阈值 (相似度={similarity:.3f} < {user_threshold:.3f})',
							'confidence': confidence,
							'details': {
								'decision_layer': 'user_threshold',
								'threshold_used': user_threshold,
								'similarity': similarity,
								'fusion_weights': fusion_result.get( 'weights', { } ),
								'fusion_reason': fusion_result.get( 'fusion_reason', '' )
							}
						}

				# 简化的三层决策
				if similarity >= self.high_threshold:
					return {
						'action': 'skip',
						'reason': f'高相似度 (相似度={similarity:.3f} ≥ {self.high_threshold})',
						'confidence': confidence,
						'details': {
							'decision_layer': 'high_similarity',
							'threshold_used': self.high_threshold,
							'similarity': similarity,
							'fusion_weights': fusion_result.get( 'weights', { } ),
							'fusion_reason': fusion_result.get( 'fusion_reason', '' )
						}
					}
				elif similarity <= self.low_threshold:
					return {
						'action': 'add',
						'reason': f'低相似度 (相似度={similarity:.3f} ≤ {self.low_threshold})',
						'confidence': confidence,
						'details': {
							'decision_layer': 'low_similarity',
							'threshold_used': self.low_threshold,
							'similarity': similarity,
							'fusion_weights': fusion_result.get( 'weights', { } ),
							'fusion_reason': fusion_result.get( 'fusion_reason', '' )
						}
					}
				else:
					# 中等相似度，基于置信度和细分阈值决策
					return self._decide_medium_similarity( similarity, confidence, fusion_result )

			except Exception as e:
				if self.logger:
					self.logger.warning( f"决策引擎失败: {str( e )}" )
				return self._fallback_decision( fusion_result )

		def _decide_medium_similarity( self, similarity: float, confidence: float, fusion_result: dict ) -> dict:
			"""中等相似度的决策逻辑"""
			try:
				# 基于置信度的决策
				if confidence >= self.confidence_threshold:
					# 高置信度时，进一步细分相似度区间
					if similarity >= self.medium_threshold_upper:
						action = 'skip'
						reason = f'中高相似度，高置信度 (相似度={similarity:.3f}, 置信度={confidence:.3f})'
					elif similarity <= self.medium_threshold_lower:
						action = 'add'
						reason = f'中低相似度，高置信度 (相似度={similarity:.3f}, 置信度={confidence:.3f})'
					else:
						# 中等相似度，高置信度，倾向于跳过
						action = 'skip'
						reason = f'中等相似度，高置信度，倾向跳过 (相似度={similarity:.3f}, 置信度={confidence:.3f})'
				else:
					# 低置信度时，倾向于追加（保守策略）
					action = 'add'
					reason = f'中等相似度，低置信度，保守追加 (相似度={similarity:.3f}, 置信度={confidence:.3f})'

				return {
					'action': action,
					'reason': reason,
					'confidence': confidence,
					'details': {
						'decision_layer': 'medium_similarity',
						'confidence_based': True,
						'similarity': similarity,
						'confidence_threshold': self.confidence_threshold,
						'fusion_weights': fusion_result.get( 'weights', { } ),
						'fusion_reason': fusion_result.get( 'fusion_reason', '' )
					}
				}

			except Exception:
				# 中等相似度决策失败时，默认追加
				return {
					'action': 'add',
					'reason': f'中等相似度决策失败，默认追加 (相似度={similarity:.3f})',
					'confidence': 0.3,
					'details': { 'decision_layer': 'fallback_medium' }
				}

		def _fallback_decision( self, fusion_result: dict ) -> dict:
			"""降级决策方案"""
			try:
				similarity = fusion_result.get( 'similarity', 0.0 )

				# 简单的阈值决策
				if similarity > 0.5:
					action = 'skip'
					reason = '降级决策：相似度>0.5，跳过'
				else:
					action = 'add'
					reason = '降级决策：相似度≤0.5，追加'

				return {
					'action': action,
					'reason': reason,
					'confidence': 0.2,  # 降级决策置信度较低
					'details': {
						'decision_layer': 'fallback',
						'similarity': similarity,
						'fusion_result': fusion_result
					}
				}

			except Exception:
				# 最终降级：默认追加
				return {
					'action': 'add',
					'reason': '决策引擎完全失败，默认追加',
					'confidence': 0.0,
					'details': { 'decision_layer': 'emergency_fallback' }
				}

	def __preprocess_coordinates( self, points: List[ List[ float ] ] ) -> Optional[ np.ndarray ]:
		"""
		坐标预处理和标准化

		处理步骤:
		1. 格式统一 - 转换为numpy数组
		2. 异常值处理 - 过滤无穷大、NaN值
		3. 坐标排序 - 确保顺时针或逆时针顺序
		4. 数据验证 - 检查几何有效性

		参数:
			points: 原始坐标点列表

		返回:
			预处理后的numpy数组，失败返回None
		"""
		try:
			# 检查输入有效性 (支持numpy数组)
			if points is None:
				return None

			# 转换为numpy数组
			points_array = np.array( points, dtype=np.float64 )

			if len( points_array ) < 3:
				return None

			# 检查和过滤异常值
			if np.any( np.isnan( points_array ) ) or np.any( np.isinf( points_array ) ):
				# 过滤掉包含NaN或无穷大的点
				valid_mask = ~(np.isnan( points_array ).any( axis=1 ) | np.isinf( points_array ).any( axis=1 ))
				points_array = points_array[ valid_mask ]

				if len( points_array ) < 3:
					return None

			# 确保坐标顺序 (使用简化的顺序检查)
			# 计算多边形的有向面积，如果为负则逆时针，为正则顺时针
			signed_area = 0.0
			n = len( points_array )
			for i in range( n ):
				j = (i + 1) % n
				signed_area += (points_array[ j ][ 0 ] - points_array[ i ][ 0 ]) * (points_array[ j ][ 1 ] + points_array[ i ][ 1 ])

			# 如果有向面积为负，说明是逆时针，需要反转
			if signed_area < 0:
				points_array = points_array[ ::-1 ]

			return points_array

		except Exception as e:
			self.__logger.warning( f"坐标预处理失败: {str( e )}" )
			return None


	# ==================== 配置和管理方法 ====================

	def configure_weights(
			self,
			geometric_weight: float = 0.50,
			position_weight: float = 0.50,
			scale_weight: float = 0.00,
			shape_weight: float = 0.00
	):
		"""
		配置相似度权重 (新架构)

		注意：在新架构中，只有几何和位置相似度参与综合相似度计算
		尺度和形状相似度仅作为前提条件使用，不参与权重计算

		参数:
			geometric_weight: 几何相似度权重，默认50% (实际使用)
			position_weight: 位置相似度权重，默认50% (实际使用)
			scale_weight: 尺度相似度权重，默认0% (仅作前提条件，不参与计算)
			shape_weight: 形状相似度权重，默认0% (仅作前提条件，不参与计算)

		使用示例:
			# 调整几何和位置的权重比例
			processor.configure_weights(
				geometric_weight=0.60,
				position_weight=0.40,
				scale_weight=0.00,  # 必须为0
				shape_weight=0.00   # 必须为0
			)
		"""
		# 验证核心权重总和 (只验证几何和位置)
		core_weight_total = geometric_weight + position_weight
		if abs( core_weight_total - 1.0 ) > 0.01:
			self.__logger.warning( f"几何和位置权重总和为{core_weight_total:.3f}，建议调整为1.0" )

		# 验证尺度和形状权重为0
		if scale_weight != 0.0 or shape_weight != 0.0:
			self.__logger.warning( f"新架构中尺度和形状权重应为0，当前值: 尺度={scale_weight}, 形状={shape_weight}" )

		# 更新权重
		self.__geometric_weight = geometric_weight
		self.__position_weight = position_weight
		self.__scale_weight = scale_weight
		self.__shape_weight = shape_weight

		self.__logger.info(
			f"相似度权重已更新: 几何={geometric_weight:.2f}, 位置={position_weight:.2f}, "
			f"尺度={scale_weight:.2f}, 形状={shape_weight:.2f}"
		)

		if self.__log_output:
			self.__log_output.append(
				f"坐标相似度权重已更新: 几何={geometric_weight:.0%}, 位置={position_weight:.0%}, "
				f"尺度={scale_weight:.0%}, 形状={shape_weight:.0%}",
				Colors.GREEN
			)

	def configure_thresholds(
			self,
			extreme_threshold: float = None,
			high_threshold: float = None,
			medium_threshold: float = None,
			low_threshold: float = None
	):
		"""
		配置决策阈值

		参数:
			extreme_threshold: 极高相似度阈值
			high_threshold: 高相似度阈值
			medium_threshold: 中等相似度阈值
			low_threshold: 低相似度阈值

		使用示例:
			# 设置更严格的阈值
			processor.configure_thresholds(
				extreme_threshold=0.95,
				high_threshold=0.80,
				low_threshold=0.35
			)
		"""
		if extreme_threshold is not None:
			self.__extreme_similarity_threshold = extreme_threshold
		if high_threshold is not None:
			self.__high_similarity_threshold = high_threshold
		if medium_threshold is not None:
			self.__medium_similarity_threshold = medium_threshold
		if low_threshold is not None:
			self.__low_similarity_threshold = low_threshold

		self.__logger.info(
			f"决策阈值已更新: 极高≥{self.__extreme_similarity_threshold:.2f}, "
			f"高≥{self.__high_similarity_threshold:.2f}, "
			f"中等≥{self.__medium_similarity_threshold:.2f}, "
			f"低<{self.__low_similarity_threshold:.2f}"
		)

		if self.__log_output:
			self.__log_output.append(
				f"坐标相似度阈值已更新: 极高≥{self.__extreme_similarity_threshold:.0%}, "
				f"高≥{self.__high_similarity_threshold:.0%}, 低<{self.__low_similarity_threshold:.0%}",
				Colors.GREEN
			)

	def clear_cache( self ):
		"""清空计算缓存"""
		with self.__cache_lock:
			self.__calculation_cache.clear()
		self.__logger.info( "坐标相似度计算缓存已清空" )

	def get_cache_info( self ) -> Dict[ str, Any ]:
		"""获取缓存信息"""
		with self.__cache_lock:
			return {
				'cache_size': len( self.__calculation_cache ),
				'max_cache_size': self.__max_cache_size,
				'cache_keys': list( self.__calculation_cache.keys() )[ :10 ]  # 只显示前10个键
			}

	def get_performance_stats( self ) -> Dict[ str, Any ]:
		"""获取性能统计信息"""
		with self.__performance_lock:
			avg_time = (self.__total_calculation_time / self.__calculation_count
			            if self.__calculation_count > 0 else 0.0)
			return {
				'calculation_count': self.__calculation_count,
				'total_calculation_time': self.__total_calculation_time,
				'average_calculation_time': avg_time,
				'calculations_per_second': (self.__calculation_count / self.__total_calculation_time
				                            if self.__total_calculation_time > 0 else 0.0)
			}

	def get_configuration( self ) -> Dict[ str, Any ]:
		"""获取当前配置信息"""
		return {
			'mode': self.__mode,
			'weights': {
				'geometric': self.__geometric_weight,
				'position': self.__position_weight,
				'scale': self.__scale_weight,
				'shape': self.__shape_weight
			},
			'thresholds': {
				'extreme_similarity': self.__extreme_similarity_threshold,
				'high_similarity': self.__high_similarity_threshold,
				'medium_similarity': self.__medium_similarity_threshold,
				'low_similarity': self.__low_similarity_threshold
			},
			'fine_tuning_thresholds': {
				'single_dimension_high': self.__single_dimension_high_threshold,
				'multi_dimension': self.__multi_dimension_threshold,
				'key_dimension_geometric': self.__key_dimension_geometric_threshold,
				'key_dimension_position': self.__key_dimension_position_threshold,
				'geometric_veto': self.__geometric_veto_threshold,
				'position_veto': self.__position_veto_threshold
			},
			'geometric_parameters': {
				'min_area_threshold': self.__min_area_threshold,
				'max_size_ratio': self.__max_size_ratio,
				'distance_threshold_factor': self.__distance_threshold_factor,
				'log_threshold': self.__log_threshold
			}
		}
