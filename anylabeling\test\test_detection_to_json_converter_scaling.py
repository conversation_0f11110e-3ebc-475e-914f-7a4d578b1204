#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DetectionToJsonConverter 缩放算法全面测试套件

该测试文件为 DetectionToJsonConverter 类的缩放标注框功能提供全面的单元测试，
包括数学正确性验证、边界条件测试、性能对比测试和集成流程测试。

测试覆盖范围：
- 核心缩放算法 (_scale_obb_annotation)
- 几何中心计算 (_calculate_obb_center)
- 缩放中心点计算 (_calculate_scaling_center)
- 类方法功能 (set/get_default_scaling_center_type)
- 完整缩放流程 (_apply_scaling_if_enabled)

作者：AI Assistant
创建时间：2025-01-29
版本：1.0.0
"""

import unittest
import math
import sys
import os
from unittest.mock import Mock, MagicMock, patch
from typing import List, Tuple, Dict, Any

# 添加项目路径到系统路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# 导入被测试的类
from anylabeling.customize_ui.src.ui_operate.predict_image.helper import DetectionToJsonConverter


class MockLogger:
    """模拟Logger对象，用于测试中替代真实的日志记录器"""
    
    def __init__(self):
        self.logs = []
    
    def debug(self, message):
        self.logs.append(('DEBUG', message))
    
    def info(self, message):
        self.logs.append(('INFO', message))
    
    def warning(self, message):
        self.logs.append(('WARNING', message))
    
    def error(self, message):
        self.logs.append(('ERROR', message))


class MockLogOutput:
    """模拟LogOutput对象，用于测试中替代真实的UI日志输出"""
    
    def __init__(self):
        self.messages = []
    
    def append(self, message, color=None):
        self.messages.append((message, color))


class MockLineEditManager:
    """模拟LineEditManager对象，用于测试中替代真实的UI控件管理器"""
    
    def __init__(self, values=None):
        self.values = values or {}
    
    def get_value(self, widget_id):
        return self.values.get(widget_id, "1.0")


class MockCheckBoxManager:
    """模拟CheckBoxManager对象，用于测试中替代真实的UI控件管理器"""
    
    def __init__(self, values=None):
        self.values = values or {}
    
    def is_checked(self, widget_id):
        return self.values.get(widget_id, False)


class TestDetectionToJsonConverterScaling(unittest.TestCase):
    """DetectionToJsonConverter 缩放算法测试类"""
    
    def setUp(self):
        """测试前的初始化设置"""
        self.mock_logger = MockLogger()
        self.mock_log_output = MockLogOutput()
        self.mock_line_edit_manager = MockLineEditManager()
        self.mock_checkbox_manager = MockCheckBoxManager()
        
        # 创建被测试对象
        self.converter = DetectionToJsonConverter(
            version="3.0.3",
            log_output=self.mock_log_output,
            logger=self.mock_logger,
            line_edit_manager=self.mock_line_edit_manager,
            checkbox_manager=self.mock_checkbox_manager
        )
        
        # 测试数据：标准正方形OBB
        self.square_obb = [
            [100.0, 100.0],  # 左上角
            [200.0, 100.0],  # 右上角
            [200.0, 200.0],  # 右下角
            [100.0, 200.0]   # 左下角
        ]
        
        # 测试数据：矩形OBB
        self.rectangle_obb = [
            [50.0, 80.0],   # 左上角
            [150.0, 80.0],  # 右上角
            [150.0, 120.0], # 右下角
            [50.0, 120.0]   # 左下角
        ]
        
        # 测试数据：不规则四边形OBB
        self.irregular_obb = [
            [10.0, 20.0],
            [30.0, 15.0],
            [35.0, 40.0],
            [15.0, 45.0]
        ]
    
    def tearDown(self):
        """测试后的清理工作"""
        # 重置类变量到默认值
        DetectionToJsonConverter._default_scaling_center_type = "center"
    
    def assertPointsAlmostEqual(self, points1: List[List[float]], points2: List[List[float]], 
                               places: int = 6, msg: str = None):
        """
        断言两个点列表在数值上几乎相等
        
        Args:
            points1: 第一个点列表
            points2: 第二个点列表
            places: 小数点后比较位数
            msg: 断言失败时的错误消息
        """
        self.assertEqual(len(points1), len(points2), 
                        f"点列表长度不匹配: {len(points1)} vs {len(points2)}")
        
        for i, (p1, p2) in enumerate(zip(points1, points2)):
            self.assertAlmostEqual(p1[0], p2[0], places=places,
                                 msg=f"点{i}的X坐标不匹配: {p1[0]} vs {p2[0]}")
            self.assertAlmostEqual(p1[1], p2[1], places=places,
                                 msg=f"点{i}的Y坐标不匹配: {p1[1]} vs {p2[1]}")


class TestGeometricCenterCalculation(TestDetectionToJsonConverterScaling):
    """几何中心计算测试类"""
    
    def test_calculate_obb_center_square(self):
        """测试正方形OBB的几何中心计算"""
        center_x, center_y = self.converter._calculate_obb_center(self.square_obb)
        
        # 正方形的中心应该是 (150, 150)
        self.assertAlmostEqual(center_x, 150.0, places=6)
        self.assertAlmostEqual(center_y, 150.0, places=6)
    
    def test_calculate_obb_center_rectangle(self):
        """测试矩形OBB的几何中心计算"""
        center_x, center_y = self.converter._calculate_obb_center(self.rectangle_obb)
        
        # 矩形的中心应该是 (100, 100)
        self.assertAlmostEqual(center_x, 100.0, places=6)
        self.assertAlmostEqual(center_y, 100.0, places=6)
    
    def test_calculate_obb_center_irregular(self):
        """测试不规则四边形OBB的几何中心计算"""
        center_x, center_y = self.converter._calculate_obb_center(self.irregular_obb)
        
        # 不规则四边形的中心：((10+30+35+15)/4, (20+15+40+45)/4) = (22.5, 30)
        self.assertAlmostEqual(center_x, 22.5, places=6)
        self.assertAlmostEqual(center_y, 30.0, places=6)
    
    def test_calculate_obb_center_empty_input(self):
        """测试空输入的处理"""
        center_x, center_y = self.converter._calculate_obb_center([])
        self.assertEqual(center_x, 0.0)
        self.assertEqual(center_y, 0.0)
    
    def test_calculate_obb_center_invalid_input(self):
        """测试无效输入的处理"""
        # 测试点数不足
        center_x, center_y = self.converter._calculate_obb_center([[1, 2], [3, 4]])
        self.assertEqual(center_x, 0.0)
        self.assertEqual(center_y, 0.0)
        
        # 测试无效坐标格式
        center_x, center_y = self.converter._calculate_obb_center([
            [1, 2], [3, 4], ["invalid", 6], [7, 8]
        ])
        self.assertEqual(center_x, 0.0)
        self.assertEqual(center_y, 0.0)


class TestScalingCenterCalculation(TestDetectionToJsonConverterScaling):
    """缩放中心点计算测试类"""
    
    def test_calculate_scaling_center_center_type(self):
        """测试center类型的缩放中心点计算"""
        center_x, center_y = self.converter._calculate_scaling_center(self.square_obb, "center")
        
        # center类型应该返回几何中心
        self.assertAlmostEqual(center_x, 150.0, places=6)
        self.assertAlmostEqual(center_y, 150.0, places=6)
    
    def test_calculate_scaling_center_left_top(self):
        """测试leftTop类型的缩放中心点计算"""
        center_x, center_y = self.converter._calculate_scaling_center(self.square_obb, "leftTop")
        
        # leftTop类型应该返回最小X和最小Y的交点
        self.assertAlmostEqual(center_x, 100.0, places=6)
        self.assertAlmostEqual(center_y, 100.0, places=6)
    
    def test_calculate_scaling_center_right_bottom(self):
        """测试rightBottom类型的缩放中心点计算"""
        center_x, center_y = self.converter._calculate_scaling_center(self.square_obb, "rightBottom")
        
        # rightBottom类型应该返回最大X和最大Y的交点
        self.assertAlmostEqual(center_x, 200.0, places=6)
        self.assertAlmostEqual(center_y, 200.0, places=6)
    
    def test_calculate_scaling_center_all_types(self):
        """测试所有5种缩放中心点类型"""
        test_cases = [
            ("center", 150.0, 150.0),      # 几何中心
            ("leftTop", 100.0, 100.0),     # 左上角
            ("rightTop", 200.0, 100.0),    # 右上角
            ("leftBottom", 100.0, 200.0),  # 左下角
            ("rightBottom", 200.0, 200.0)  # 右下角
        ]
        
        for center_type, expected_x, expected_y in test_cases:
            with self.subTest(center_type=center_type):
                center_x, center_y = self.converter._calculate_scaling_center(
                    self.square_obb, center_type
                )
                self.assertAlmostEqual(center_x, expected_x, places=6,
                                     msg=f"{center_type}类型的X坐标不匹配")
                self.assertAlmostEqual(center_y, expected_y, places=6,
                                     msg=f"{center_type}类型的Y坐标不匹配")
    
    def test_calculate_scaling_center_unknown_type(self):
        """测试未知中心点类型的处理"""
        center_x, center_y = self.converter._calculate_scaling_center(
            self.square_obb, "unknown_type"
        )
        
        # 未知类型应该降级为几何中心
        self.assertAlmostEqual(center_x, 150.0, places=6)
        self.assertAlmostEqual(center_y, 150.0, places=6)


if __name__ == '__main__':
    unittest.main()
