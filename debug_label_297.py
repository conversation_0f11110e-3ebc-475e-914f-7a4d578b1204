#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细调试label_297控件更新情况
"""

import sys
import os
from unittest.mock import Mock, call

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'anylabeling'))

def debug_label_297_updates():
    """详细调试label_297控件的更新情况"""
    print("=" * 60)
    print("详细调试label_297控件更新情况")
    print("=" * 60)
    
    try:
        # 导入TrainingDataExporter类
        from anylabeling.customize_ui.src.ui_operate.train_data.helper1 import TrainingDataExporter
        
        # 创建模拟管理器
        line_edit_manager = Mock()
        line_edit_manager.get_text.return_value = "test_value"
        
        checkbox_manager = Mock()
        checkbox_manager.get_checked_state_by_object_name.return_value = True
        
        log_output = Mock()
        logger = Mock()
        
        # 创建模拟的QLabelManager，记录所有调用
        label_manager = Mock()
        label_manager.has_label.return_value = True
        label_manager.set_text.return_value = True
        label_manager.clear_text.return_value = True
        label_manager.start_fade_animation.return_value = True
        
        print("1. 创建TrainingDataExporter实例...")
        exporter = TrainingDataExporter(
            line_edit_manager=line_edit_manager,
            checkbox_manager=checkbox_manager,
            log_output=log_output,
            logger=logger,
            label_manager=label_manager
        )
        print("✓ 实例创建成功")
        
        # 检查初始化时的调用
        print("\n2. 检查初始化时的label_manager调用...")
        print(f"has_label调用次数: {label_manager.has_label.call_count}")
        print(f"set_text调用次数: {label_manager.set_text.call_count}")
        print(f"start_fade_animation调用次数: {label_manager.start_fade_animation.call_count}")
        
        # 检查has_label的调用参数
        if label_manager.has_label.call_args_list:
            for i, call_args in enumerate(label_manager.has_label.call_args_list):
                print(f"has_label调用{i+1}: {call_args}")
        
        # 检查set_text的调用参数
        if label_manager.set_text.call_args_list:
            for i, call_args in enumerate(label_manager.set_text.call_args_list):
                print(f"set_text调用{i+1}: {call_args}")
        
        print("\n3. 测试进度显示方法...")
        
        # 重置mock以清除初始化时的调用记录
        label_manager.reset_mock()
        
        # 测试__set_total_progress方法
        print("测试__set_total_progress方法...")
        exporter._TrainingDataExporter__set_total_progress("正在处理 100 条数据，转换为YOLO格式...")
        
        print(f"has_label调用次数: {label_manager.has_label.call_count}")
        print(f"set_text调用次数: {label_manager.set_text.call_count}")
        print(f"start_fade_animation调用次数: {label_manager.start_fade_animation.call_count}")
        
        # 详细检查调用参数
        if label_manager.has_label.call_args_list:
            for i, call_args in enumerate(label_manager.has_label.call_args_list):
                print(f"has_label调用{i+1}: {call_args}")
        
        if label_manager.set_text.call_args_list:
            for i, call_args in enumerate(label_manager.set_text.call_args_list):
                widget_name, text = call_args[0]
                print(f"set_text调用{i+1}: 控件='{widget_name}', 文本='{text}'")
        
        # 重置mock
        label_manager.reset_mock()
        
        # 测试__set_completion_status方法
        print("\n测试__set_completion_status方法...")
        exporter._TrainingDataExporter__set_completion_status("YOLO格式转换完成")
        
        print(f"has_label调用次数: {label_manager.has_label.call_count}")
        print(f"set_text调用次数: {label_manager.set_text.call_count}")
        print(f"start_fade_animation调用次数: {label_manager.start_fade_animation.call_count}")
        
        if label_manager.set_text.call_args_list:
            for i, call_args in enumerate(label_manager.set_text.call_args_list):
                widget_name, text = call_args[0]
                print(f"set_text调用{i+1}: 控件='{widget_name}', 文本='{text}'")
        
        print("\n4. 检查进度显示配置...")
        progress_config = exporter._TrainingDataExporter__progress_widget_config
        print(f"进度显示配置: {progress_config}")
        
        # 检查label_manager是否为None
        label_mgr = exporter._TrainingDataExporter__label_manager
        print(f"label_manager是否为None: {label_mgr is None}")
        print(f"label_manager类型: {type(label_mgr)}")
        
        print("\n5. 手动测试控件检查...")
        # 手动调用has_label检查
        result = label_manager.has_label('label_297')
        print(f"手动调用has_label('label_297')结果: {result}")
        
        # 手动调用set_text
        result = label_manager.set_text('label_297', '测试文本')
        print(f"手动调用set_text('label_297', '测试文本')结果: {result}")
        
        print("\n✓ 所有调试检查完成")
        return True
        
    except Exception as e:
        print(f"✗ 调试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_label_297_updates()
    print("\n" + "=" * 60)
    if success:
        print("调试完成 ✓")
    else:
        print("调试失败 ✗")
