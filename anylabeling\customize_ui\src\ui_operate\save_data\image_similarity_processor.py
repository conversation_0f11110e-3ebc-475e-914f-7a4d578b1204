#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于图像裁剪的标注相似度比较系统

实现多重相似度算法融合的图像相似度判断系统，用于替代基于坐标计算的相似度判断。
根据 image_based_similarity_comparison_solution.md 文档严格实现。

作者: AI Assistant
创建时间: 2025-01-20
"""

import os
import json
import hashlib
import threading
from typing import Dict, List, Tuple, Any, Optional, Union
from pathlib import Path
import numpy as np
from PIL import Image, ImageDraw
import cv2

from global_tools.utils import Logger, Colors


class ImageSimilarityProcessor:
	"""
	基于图像裁剪的标注相似度比较处理器

	实现五重相似度算法融合：
	1. SSIM结构相似性（30%权重）
	2. 直方图相似度（25%权重）
	3. 特征点匹配（20%权重）
	4. 轮廓形状相似度（15%权重）
	5. 像素级相似度（10%权重）

	使用示例:
		processor = ImageSimilarityProcessor(
			image_cache_dir="./cache/similarity_images",
			target_size=(256, 256),
			logger=logger
		)

		result = processor.compare_annotations(
			image_path="path/to/image.jpg",
			points1=[[100,100], [200,100], [200,200], [100,200]],
			points2=[[105,105], [205,105], [205,205], [105,205]]
		)

		if result['decision'] == 'skip':
			print(f"标注相似，跳过: {result['reason']}")
		else:
			print(f"标注不同，追加: {result['reason']}")
	"""

	def __init__(
			self,
			image_cache_dir: str = "./cache/similarity_images",
			target_size: Tuple[int, int] = (256, 256),
			margin: int = 20,
			logger: Optional[Logger] = None,
			log_output: Optional[Any] = None
	):
		"""
		初始化图像相似度处理器

		参数:
			image_cache_dir: 图像缓存目录
			target_size: 标准化图像尺寸 (width, height)
			margin: 裁剪边距（像素）
			logger: 日志记录器
			log_output: 日志输出对象
		"""
		self.__image_cache_dir = Path(image_cache_dir)
		self.__target_size = target_size
		self.__margin = margin
		self.__logger = logger or Logger("ImageSimilarityProcessor")
		self.__log_output = log_output

		# 创建缓存目录
		self.__image_cache_dir.mkdir(parents=True, exist_ok=True)

		# 缓存管理
		self.__image_cache = {}  # 图像缓存
		self.__cache_lock = threading.Lock()  # 缓存锁
		self.__max_cache_size = 100  # 最大缓存数量

		# 相似度算法权重配置
		self.__ssim_weight = 0.30          # SSIM权重30%
		self.__histogram_weight = 0.25     # 直方图权重25%
		self.__feature_weight = 0.20       # 特征点权重20%
		self.__contour_weight = 0.15       # 轮廓权重15%
		self.__pixel_weight = 0.10         # 像素级权重10%

		# 决策阈值配置
		self.__extreme_similarity_threshold = 0.95    # 极高相似度阈值
		self.__high_similarity_threshold = 0.85       # 高相似度阈值
		self.__medium_similarity_threshold = 0.70     # 中等相似度阈值
		self.__low_similarity_threshold = 0.50        # 低相似度阈值

		# 细化判断阈值配置（更严格的设置）
		self.__refined_high_threshold = 0.95          # 细化判断中的单维度高相似度阈值（提高到95%）
		self.__refined_medium_threshold = 0.75        # 细化判断中的多维度一致性阈值（提高到75%）
		self.__refined_special_ssim_threshold = 0.85  # 细化判断中的特殊情况SSIM阈值（提高到85%）
		self.__refined_special_contour_threshold = 0.85  # 细化判断中的特殊情况轮廓阈值（提高到85%）
		self.__refined_multi_dimension_count = 4      # 细化判断中的多维度一致性最小数量（提高到4个）

		# 细化判断中的其他阈值配置
		self.__refined_core_pixel_threshold = 0.80    # 核心维度检查中的像素阈值
		self.__refined_double_verify_hist_threshold = 0.70  # 双重验证中的直方图阈值
		self.__refined_double_verify_feature_threshold = 0.70  # 双重验证中的特征阈值
		self.__refined_comprehensive_threshold = 0.82  # 综合相似度严格检查阈值

		# 直方图相似度计算已优化为固定的最佳权重组合
		# 不再需要可配置权重，使用最精确的3种方法组合

		# SSIM相似度计算权重配置
		self.__ssim_global_weight = 0.40              # 全局SSIM权重
		self.__ssim_local_weight = 0.25               # 局部SSIM方差权重
		self.__ssim_multiscale_weight = 0.25          # 多尺度SSIM权重
		self.__ssim_structure_weight = 0.10           # 结构差异惩罚权重

		self.__logger.info("图像相似度处理器初始化完成")
		if self.__log_output:
			self.__log_output.append("图像相似度处理器初始化完成", Colors.GREEN)

	def compare_annotations(
			self,
			image_path: str,
			points1: List[List[float]],
			points2: List[List[float]]
	) -> Dict[str, Any]:
		"""
		比较两个标注的相似度

		参数:
			image_path: 原始图像路径
			points1: 第一个标注的坐标点
			points2: 第二个标注的坐标点

		返回:
			包含相似度详情和决策结果的字典
			{
				'ssim_similarity': float,
				'histogram_similarity': float,
				'feature_similarity': float,
				'contour_similarity': float,
				'pixel_similarity': float,
				'comprehensive_similarity': float,
				'decision': 'skip'/'add',
				'reason': str,
				'processing_time': float
			}
		"""
		import time
		start_time = time.time()

		try:
			# 基础验证
			if not points1 or not points2:
				return self.__create_result(
					decision='add',
					reason='坐标为空，可以追加',
					processing_time=time.time() - start_time
				)

			if not os.path.exists(image_path):
				return self.__create_result(
					decision='add',
					reason=f'图像文件不存在: {image_path}',
					processing_time=time.time() - start_time
				)

			# 裁剪并预处理图像
			image1 = self.__crop_and_preprocess_image(image_path, points1)
			image2 = self.__crop_and_preprocess_image(image_path, points2)

			if image1 is None or image2 is None:
				return self.__create_result(
					decision='add',
					reason='图像裁剪失败',
					processing_time=time.time() - start_time
				)

			# 计算五重相似度
			ssim_sim = self.__calculate_ssim_similarity(image1, image2)
			hist_sim = self.__calculate_histogram_similarity(image1, image2)
			feature_sim = self.__calculate_feature_similarity(image1, image2)
			contour_sim = self.__calculate_contour_similarity(image1, image2)
			pixel_sim = self.__calculate_pixel_similarity(image1, image2)

			# 计算综合相似度
			comprehensive_sim = (
				self.__ssim_weight * ssim_sim +
				self.__histogram_weight * hist_sim +
				self.__feature_weight * feature_sim +
				self.__contour_weight * contour_sim +
				self.__pixel_weight * pixel_sim
			)

			# 多层次决策
			decision, reason = self.__make_similarity_decision(
				ssim_sim, hist_sim, feature_sim, contour_sim, pixel_sim, comprehensive_sim
			)

			processing_time = time.time() - start_time

			self.__logger.debug(
				f"图像相似度计算完成: SSIM={ssim_sim:.3f}, 直方图={hist_sim:.3f}, "
				f"特征={feature_sim:.3f}, 轮廓={contour_sim:.3f}, 像素={pixel_sim:.3f}, "
				f"综合={comprehensive_sim:.3f}, 决策={decision}, 耗时={processing_time:.3f}s"
			)

			return {
				'ssim_similarity': ssim_sim,
				'histogram_similarity': hist_sim,
				'feature_similarity': feature_sim,
				'contour_similarity': contour_sim,
				'pixel_similarity': pixel_sim,
				'comprehensive_similarity': comprehensive_sim,
				'decision': decision,
				'reason': reason,
				'processing_time': processing_time
			}

		except Exception as e:
			processing_time = time.time() - start_time
			error_msg = f"图像相似度计算失败: {str(e)}"
			self.__logger.error(error_msg)
			return self.__create_result(
				decision='add',
				reason=error_msg,
				processing_time=processing_time
			)

	def __crop_and_preprocess_image(self, image_path: str, points: List[List[float]]) -> Optional[np.ndarray]:
		"""
		裁剪并预处理图像

		步骤:
		1. 计算标注的边界框
		2. 添加边距
		3. 裁剪图像
		4. 创建多边形掩码
		5. 应用黑色填充
		6. 标准化尺寸

		参数:
			image_path: 图像路径
			points: 标注坐标点

		返回:
			预处理后的图像数组，失败返回None
		"""
		try:
			# 生成缓存键
			cache_key = self.__generate_cache_key(image_path, points)

			# 检查缓存
			with self.__cache_lock:
				if cache_key in self.__image_cache:
					return self.__image_cache[cache_key]

			# 加载原始图像
			original_image = cv2.imread(image_path)
			if original_image is None:
				self.__logger.error(f"无法加载图像: {image_path}")
				return None

			# 转换为RGB
			original_image = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)

			# 计算边界框
			points_array = np.array(points)
			min_x, min_y = np.min(points_array, axis=0).astype(int)
			max_x, max_y = np.max(points_array, axis=0).astype(int)

			# 添加边距
			h, w = original_image.shape[:2]
			min_x = max(0, min_x - self.__margin)
			min_y = max(0, min_y - self.__margin)
			max_x = min(w, max_x + self.__margin)
			max_y = min(h, max_y + self.__margin)

			# 裁剪图像
			cropped_image = original_image[min_y:max_y, min_x:max_x]

			# 调整坐标到裁剪后的图像
			adjusted_points = [(x - min_x, y - min_y) for x, y in points]

			# 创建多边形掩码
			mask = np.zeros(cropped_image.shape[:2], dtype=np.uint8)
			cv2.fillPoly(mask, [np.array(adjusted_points, dtype=np.int32)], 255)

			# 应用掩码（多边形外部填充黑色）
			masked_image = cropped_image.copy()
			masked_image[mask == 0] = [0, 0, 0]  # 黑色填充

			# 标准化尺寸
			resized_image = cv2.resize(masked_image, self.__target_size, interpolation=cv2.INTER_AREA)

			# 缓存结果
			with self.__cache_lock:
				if len(self.__image_cache) >= self.__max_cache_size:
					# 简单的LRU：删除第一个元素
					first_key = next(iter(self.__image_cache))
					del self.__image_cache[first_key]

				self.__image_cache[cache_key] = resized_image

			return resized_image

		except Exception as e:
			self.__logger.error(f"图像裁剪预处理失败: {str(e)}")
			return None

	def __generate_cache_key(self, image_path: str, points: List[List[float]]) -> str:
		"""生成缓存键"""
		# 使用图像路径和坐标点生成唯一键
		points_str = json.dumps(points, sort_keys=True)
		content = f"{image_path}:{points_str}:{self.__target_size}:{self.__margin}"
		return hashlib.md5(content.encode()).hexdigest()

	def __create_result(self, decision: str, reason: str, processing_time: float = 0.0, **kwargs) -> Dict[str, Any]:
		"""创建标准化的结果字典"""
		result = {
			'ssim_similarity': 0.0,
			'histogram_similarity': 0.0,
			'feature_similarity': 0.0,
			'contour_similarity': 0.0,
			'pixel_similarity': 0.0,
			'comprehensive_similarity': 0.0,
			'decision': decision,
			'reason': reason,
			'processing_time': processing_time
		}
		result.update(kwargs)
		return result

	def __calculate_ssim_similarity(self, image1: np.ndarray, image2: np.ndarray) -> float:
		"""
		计算SSIM结构相似性指数（改进版）

		使用多尺度SSIM和增强的结构差异检测，解决了原有方法在图像差异较大但重叠率高时相似度虚高的问题
		包含全局SSIM、局部SSIM方差分析、多尺度SSIM和结构差异惩罚

		参数:
			image1: 第一幅图像
			image2: 第二幅图像

		返回:
			SSIM相似度 (0.0-1.0)
		"""
		try:
			from skimage.metrics import structural_similarity as ssim

			# 转换为灰度图像
			gray1 = cv2.cvtColor(image1, cv2.COLOR_RGB2GRAY)
			gray2 = cv2.cvtColor(image2, cv2.COLOR_RGB2GRAY)

			# 方法1: 全局SSIM（改进的转换方式）
			ssim_result = ssim(gray1, gray2, data_range=255)
			if isinstance(ssim_result, tuple):
				global_ssim = ssim_result[0]
			else:
				global_ssim = ssim_result

			# 只保留正SSIM值，负值表示结构差异很大
			global_ssim_similarity = max(0.0, global_ssim)

			# 方法2: 局部SSIM方差分析
			local_ssim_variance = self.__calculate_local_ssim_variance(gray1, gray2)

			# 方法3: 多尺度SSIM
			multiscale_ssim = self.__calculate_multiscale_ssim(gray1, gray2)

			# 方法4: 结构差异惩罚
			structure_penalty = self.__calculate_structure_penalty(gray1, gray2)

			# 加权组合多种SSIM度量（使用可配置权重）
			enhanced_ssim = (
				self.__ssim_global_weight * global_ssim_similarity +
				self.__ssim_local_weight * (1.0 - local_ssim_variance) +  # 方差越小越相似
				self.__ssim_multiscale_weight * multiscale_ssim +
				self.__ssim_structure_weight * (1.0 - structure_penalty)   # 惩罚越小越相似
			)

			return float(max(0.0, min(1.0, enhanced_ssim)))

		except ImportError:
			# scikit-image不可用时的降级方案
			self.__logger.warning("scikit-image不可用，使用简化的SSIM计算")
			return self.__calculate_simple_ssim(image1, image2)
		except Exception as e:
			self.__logger.warning(f"SSIM计算失败: {str(e)}")
			return 0.0

	def __calculate_local_ssim_variance(self, gray1: np.ndarray, gray2: np.ndarray) -> float:
		"""
		计算局部SSIM的方差，用于检测局部差异

		参数:
			gray1: 第一幅灰度图像
			gray2: 第二幅灰度图像

		返回:
			局部SSIM方差 (0.0-1.0)，值越大表示局部差异越大
		"""
		try:
			from skimage.metrics import structural_similarity as ssim

			# 将图像分割成多个窗口
			h, w = gray1.shape
			window_size = min(32, h//4, w//4)  # 动态窗口大小
			if window_size < 8:
				return 0.0  # 图像太小，无法进行局部分析

			local_ssims = []

			# 滑动窗口计算局部SSIM
			for i in range(0, h - window_size + 1, window_size//2):
				for j in range(0, w - window_size + 1, window_size//2):
					window1 = gray1[i:i+window_size, j:j+window_size]
					window2 = gray2[i:i+window_size, j:j+window_size]

					if window1.size > 0 and window2.size > 0:
						local_ssim = ssim(window1, window2, data_range=255)
						if isinstance(local_ssim, tuple):
							local_ssim = local_ssim[0]
						local_ssims.append(local_ssim)

			if len(local_ssims) == 0:
				return 0.0

			# 计算局部SSIM的方差
			local_ssims = np.array(local_ssims)
			variance = np.var(local_ssims)

			# 归一化方差到[0,1]范围
			normalized_variance = min(1.0, variance / 0.5)  # 0.5是经验最大方差值

			return float(normalized_variance)

		except Exception:
			return 0.0

	def __calculate_multiscale_ssim(self, gray1: np.ndarray, gray2: np.ndarray) -> float:
		"""
		计算多尺度SSIM，在不同分辨率下评估相似性

		参数:
			gray1: 第一幅灰度图像
			gray2: 第二幅灰度图像

		返回:
			多尺度SSIM相似度 (0.0-1.0)
		"""
		try:
			from skimage.metrics import structural_similarity as ssim

			scales = []
			current_gray1, current_gray2 = gray1.copy(), gray2.copy()

			# 在多个尺度上计算SSIM
			for scale in range(3):  # 3个尺度
				if current_gray1.shape[0] < 16 or current_gray1.shape[1] < 16:
					break

				# 计算当前尺度的SSIM
				scale_ssim = ssim(current_gray1, current_gray2, data_range=255)
				if isinstance(scale_ssim, tuple):
					scale_ssim = scale_ssim[0]

				# 只保留正值
				scales.append(max(0.0, scale_ssim))

				# 下采样到下一个尺度
				if scale < 2:  # 避免最后一次不必要的下采样
					current_gray1 = cv2.resize(current_gray1,
						(current_gray1.shape[1]//2, current_gray1.shape[0]//2),
						interpolation=cv2.INTER_AREA)
					current_gray2 = cv2.resize(current_gray2,
						(current_gray2.shape[1]//2, current_gray2.shape[0]//2),
						interpolation=cv2.INTER_AREA)

			if len(scales) == 0:
				return 0.0

			# 加权平均（高分辨率权重更高）
			weights = [0.5, 0.3, 0.2][:len(scales)]
			weights = weights[:len(scales)]
			if len(weights) < len(scales):
				weights.extend([0.1] * (len(scales) - len(weights)))

			# 归一化权重
			total_weight = sum(weights)
			if total_weight > 0:
				weights = [w/total_weight for w in weights]
				multiscale_ssim = sum(s * w for s, w in zip(scales, weights))
			else:
				multiscale_ssim = np.mean(scales)

			return float(max(0.0, min(1.0, multiscale_ssim)))

		except Exception:
			return 0.0

	def __calculate_structure_penalty(self, gray1: np.ndarray, gray2: np.ndarray) -> float:
		"""
		计算结构差异惩罚，检测显著的结构性差异

		参数:
			gray1: 第一幅灰度图像
			gray2: 第二幅灰度图像

		返回:
			结构差异惩罚 (0.0-1.0)，值越大表示结构差异越大
		"""
		try:
			# 计算梯度
			grad1_x = cv2.Sobel(gray1, cv2.CV_64F, 1, 0, ksize=3)
			grad1_y = cv2.Sobel(gray1, cv2.CV_64F, 0, 1, ksize=3)
			grad2_x = cv2.Sobel(gray2, cv2.CV_64F, 1, 0, ksize=3)
			grad2_y = cv2.Sobel(gray2, cv2.CV_64F, 0, 1, ksize=3)

			# 计算梯度幅值
			grad1_mag = np.sqrt(grad1_x**2 + grad1_y**2)
			grad2_mag = np.sqrt(grad2_x**2 + grad2_y**2)

			# 计算梯度差异
			grad_diff = np.abs(grad1_mag - grad2_mag)

			# 归一化梯度差异
			max_grad = max(np.max(grad1_mag), np.max(grad2_mag))
			if max_grad > 0:
				normalized_diff = grad_diff / max_grad
			else:
				normalized_diff = np.zeros_like(grad_diff)

			# 计算平均结构差异
			structure_penalty = np.mean(normalized_diff)

			return float(max(0.0, min(1.0, structure_penalty)))

		except Exception:
			return 0.0

	def __calculate_simple_ssim(self, image1: np.ndarray, image2: np.ndarray) -> float:
		"""简化的SSIM计算（不依赖scikit-image）"""
		try:
			# 转换为灰度
			gray1 = cv2.cvtColor(image1, cv2.COLOR_RGB2GRAY).astype(np.float64)
			gray2 = cv2.cvtColor(image2, cv2.COLOR_RGB2GRAY).astype(np.float64)

			# 计算均值
			mu1 = np.mean(gray1)
			mu2 = np.mean(gray2)

			# 计算方差和协方差
			sigma1_sq = np.var(gray1)
			sigma2_sq = np.var(gray2)
			sigma12 = np.mean((gray1 - mu1) * (gray2 - mu2))

			# SSIM常数
			c1 = (0.01 * 255) ** 2
			c2 = (0.03 * 255) ** 2

			# 计算SSIM
			numerator = (2 * mu1 * mu2 + c1) * (2 * sigma12 + c2)
			denominator = (mu1**2 + mu2**2 + c1) * (sigma1_sq + sigma2_sq + c2)

			ssim_value = numerator / denominator

			# 转换到[0,1]范围
			return float(max(0.0, min(1.0, (ssim_value + 1) / 2)))

		except Exception:
			return 0.0

	def __calculate_histogram_similarity(self, image1: np.ndarray, image2: np.ndarray) -> float:
		"""
		计算直方图相似度（精确优化版）

		使用最精确的3种专业方法组合：OpenCV巴氏距离、Jensen-Shannon散度、OpenCV卡方检验
		确保图像差异较大时得到合理的低相似度，避免虚高问题

		参数:
			image1: 第一幅图像
			image2: 第二幅图像

		返回:
			直方图相似度 (0.0-1.0)
		"""
		try:
			# 使用OpenCV计算RGB直方图
			hist1_r = cv2.calcHist([image1], [0], None, [256], [0, 256])
			hist1_g = cv2.calcHist([image1], [1], None, [256], [0, 256])
			hist1_b = cv2.calcHist([image1], [2], None, [256], [0, 256])

			hist2_r = cv2.calcHist([image2], [0], None, [256], [0, 256])
			hist2_g = cv2.calcHist([image2], [1], None, [256], [0, 256])
			hist2_b = cv2.calcHist([image2], [2], None, [256], [0, 256])

			# 方法1: OpenCV巴氏距离（最精确的分布差异检测）
			bhatta_r = cv2.compareHist(hist1_r, hist2_r, cv2.HISTCMP_BHATTACHARYYA)
			bhatta_g = cv2.compareHist(hist1_g, hist2_g, cv2.HISTCMP_BHATTACHARYYA)
			bhatta_b = cv2.compareHist(hist1_b, hist2_b, cv2.HISTCMP_BHATTACHARYYA)
			# 巴氏距离越小越相似，使用更严格的转换
			avg_bhatta = (bhatta_r + bhatta_g + bhatta_b) / 3
			bhattacharyya_similarity = np.exp(-avg_bhatta * 3.0)  # 增强敏感度

			# 方法2: Jensen-Shannon散度（统计学最严格的距离度量）
			js_similarity = self.__calculate_jensen_shannon_similarity(
				hist1_r, hist1_g, hist1_b, hist2_r, hist2_g, hist2_b)

			# 方法3: OpenCV卡方检验（辅助验证）
			chi2_r = cv2.compareHist(hist1_r, hist2_r, cv2.HISTCMP_CHISQR)
			chi2_g = cv2.compareHist(hist1_g, hist2_g, cv2.HISTCMP_CHISQR)
			chi2_b = cv2.compareHist(hist1_b, hist2_b, cv2.HISTCMP_CHISQR)
			# 使用更严格的卡方转换
			avg_chi2 = (chi2_r + chi2_g + chi2_b) / 3
			chi_square_similarity = np.exp(-avg_chi2 / 500.0)  # 更严格的衰减

			# 精确的3方法组合（严格权重）
			histogram_similarity = (
				0.50 * bhattacharyya_similarity +    # 巴氏距离权重最高（50%）
				0.35 * js_similarity +               # JS散度权重（35%）
				0.15 * chi_square_similarity         # 卡方检验权重（15%）
			)

			# 应用严格性惩罚：如果任何一个方法显示高差异，则大幅降低相似度
			min_similarity = min(bhattacharyya_similarity, js_similarity, chi_square_similarity)
			if min_similarity < 0.3:  # 如果任何方法显示低相似度
				histogram_similarity = histogram_similarity * 0.5  # 惩罚50%

			return float(max(0.0, min(1.0, histogram_similarity)))

		except Exception as e:
			self.__logger.warning(f"直方图相似度计算失败: {str(e)}")
			return 0.0

	def __calculate_jensen_shannon_similarity(self, hist1_r, hist1_g, hist1_b, hist2_r, hist2_g, hist2_b) -> float:
		"""
		使用Jensen-Shannon散度计算精确的统计距离相似度

		Jensen-Shannon散度是最严格的概率分布距离度量，对分布差异极其敏感

		参数:
			hist1_r, hist1_g, hist1_b: 第一幅图像的RGB直方图
			hist2_r, hist2_g, hist2_b: 第二幅图像的RGB直方图

		返回:
			Jensen-Shannon相似度 (0.0-1.0)
		"""
		try:
			from scipy.spatial.distance import jensenshannon

			# 归一化直方图为概率分布
			hist1_r_norm = hist1_r.flatten() / (np.sum(hist1_r) + 1e-10)
			hist1_g_norm = hist1_g.flatten() / (np.sum(hist1_g) + 1e-10)
			hist1_b_norm = hist1_b.flatten() / (np.sum(hist1_b) + 1e-10)

			hist2_r_norm = hist2_r.flatten() / (np.sum(hist2_r) + 1e-10)
			hist2_g_norm = hist2_g.flatten() / (np.sum(hist2_g) + 1e-10)
			hist2_b_norm = hist2_b.flatten() / (np.sum(hist2_b) + 1e-10)

			# 计算Jensen-Shannon散度
			js_r = jensenshannon(hist1_r_norm, hist2_r_norm)
			js_g = jensenshannon(hist1_g_norm, hist2_g_norm)
			js_b = jensenshannon(hist1_b_norm, hist2_b_norm)

			# 平均JS散度
			avg_js = (js_r + js_g + js_b) / 3

			# 转换为相似度，使用更严格的转换函数
			# JS散度范围是[0,1]，使用指数函数增强敏感性
			js_similarity = np.exp(-avg_js * 4.0)  # 严格的指数衰减

			return float(max(0.0, min(1.0, js_similarity)))

		except ImportError:
			# scipy不可用时的自实现JS散度
			self.__logger.warning("scipy不可用，使用自实现的JS散度")
			return self.__calculate_js_similarity_manual(hist1_r, hist1_g, hist1_b, hist2_r, hist2_g, hist2_b)
		except Exception:
			return 0.0

	def __calculate_js_similarity_manual(self, hist1_r, hist1_g, hist1_b, hist2_r, hist2_g, hist2_b) -> float:
		"""
		手动实现Jensen-Shannon散度（scipy不可用时的降级方案）
		"""
		try:
			# 归一化直方图
			hist1_r_norm = hist1_r.flatten() / (np.sum(hist1_r) + 1e-10)
			hist1_g_norm = hist1_g.flatten() / (np.sum(hist1_g) + 1e-10)
			hist1_b_norm = hist1_b.flatten() / (np.sum(hist1_b) + 1e-10)

			hist2_r_norm = hist2_r.flatten() / (np.sum(hist2_r) + 1e-10)
			hist2_g_norm = hist2_g.flatten() / (np.sum(hist2_g) + 1e-10)
			hist2_b_norm = hist2_b.flatten() / (np.sum(hist2_b) + 1e-10)

			def kl_divergence(p, q):
				"""计算KL散度"""
				epsilon = 1e-10
				p = p + epsilon
				q = q + epsilon
				return np.sum(p * np.log(p / q))

			def js_divergence(p, q):
				"""计算JS散度"""
				m = 0.5 * (p + q)
				return 0.5 * kl_divergence(p, m) + 0.5 * kl_divergence(q, m)

			# 计算各通道的JS散度
			js_r = js_divergence(hist1_r_norm, hist2_r_norm)
			js_g = js_divergence(hist1_g_norm, hist2_g_norm)
			js_b = js_divergence(hist1_b_norm, hist2_b_norm)

			# 平均JS散度并转换为相似度
			avg_js = (js_r + js_g + js_b) / 3
			js_similarity = np.exp(-avg_js * 2.0)  # 手动实现使用稍微宽松的参数

			return float(max(0.0, min(1.0, js_similarity)))

		except Exception:
			return 0.0

	def __calculate_feature_similarity(self, image1: np.ndarray, image2: np.ndarray) -> float:
		"""
		计算特征点匹配相似度

		使用ORB特征提取器进行特征点检测和匹配，适用于纹理丰富的图像区域

		参数:
			image1: 第一幅图像
			image2: 第二幅图像

		返回:
			特征点相似度 (0.0-1.0)
		"""
		try:
			# 转换为灰度图像
			gray1 = cv2.cvtColor(image1, cv2.COLOR_RGB2GRAY)
			gray2 = cv2.cvtColor(image2, cv2.COLOR_RGB2GRAY)

			# 创建ORB特征检测器
			orb = cv2.ORB_create(nfeatures=500)

			# 检测特征点和描述符
			kp1, des1 = orb.detectAndCompute(gray1, None)
			kp2, des2 = orb.detectAndCompute(gray2, None)

			if des1 is None or des2 is None or len(des1) == 0 or len(des2) == 0:
				# 没有检测到特征点，使用模板匹配作为降级方案
				return self.__calculate_template_matching_similarity(gray1, gray2)

			# 创建BF匹配器
			bf = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)

			# 匹配特征点
			matches = bf.match(des1, des2)

			if len(matches) == 0:
				return 0.0

			# 计算匹配质量
			distances = [m.distance for m in matches]
			avg_distance = np.mean(distances)
			max_distance = 100.0  # ORB描述符的最大距离

			# 转换为相似度
			distance_similarity = 1.0 - (avg_distance / max_distance)

			# 计算匹配比例
			total_features = min(len(kp1), len(kp2))
			match_ratio = len(matches) / total_features if total_features > 0 else 0.0

			# 综合特征相似度
			feature_similarity = 0.7 * distance_similarity + 0.3 * match_ratio

			return float(max(0.0, min(1.0, feature_similarity)))

		except Exception as e:
			self.__logger.warning(f"特征点相似度计算失败: {str(e)}")
			return 0.0

	def __calculate_template_matching_similarity(self, gray1: np.ndarray, gray2: np.ndarray) -> float:
		"""模板匹配相似度（特征点检测失败时的降级方案）"""
		try:
			# 使用较小的图像作为模板
			if gray1.size <= gray2.size:
				template, target = gray1, gray2
			else:
				template, target = gray2, gray1

			# 模板匹配
			result = cv2.matchTemplate(target, template, cv2.TM_CCOEFF_NORMED)
			max_val = np.max(result)

			return float(max(0.0, min(1.0, max_val)))

		except Exception:
			return 0.0

	def __calculate_contour_similarity(self, image1: np.ndarray, image2: np.ndarray) -> float:
		"""
		计算轮廓形状相似度

		提取图像中的主要轮廓，使用Hu矩进行形状分析，专注于形状特征而忽略颜色信息

		参数:
			image1: 第一幅图像
			image2: 第二幅图像

		返回:
			轮廓相似度 (0.0-1.0)
		"""
		try:
			# 转换为灰度图像
			gray1 = cv2.cvtColor(image1, cv2.COLOR_RGB2GRAY)
			gray2 = cv2.cvtColor(image2, cv2.COLOR_RGB2GRAY)

			# 边缘检测
			edges1 = cv2.Canny(gray1, 50, 150)
			edges2 = cv2.Canny(gray2, 50, 150)

			# 查找轮廓
			contours1, _ = cv2.findContours(edges1, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
			contours2, _ = cv2.findContours(edges2, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

			if len(contours1) == 0 or len(contours2) == 0:
				# 没有找到轮廓，使用边缘相似度
				return self.__calculate_edge_similarity(edges1, edges2)

			# 找到最大的轮廓
			largest_contour1 = max(contours1, key=cv2.contourArea)
			largest_contour2 = max(contours2, key=cv2.contourArea)

			# 计算Hu矩
			moments1 = cv2.moments(largest_contour1)
			moments2 = cv2.moments(largest_contour2)

			if moments1['m00'] == 0 or moments2['m00'] == 0:
				return 0.0

			hu_moments1 = cv2.HuMoments(moments1).flatten()
			hu_moments2 = cv2.HuMoments(moments2).flatten()

			# 对数变换（Hu矩可能有很大的值）
			hu_moments1 = -np.sign(hu_moments1) * np.log10(np.abs(hu_moments1) + 1e-10)
			hu_moments2 = -np.sign(hu_moments2) * np.log10(np.abs(hu_moments2) + 1e-10)

			# 计算欧几里得距离
			distance = np.linalg.norm(hu_moments1 - hu_moments2)

			# 转换为相似度
			max_distance = 10.0  # 经验值
			contour_similarity = 1.0 / (1.0 + distance / max_distance)

			return float(max(0.0, min(1.0, contour_similarity)))

		except Exception as e:
			self.__logger.warning(f"轮廓相似度计算失败: {str(e)}")
			return 0.0

	def __calculate_edge_similarity(self, edges1: np.ndarray, edges2: np.ndarray) -> float:
		"""边缘相似度计算（轮廓检测失败时的降级方案）"""
		try:
			# 计算边缘像素的重叠度
			intersection = np.logical_and(edges1, edges2)
			union = np.logical_or(edges1, edges2)

			intersection_count = np.sum(intersection)
			union_count = np.sum(union)

			if union_count == 0:
				return 1.0 if intersection_count == 0 else 0.0

			edge_similarity = intersection_count / union_count
			return float(edge_similarity)

		except Exception:
			return 0.0

	def __calculate_pixel_similarity(self, image1: np.ndarray, image2: np.ndarray) -> float:
		"""
		计算像素级相似度

		使用峰值信噪比(PSNR)进行像素级对比，对图像的微小变化敏感

		参数:
			image1: 第一幅图像
			image2: 第二幅图像

		返回:
			像素级相似度 (0.0-1.0)
		"""
		try:
			# 计算均方误差(MSE)
			mse = np.mean((image1.astype(np.float64) - image2.astype(np.float64)) ** 2)

			if mse == 0:
				return 1.0  # 图像完全相同

			# 计算PSNR
			max_pixel_value = 255.0
			psnr = 20 * np.log10(max_pixel_value / np.sqrt(mse))

			# 将PSNR转换为相似度
			# PSNR范围通常是0-100，高质量图像的PSNR > 30
			max_psnr = 50.0  # 设定最大PSNR值
			pixel_similarity = min(psnr / max_psnr, 1.0)

			return float(max(0.0, pixel_similarity))

		except Exception as e:
			self.__logger.warning(f"像素级相似度计算失败: {str(e)}")
			return 0.0

	def __make_similarity_decision(
			self,
			ssim_sim: float,
			hist_sim: float,
			feature_sim: float,
			contour_sim: float,
			pixel_sim: float,
			comprehensive_sim: float
	) -> Tuple[str, str]:
		"""
		多层次相似度决策机制

		决策逻辑:
		1. 极高相似度（≥0.95）→ 跳过
		2. 高相似度（≥0.85）→ 跳过
		3. 中等相似度（0.70-0.85）→ 细化判断
		4. 低相似度（<0.50）→ 追加

		参数:
			ssim_sim: SSIM相似度
			hist_sim: 直方图相似度
			feature_sim: 特征点相似度
			contour_sim: 轮廓相似度
			pixel_sim: 像素级相似度
			comprehensive_sim: 综合相似度

		返回:
			(决策, 原因) 元组
		"""
		try:
			# 第一层：极高相似度检查
			if comprehensive_sim >= self.__extreme_similarity_threshold:
				return 'skip', f'极高相似度 (综合={comprehensive_sim:.3f})'

			# 第二层：高相似度检查
			if comprehensive_sim >= self.__high_similarity_threshold:
				return 'skip', f'高相似度 (综合={comprehensive_sim:.3f})'

			# 第三层：低相似度检查
			if comprehensive_sim < self.__low_similarity_threshold:
				return 'add', f'低相似度，可以追加 (综合={comprehensive_sim:.3f})'

			# 第四层：中等相似度的细化判断
			return self.__refined_similarity_decision(
				ssim_sim, hist_sim, feature_sim, contour_sim, pixel_sim, comprehensive_sim
			)

		except Exception as e:
			self.__logger.warning(f"相似度决策失败: {str(e)}")
			return 'add', f'决策失败，默认追加: {str(e)}'

	def __refined_similarity_decision(
			self,
			ssim_sim: float,
			hist_sim: float,
			feature_sim: float,
			contour_sim: float,
			pixel_sim: float,
			comprehensive_sim: float
	) -> Tuple[str, str]:
		"""
		细化判断流程（中等相似度范围）

		检查规则:
		1. 单维度高相似度检查
		2. 多维度一致性验证
		3. 特殊情况处理
		"""
		try:
			# 第一层：极高单维度相似度检查（更严格）
			if (ssim_sim >= self.__refined_high_threshold or
				contour_sim >= self.__refined_high_threshold or
				pixel_sim >= self.__refined_high_threshold):
				return 'skip', f'单维度极高相似度 (SSIM={ssim_sim:.3f}, 轮廓={contour_sim:.3f}, 像素={pixel_sim:.3f})'

			# 第二层：多维度一致性验证（更严格：需要4个维度≥75%）
			high_similarity_count = sum([
				ssim_sim >= self.__refined_medium_threshold,
				hist_sim >= self.__refined_medium_threshold,
				feature_sim >= self.__refined_medium_threshold,
				contour_sim >= self.__refined_medium_threshold,
				pixel_sim >= self.__refined_medium_threshold
			])

			if high_similarity_count >= self.__refined_multi_dimension_count:
				return 'skip', f'多维度一致性相似 (≥{self.__refined_multi_dimension_count}个维度>{self.__refined_medium_threshold:.1f})'

			# 第三层：核心维度组合检查（SSIM+轮廓+像素）
			core_dimensions_high = sum([
				ssim_sim >= self.__refined_special_ssim_threshold,
				contour_sim >= self.__refined_special_contour_threshold,
				pixel_sim >= self.__refined_core_pixel_threshold
			])
			if core_dimensions_high >= 3:
				return 'skip', f'核心维度组合相似 (SSIM={ssim_sim:.3f}, 轮廓={contour_sim:.3f}, 像素={pixel_sim:.3f})'

			# 第四层：结构+形状双重验证（更严格的特殊情况）
			if (ssim_sim >= self.__refined_special_ssim_threshold and
				contour_sim >= self.__refined_special_contour_threshold and
				(hist_sim >= self.__refined_double_verify_hist_threshold or
				 feature_sim >= self.__refined_double_verify_feature_threshold)):
				return 'skip', f'结构形状双重相似 (SSIM={ssim_sim:.3f}, 轮廓={contour_sim:.3f}, 直方图={hist_sim:.3f}, 特征={feature_sim:.3f})'

			# 第五层：综合相似度严格检查
			if comprehensive_sim >= self.__refined_comprehensive_threshold:
				return 'skip', f'综合相似度过高 (综合={comprehensive_sim:.3f})'

			# 默认：追加
			return 'add', f'中等相似度，可以追加 (综合={comprehensive_sim:.3f})'

		except Exception:
			return 'add', f'细化判断失败，默认追加 (综合={comprehensive_sim:.3f})'

	def configure_similarity_weights(
			self,
			ssim_weight: float = 0.30,
			histogram_weight: float = 0.25,
			feature_weight: float = 0.20,
			contour_weight: float = 0.15,
			pixel_weight: float = 0.10
	):
		"""
		配置相似度算法权重

		参数:
			ssim_weight: SSIM权重，默认30%
			histogram_weight: 直方图权重，默认25%
			feature_weight: 特征点权重，默认20%
			contour_weight: 轮廓权重，默认15%
			pixel_weight: 像素级权重，默认10%

		使用示例:
			# 提高结构相似度的权重
			processor.configure_similarity_weights(
				ssim_weight=0.40,
				histogram_weight=0.20,
				feature_weight=0.20,
				contour_weight=0.15,
				pixel_weight=0.05
			)
		"""
		# 验证权重总和
		total_weight = ssim_weight + histogram_weight + feature_weight + contour_weight + pixel_weight
		if abs(total_weight - 1.0) > 0.01:
			self.__logger.warning(f"权重总和为{total_weight:.3f}，建议调整为1.0")

		# 更新权重
		self.__ssim_weight = ssim_weight
		self.__histogram_weight = histogram_weight
		self.__feature_weight = feature_weight
		self.__contour_weight = contour_weight
		self.__pixel_weight = pixel_weight

		self.__logger.info(
			f"相似度权重已更新: SSIM={ssim_weight:.2f}, 直方图={histogram_weight:.2f}, "
			f"特征={feature_weight:.2f}, 轮廓={contour_weight:.2f}, 像素={pixel_weight:.2f}"
		)

		if self.__log_output:
			self.__log_output.append(
				f"图像相似度权重已更新: SSIM={ssim_weight:.2f}, 直方图={histogram_weight:.2f}, "
				f"特征={feature_weight:.2f}, 轮廓={contour_weight:.2f}, 像素={pixel_weight:.2f}",
				Colors.GREEN
			)

	def configure_decision_thresholds(
			self,
			extreme_similarity_threshold: float = 0.95,
			high_similarity_threshold: float = 0.85,
			medium_similarity_threshold: float = 0.70,
			low_similarity_threshold: float = 0.50
	):
		"""
		配置决策阈值

		参数:
			extreme_similarity_threshold: 极高相似度阈值，默认95%
			high_similarity_threshold: 高相似度阈值，默认85%
			medium_similarity_threshold: 中等相似度阈值，默认70%
			low_similarity_threshold: 低相似度阈值，默认50%

		使用示例:
			# 设置更严格的阈值
			processor.configure_decision_thresholds(
				extreme_similarity_threshold=0.98,
				high_similarity_threshold=0.90,
				low_similarity_threshold=0.40
			)
		"""
		self.__extreme_similarity_threshold = extreme_similarity_threshold
		self.__high_similarity_threshold = high_similarity_threshold
		self.__medium_similarity_threshold = medium_similarity_threshold
		self.__low_similarity_threshold = low_similarity_threshold

		self.__logger.info(
			f"决策阈值已更新: 极高≥{extreme_similarity_threshold:.2f}, "
			f"高≥{high_similarity_threshold:.2f}, 中等≥{medium_similarity_threshold:.2f}, "
			f"低<{low_similarity_threshold:.2f}"
		)

		if self.__log_output:
			self.__log_output.append(
				f"图像相似度阈值已更新: 极高≥{extreme_similarity_threshold:.0%}, "
				f"高≥{high_similarity_threshold:.0%}, 低<{low_similarity_threshold:.0%}",
				Colors.GREEN
			)

	def configure_refined_decision_thresholds(
			self,
			refined_high_threshold: float = 0.95,
			refined_medium_threshold: float = 0.75,
			refined_special_ssim_threshold: float = 0.85,
			refined_special_contour_threshold: float = 0.85,
			refined_multi_dimension_count: int = 4,
			refined_core_pixel_threshold: float = 0.80,
			refined_double_verify_hist_threshold: float = 0.70,
			refined_double_verify_feature_threshold: float = 0.70,
			refined_comprehensive_threshold: float = 0.82
	):
		"""
		配置细化判断阈值（默认为严格模式）

		参数:
			refined_high_threshold: 细化判断中的单维度高相似度阈值，默认95%（严格）
			refined_medium_threshold: 细化判断中的多维度一致性阈值，默认75%（严格）
			refined_special_ssim_threshold: 细化判断中的特殊情况SSIM阈值，默认85%（严格）
			refined_special_contour_threshold: 细化判断中的特殊情况轮廓阈值，默认85%（严格）
			refined_multi_dimension_count: 细化判断中的多维度一致性最小数量，默认4个（严格）
			refined_core_pixel_threshold: 核心维度检查中的像素阈值，默认80%
			refined_double_verify_hist_threshold: 双重验证中的直方图阈值，默认70%
			refined_double_verify_feature_threshold: 双重验证中的特征阈值，默认70%
			refined_comprehensive_threshold: 综合相似度严格检查阈值，默认82%

		严格判断包含五层检查:
			1. 极高单维度相似度检查（≥95%）
			2. 多维度一致性验证（≥4个维度≥75%）
			3. 核心维度组合检查（SSIM≥85%+轮廓≥85%+像素≥80%）
			4. 结构形状双重验证（SSIM≥85%+轮廓≥85%+直方图≥70%或特征≥70%）
			5. 综合相似度严格检查（≥82%）

		使用示例:
			# 设置更宽松的细化判断阈值
			processor.configure_refined_decision_thresholds(
				refined_high_threshold=0.90,
				refined_medium_threshold=0.70,
				refined_special_ssim_threshold=0.80,
				refined_special_contour_threshold=0.80,
				refined_multi_dimension_count=3,
				refined_core_pixel_threshold=0.75,
				refined_double_verify_hist_threshold=0.65,
				refined_double_verify_feature_threshold=0.65,
				refined_comprehensive_threshold=0.78
			)
		"""
		self.__refined_high_threshold = refined_high_threshold
		self.__refined_medium_threshold = refined_medium_threshold
		self.__refined_special_ssim_threshold = refined_special_ssim_threshold
		self.__refined_special_contour_threshold = refined_special_contour_threshold
		self.__refined_multi_dimension_count = refined_multi_dimension_count
		self.__refined_core_pixel_threshold = refined_core_pixel_threshold
		self.__refined_double_verify_hist_threshold = refined_double_verify_hist_threshold
		self.__refined_double_verify_feature_threshold = refined_double_verify_feature_threshold
		self.__refined_comprehensive_threshold = refined_comprehensive_threshold

		self.__logger.info(
			f"细化判断阈值已更新: 单维度高≥{refined_high_threshold:.2f}, "
			f"多维度≥{refined_medium_threshold:.2f}, SSIM特殊≥{refined_special_ssim_threshold:.2f}, "
			f"轮廓特殊≥{refined_special_contour_threshold:.2f}, 多维度数量≥{refined_multi_dimension_count}, "
			f"核心像素≥{refined_core_pixel_threshold:.2f}, 双重直方图≥{refined_double_verify_hist_threshold:.2f}, "
			f"双重特征≥{refined_double_verify_feature_threshold:.2f}, 综合≥{refined_comprehensive_threshold:.2f}"
		)

		if self.__log_output:
			self.__log_output.append(
				f"细化判断阈值已更新: 单维度高≥{refined_high_threshold:.0%}, "
				f"多维度≥{refined_medium_threshold:.0%}, 综合≥{refined_comprehensive_threshold:.0%}",
				Colors.GREEN
			)

	def configure_ssim_weights(
			self,
			global_weight: float = 0.40,
			local_weight: float = 0.25,
			multiscale_weight: float = 0.25,
			structure_weight: float = 0.10
	):
		"""
		配置SSIM相似度计算中各种方法的权重

		参数:
			global_weight: 全局SSIM权重，默认40%（主要指标）
			local_weight: 局部SSIM方差权重，默认25%（检测局部差异）
			multiscale_weight: 多尺度SSIM权重，默认25%（多分辨率验证）
			structure_weight: 结构差异惩罚权重，默认10%（结构差异检测）

		说明:
			- 全局SSIM提供整体结构相似性评估
			- 局部SSIM方差检测局部区域的不一致性
			- 多尺度SSIM在不同分辨率下验证相似性
			- 结构差异惩罚识别显著的结构性变化

		使用示例:
			# 更严格的SSIM判断（提高局部和结构权重）
			processor.configure_ssim_weights(
				global_weight=0.35,
				local_weight=0.30,
				multiscale_weight=0.25,
				structure_weight=0.10
			)
		"""
		# 验证权重总和
		total_weight = global_weight + local_weight + multiscale_weight + structure_weight
		if abs(total_weight - 1.0) > 0.01:
			self.__logger.warning(f"SSIM权重总和为{total_weight:.3f}，建议调整为1.0")

		# 更新权重
		self.__ssim_global_weight = global_weight
		self.__ssim_local_weight = local_weight
		self.__ssim_multiscale_weight = multiscale_weight
		self.__ssim_structure_weight = structure_weight

		self.__logger.info(
			f"SSIM权重已更新: 全局={global_weight:.2f}, 局部={local_weight:.2f}, "
			f"多尺度={multiscale_weight:.2f}, 结构={structure_weight:.2f}"
		)

		if self.__log_output:
			self.__log_output.append(
				f"SSIM计算权重已更新: 全局={global_weight:.0%}, "
				f"局部={local_weight:.0%}, 多尺度={multiscale_weight:.0%}",
				Colors.GREEN
			)

	def clear_cache(self):
		"""清空图像缓存"""
		with self.__cache_lock:
			self.__image_cache.clear()
		self.__logger.info("图像缓存已清空")

	def get_cache_info(self) -> Dict[str, Any]:
		"""获取缓存信息"""
		with self.__cache_lock:
			return {
				'cache_size': len(self.__image_cache),
				'max_cache_size': self.__max_cache_size,
				'cache_keys': list(self.__image_cache.keys())
			}

	def get_configuration(self) -> Dict[str, Any]:
		"""获取当前配置信息"""
		return {
			'target_size': self.__target_size,
			'margin': self.__margin,
			'weights': {
				'ssim': self.__ssim_weight,
				'histogram': self.__histogram_weight,
				'feature': self.__feature_weight,
				'contour': self.__contour_weight,
				'pixel': self.__pixel_weight
			},
			'thresholds': {
				'extreme_similarity': self.__extreme_similarity_threshold,
				'high_similarity': self.__high_similarity_threshold,
				'medium_similarity': self.__medium_similarity_threshold,
				'low_similarity': self.__low_similarity_threshold
			}
		}
