# 复杂条件表达式功能说明

## 概述

`ConditionalFileBatchDeleter` 类现已支持复杂条件表达式，可以使用逻辑运算符（AND、OR、NOT）和括号来构建复杂的文件筛选条件。

## 功能特性

### 1. 向后兼容性
- 完全兼容原有的简单表达式格式
- 现有的简单条件表达式无需修改即可正常工作

### 2. 支持的逻辑运算符
- **AND**: 逻辑与运算
- **OR**: 逻辑或运算  
- **NOT**: 逻辑非运算

### 3. 运算符优先级
- **括号** `()`: 最高优先级
- **NOT**: 高优先级
- **AND**: 中优先级
- **OR**: 低优先级

### 4. 支持的比较运算符
- `>=`: 大于等于
- `<=`: 小于等于
- `==`: 等于
- `!=`: 不等于
- `>`: 大于
- `<`: 小于

## 表达式示例

### 简单表达式（向后兼容）
```
主任务 >= 1
XXX == 2
YYY > 0
```

### AND 逻辑表达式
```
主任务 >= 1 AND XXX >= 1
主任务 > 1 AND XXX == 0
主任务 == 1 AND XXX == 1 AND YYY == 1
```

### OR 逻辑表达式
```
主任务 >= 3 OR YYY >= 3
XXX == 2 OR YYY == 0
主任务 == 0 OR XXX == 0
```

### NOT 逻辑表达式
```
NOT 主任务 >= 3
NOT XXX == 0
NOT (YYY > 1)
```

### 复杂嵌套表达式
```
(主任务 >= 1 AND XXX >= 1) OR YYY >= 2
NOT (主任务 <= 1) AND (XXX > 0 OR YYY > 0)
((主任务 >= 1 AND XXX <= 1) OR YYY == 0) AND NOT 主任务 == 0
(主任务 == 1 OR 主任务 == 2) AND (XXX >= 1 OR YYY >= 1)
```

## 技术实现

### 1. 词法分析器 (ExpressionTokenizer)
- 将表达式分解为标记序列
- 支持中文标签名、英文标签名、数字、运算符、括号
- 提供详细的语法错误定位

### 2. 递归下降解析器
- 实现标准的递归下降解析算法
- 正确处理运算符优先级和结合性
- 生成抽象语法树 (AST)

### 3. AST 节点类型
- **ComparisonNode**: 比较节点（标签名 运算符 数值）
- **LogicalNode**: 逻辑运算节点（AND、OR、NOT）

### 4. 表达式评估器
- 递归评估 AST 节点
- 支持简单条件和复杂条件的统一处理
- 提供详细的评估日志

## 使用方法

### 1. 表达式验证
```python
deleter = ConditionalFileBatchDeleter()
is_valid, message = deleter.validate_condition_expression("(主任务 >= 1 AND XXX >= 1) OR YYY >= 2")
```

### 2. 预览删除
```python
result = deleter.preview_deletion(folder_path, "(主任务 >= 1 AND XXX >= 1) OR YYY >= 2")
if result["success"]:
    print(f"匹配文件数: {len(result['files_to_delete'])}")
```

### 3. 执行批量删除
```python
result = deleter.execute_batch_deletion(
    folder_path="path/to/json/files",
    condition_expression="(主任务 >= 1 AND XXX >= 1) OR YYY >= 2",
    delete_images=True
)
```

## 错误处理

### 1. 语法错误
- 括号不匹配: `(主任务 >= 1`
- 运算符错误: `主任务 ~= 1`
- 缺少操作数: `主任务 >=`

### 2. 词法错误
- 无法识别的字符
- 无效的数值格式

### 3. 语义错误
- 不支持的运算符
- 空表达式

## 性能优化

### 1. 智能表达式检测
- 自动检测简单表达式和复杂表达式
- 简单表达式使用原有的高效解析方式
- 复杂表达式使用新的 AST 解析器

### 2. 缓存机制
- 解析结果可以缓存重用
- 避免重复的词法分析和语法分析

### 3. 短路求值
- AND 运算：左操作数为 false 时不评估右操作数
- OR 运算：左操作数为 true 时不评估右操作数

## 测试覆盖

### 1. 功能测试
- 简单表达式向后兼容性测试
- AND、OR、NOT 逻辑运算测试
- 复杂嵌套表达式测试
- 运算符优先级测试

### 2. 错误测试
- 语法错误检测测试
- 词法错误检测测试
- 边界条件测试

### 3. 性能测试
- 大量文件批量处理测试
- 复杂表达式解析性能测试

## 注意事项

### 1. 标签名支持
- 支持中文标签名
- 支持英文标签名
- 支持数字和下划线
- 标签名区分大小写

### 2. 数值类型
- 支持整数和浮点数
- 自动类型转换

### 3. 运算符大小写
- 逻辑运算符不区分大小写（AND、and、And 都可以）
- 比较运算符区分大小写

### 4. 空白字符
- 自动忽略多余的空白字符
- 支持任意格式的空白分隔

## 更新日志

### v2.0.0 (2025-01-31)
- ✅ 新增复杂条件表达式支持
- ✅ 实现词法分析器和递归下降解析器
- ✅ 添加 AST 节点类型和表达式评估器
- ✅ 保持完全向后兼容性
- ✅ 添加全面的测试覆盖
- ✅ 优化错误处理和用户反馈
- ✅ 支持运算符优先级和括号嵌套
- ✅ 实现智能表达式类型检测
