import json
import logging
import os
import random
import re
import shutil
import time
from collections import deque
from functools import partial
from typing import Dict, List, Union, Optional, Tuple, Any

import cv2
import matplotlib.patches as patches
import matplotlib.pyplot as plt
import numpy as np
import yaml
from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import QCheckBox, QWidget, QLayout, QLineEdit, QVBoxLayout
from global_tools.ui_tools import (
	CheckBoxManager, QLabelManager,
	LineEditManager, LogOutput,
)
from global_tools.ui_tools import constant
from global_tools.utils import Logger, clean_directory, Colors


def check_lineedit( line_edit_manager: LineEditManager ):
	# Windows路径正则表达式 - 匹配形如C:\路径\文件夹的格式
	windows_path_regex = r'^[a-zA-Z]:\\(?:[^\\/:*?"<>|\r\n]+\\)*[^\\/:*?"<>|\r\n]*$'

	# 比例格式正则表达式 - 匹配形如85:15的格式
	ratio_regex = r'^\d+:\d+$'

	# 为lineEdit_45设置Windows路径验证器(之前已有的)
	line_edit_manager.set_input_validator(
		name="lineEdit_45", validator_type="regexp", pattern=constant.WINDOWS_PATH_REGEX
	)

	# 为lineEdit_6设置Windows路径验证器
	line_edit_manager.set_input_validator( name="lineEdit_6", validator_type="regexp", pattern=windows_path_regex )

	# 为lineEdit_7设置Windows路径验证器
	line_edit_manager.set_input_validator( name="lineEdit_7", validator_type="regexp", pattern=windows_path_regex )

	# 为lineEdit_9设置Windows路径验证器
	line_edit_manager.set_input_validator( name="lineEdit_9", validator_type="regexp", pattern=windows_path_regex )

	# 为lineEdit_8设置比例格式验证器
	line_edit_manager.set_input_validator( name="lineEdit_8", validator_type="regexp", pattern=ratio_regex )

	# 为lineEdit_11设置Windows路径验证器
	line_edit_manager.set_input_validator( name="lineEdit_11", validator_type="regexp", pattern=windows_path_regex )


class CheckboxSelector:
	"""
	复选框选择器，用于在UI中查找选中的QCheckBox控件。

	该类提供了查找选中复选框的功能，可以：
	1. 在特定名称的垂直布局(verticalLayout_2)中查找
	2. 在指定任意名称的布局中查找
	3. 在已有的布局对象中直接查找

	所有方法都会排除特定文本的复选框(默认为"全选/取消全选")，并返回选中复选框的objectName列表。

	使用示例:
	```python
	# 创建选择器实例
	selector = CheckboxSelector()

	# 在verticalLayout_2中查找选中的复选框
	checkboxes1 = selector.get_checked_checkboxes(main_window, layout_type="vertical_layout_2")

	# 在特定名称的布局中查找选中的复选框
	checkboxes2 = selector.get_checked_checkboxes(main_window, layout_name="grid_layout_1")

	# 在已有的布局对象中查找选中的复选框
	checkboxes3 = selector.get_checked_checkboxes(layout_object=existing_layout)
	```
	"""

	def __init__( self, exclude_texts: List[ str ] = None ):
		"""
		初始化复选框选择器。

		Args:
			exclude_texts (List[str], optional): 要排除的复选框文本列表。默认排除"全选/取消全选"。
		"""
		self.__exclude_texts = exclude_texts if exclude_texts is not None else [ "全选/取消全选" ]

	def get_checked_checkboxes(
			self, parent_widget: QWidget = None, layout_type: str = None, layout_name: str = None,
			layout_object: QLayout = None
	) -> List[ str ]:
		"""
		获取选中的复选框列表。

		调用时必须选择以下一种方式：
		1. 提供parent_widget和layout_type="vertical_layout_2"来查找verticalLayout_2中的选中复选框
		2. 提供parent_widget和layout_name来查找指定名称布局中的选中复选框
		3. 直接提供layout_object来在指定布局对象中查找选中复选框

		Args:
			parent_widget (QWidget, optional): 父部件，用于查找布局
			layout_type (str, optional): 布局类型，当前支持"vertical_layout_2"
			layout_name (str, optional): 布局名称，用于在parent_widget中查找指定名称的布局
			layout_object (QLayout, optional): 直接提供布局对象进行查找

		Returns:
			List[str]: 所有选中的且不在排除列表中的QCheckBox的objectName列表。
					  如果找不到指定的布局或没有复选框被选中，则返回空列表。

		使用示例:
			```python
			selector = CheckboxSelector()

			# 使用方式1：查找verticalLayout_2
			selected_checkboxes = selector.get_checked_checkboxes(
				parent_widget=main_window,
				layout_type="vertical_layout_2"
			)

			# 使用方式2：查找指定名称的布局
			selected_checkboxes = selector.get_checked_checkboxes(
				parent_widget=main_window,
				layout_name="options_layout"
			)

			# 使用方式3：直接提供布局对象
			selected_checkboxes = selector.get_checked_checkboxes(
				layout_object=my_layout
			)
			```
		"""
		# 验证参数合法性
		if layout_object:
			# 方式3：直接使用布局对象
			return self.__find_in_layout_object( layout_object )
		elif parent_widget:
			if layout_type == "vertical_layout_2":
				# 方式1：查找verticalLayout_2
				return self.__find_in_vertical_layout_2( parent_widget )
			elif layout_name:
				# 方式2：查找指定名称的布局
				return self.__find_in_layout( parent_widget, layout_name )

		# 参数不符合要求，记录错误并返回空列表
		logging.warning(
			"调用get_checked_checkboxes时参数无效，需要提供layout_object或parent_widget+layout_type/layout_name"
		)
		return [ ]

	def __find_in_vertical_layout_2( self, parent_widget: QWidget ) -> List[ str ]:
		"""
		在父部件中查找objectName为'verticalLayout_2'的QVBoxLayout，然后递归查找选中的QCheckBox。

		Args:
			parent_widget (QWidget): 包含verticalLayout_2的父部件

		Returns:
			List[str]: 选中的QCheckBox的objectName列表
		"""
		target_layout = parent_widget.findChild( QVBoxLayout, "verticalLayout_2" )
		if not target_layout:
			logging.warning( "在父部件中未找到objectName为'verticalLayout_2'的QVBoxLayout" )
			return [ ]

		return self.__find_in_layout_object( target_layout )

	def __find_in_layout( self, parent_widget: QWidget, layout_name: str ) -> List[ str ]:
		"""
		在父部件中查找指定objectName的布局，然后递归查找选中的QCheckBox。

		Args:
			parent_widget (QWidget): 包含目标布局的父部件
			layout_name (str): 要查找的布局的objectName

		Returns:
			List[str]: 选中的QCheckBox的objectName列表
		"""
		target_layout = parent_widget.findChild( QLayout, layout_name )
		if not target_layout:
			logging.warning( f"在父部件中未找到objectName为'{layout_name}'的布局" )
			return [ ]

		return self.__find_in_layout_object( target_layout )

	def __find_in_layout_object( self, layout: QLayout ) -> List[ str ]:
		"""
		直接在给定的布局对象中查找所有选中的QCheckBox控件。

		Args:
			layout (QLayout): 要搜索的布局对象

		Returns:
			List[str]: 选中的QCheckBox的objectName列表
		"""
		# 检查布局是否有效
		if not layout or not hasattr( layout, "objectName" ):
			logging.warning( "提供的布局对象无效" )
			return [ ]

		# 使用QCheckBoxFinder查找所有选中的复选框
		result_dict = QCheckBoxFinder.find_checked_checkboxes( layout, exclude_texts=self.__exclude_texts )

		# 获取布局中的选中复选框
		layout_name = layout.objectName()
		if not layout_name:
			# 如果布局没有objectName，返回所有发现的选中复选框（扁平化字典值）
			all_selected = [ ]
			for checkboxes in result_dict.values():
				all_selected.extend( checkboxes )
			return all_selected

		return result_dict.get( layout_name, [ ] )


class QCheckBoxFinder:
	"""
	一个功能强大的工具类，用于在给定的PyQt5容器内递归查找、过滤并返回选中的QCheckBox控件。
	该类中的所有方法都是静态的，无需实例化即可调用。
	"""

	@staticmethod
	def __get_all_checkboxes_from_item( item: Union[ QWidget, QLayout ] ) -> List[ QCheckBox ]:
		"""
		一个通用的私有方法，旨在从一个QWidget或QLayout中递归获取所有的QCheckBox子控件。
		该方法采用手动遍历布局项的方式。

		Args:
			item (Union[QWidget, QLayout]): 一个容器控件或一个布局。

		Returns:
			List[QCheckBox]: 找到的所有QCheckBox控件的列表。
		"""
		checkboxes: List[ QCheckBox ] = [ ]
		q = deque()

		layout_to_process = None
		if isinstance( item, QWidget ):
			layout_to_process = item.layout()
		elif isinstance( item, QLayout ):
			layout_to_process = item

		if layout_to_process:
			q.append( layout_to_process )
		# 对于没有布局的QWidget，直接查找其子QCheckBox
		elif isinstance( item, QWidget ):
			checkboxes.extend( item.findChildren( QCheckBox ) )

		visited_layouts = set()

		while q:
			layout = q.popleft()
			if layout in visited_layouts:
				continue
			visited_layouts.add( layout )

			for i in range( layout.count() ):
				layout_item = layout.itemAt( i )
				if layout_item is None:
					continue

				widget = layout_item.widget()
				sub_layout = layout_item.layout()

				if isinstance( widget, QCheckBox ):
					checkboxes.append( widget )

				if widget is not None and widget.layout() is not None:
					q.append( widget.layout() )
				elif sub_layout is not None:
					q.append( sub_layout )

		return checkboxes

	@staticmethod
	def find_checked_checkboxes(
			containers: Union[ QWidget, QLayout, List[ Union[ QWidget, QLayout ] ] ],
			exclude_texts: Optional[ List[ str ] ] = None
	) -> Dict[ str, List[ str ] ]:
		"""
		在指定的一个或多个容器（或布局）中，查找所有被选中的QCheckBox，并按容器分组返回，同时可根据文本排除特定的复选框。

		Args:
			containers (Union[QWidget, QLayout, List[Union[QWidget, QLayout]]]):
				一个或一系列PyQt5容器控件(QWidget)或布局(QLayout)。
			exclude_texts (Optional[List[str]], optional):
				一个用于排除的字符串列表。如果一个QCheckBox的文本在此列表中，它将被忽略。
				如果为 None 或空列表，则不会根据文本排除任何选中的QCheckBox。
				Defaults to None.

		Returns:
			Dict[str, List[str]]: 一个字典，键是容器控件的objectName，值是被选中的、未被排除的QCheckBox的objectName列表。
			如果一个容器没有符合条件的复选框，其对应的值将是一个空列表。
		"""
		"""
		使用示例:

		# 假设我们有以下的PyQt5控件结构:
		# parent_container = QWidget()
		# parent_container.setObjectName("ParentContainer")
		#
		# child_container1 = QWidget(parent_container)
		# child_container1.setObjectName("ChildContainer1")
		# cb1 = QCheckBox("Apple", child_container1)      # 将被排除
		# cb1.setObjectName("checkbox_apple")
		# cb1.setChecked(True)
		# cb2 = QCheckBox("Banana", child_container1)     # 将被包含
		# cb2.setObjectName("checkbox_banana")
		# cb2.setChecked(True)
		#
		# child_container2 = QWidget(parent_container)
		# child_container2.setObjectName("ChildContainer2")
		# cb3 = QCheckBox("Orange", child_container2)     # 未选中，不包含
		# cb3.setObjectName("checkbox_orange")
		# cb3.setChecked(False)
		# cb4 = QCheckBox("Apple", child_container2)      # 将被排除
		# cb4.setObjectName("checkbox_apple_in_2")
		# cb4.setChecked(True)
		# cb5 = QCheckBox("Grape", child_container2)      # 将被包含
		# cb5.setObjectName("checkbox_grape")
		# cb5.setChecked(True)
		#
		# # 直接通过类名调用静态方法，无需创建实例
		#
		# # 定义我们希望排除的复选框文本
		# fruits_to_exclude = ["Apple"]
		#
		# # 1. 测试单个容器，带文本排除
		# result1 = QCheckBoxFinder.find_checked_checkboxes(child_container1, exclude_texts=fruits_to_exclude)
		# print(f"Result for ChildContainer1 with exclusion: {result1}")
		# # 预期输出: Result for ChildContainer1 with exclusion: {'ChildContainer1': ['checkbox_banana']}
		#
		# # 2. 测试容器列表，带文本排除
		# all_containers = [child_container1, child_container2]
		# result2 = QCheckBoxFinder.find_checked_checkboxes(all_containers, exclude_texts=fruits_to_exclude)
		# print(f"Result for all containers with exclusion: {result2}")
		# # 预期输出: Result for all containers with exclusion: {'ChildContainer1': ['checkbox_banana'], 'ChildContainer2': ['checkbox_grape']}
		#
		# # 3. 测试单个容器，不带文本排除 (exclude_texts=None)
		# result3 = QCheckBoxFinder.find_checked_checkboxes(child_container2)
		# print(f"Result for ChildContainer2 without exclusion: {result3}")
		# # 预期输出: Result for ChildContainer2 without exclusion: {'ChildContainer2': ['checkbox_grape']}
		"""
		if not isinstance( containers, list ):
			containers = [ containers ]

		results: Dict[ str, List[ str ] ] = { }

		for item in containers:
			container_name = item.objectName()
			if not container_name:
				logging.warning(
					f"Input item of type {type( item ).__name__} lacks an objectName and will be skipped."
				)
				continue

			checkboxes = QCheckBoxFinder.__get_all_checkboxes_from_item( item )

			if container_name not in results:
				results[ container_name ] = [ ]

			for checkbox in checkboxes:
				# 复选框必须被选中，且其文本不能在排除列表中（或者没有提供排除列表）
				if checkbox.isChecked() and (not exclude_texts or checkbox.text() not in exclude_texts):
					if checkbox.objectName() not in results[ container_name ]:
						results[ container_name ].append( checkbox.objectName() )

		return results


class DatasetExporter:
	"""
	数据集转换器，用于将标注数据转换为YOLO11 Segment或YOLO11 OBB训练模型格式。

	该类处理以下功能：
	1. 收集输入文件夹中的图像和JSON文件
	2. 读取JSON文件获取标注信息
	3. 将标注转换为YOLO格式（Segment多边形分割格式或OBB定向边界框格式）
	4. 按比例划分训练集和验证集
	5. 根据用户选择处理文件

	支持的标注格式：
	- YOLO11 Segment: 多边形分割标注，默认格式
	- YOLO11 OBB: 定向边界框标注，当选中rotate_33或rotate_32复选框时使用此格式

	使用示例:
	```
	# 在HomeUiOperate.__export_dataset_button_function中使用
	exporter = DatasetExporter(
		self.__line_edit_manager,
		self.__checkbox_manager,
		self.__log_output,
		self.__logger,
		self.__label_manager
	)
	exporter.export_dataset()
	```
	"""

	def __init__(
			self,
			line_edit_manager: LineEditManager,
			checkbox_manager: CheckBoxManager,
			log_output: LogOutput = None,
			logger: Logger = None,
			label_manager: QLabelManager = None
	):
		"""
		初始化数据集转换器。

		Args:
			line_edit_manager: 用于获取输入文本的LineEditManager实例
			checkbox_manager: 用于获取复选框状态的CheckBoxManager实例
			log_output: 用于输出日志的LogOutput实例，可选
			logger: 用于记录日志的Logger实例，可选
			label_manager: 用于更新UI标签的LabelManager实例，可选
		"""
		self.__line_edit_manager = line_edit_manager
		self.__checkbox_manager = checkbox_manager
		self.__log_output = log_output
		self.__logger = logger
		self.__label_manager = label_manager

		# 控件映射配置字典，用于统一管理控件获取
		self.__WIDGET_CONFIG = {
			"line_edits": {
				"input_folder_path": "lineEdit_6",      # 输入文件夹路径
				"output_folder_path": "lineEdit_7",     # 输出文件夹路径
				"labels_file_path": "lineEdit_9",       # 标签文件路径
				"split_ratio": "lineEdit_8",            # 划分比例
				"config_file_path": "lineEdit_12",      # 配置文件路径
				"base_path": "lineEdit_1"               # 基础路径
			},
			"checkboxes": {
				"delete_source_files": "rotate_7",      # 是否删除源文件
				"use_obb_format": "rotate_35",          # 使用YOLO11 OBB格式
				"use_segment_format": "rotate_34"       # 使用YOLO11 Segment格式
			}
		}

		self.__label_manager.set_text( "label_34", "0/0" )
		self.__label_manager.set_text( "label_37", "0/0" )

	def __get_widget_value(self, logical_name: str, widget_type: str = "auto") -> Union[str, bool, None]:
		"""
		统一的控件值获取方法，支持LineEdit和CheckBox控件的统一获取接口。

		该方法通过逻辑名称获取控件值，自动识别控件类型，提供完整的错误处理和日志记录。
		重构后所有分散的控件获取操作都通过此方法统一处理，确保代码的一致性和可维护性。

		Args:
			logical_name (str): 逻辑名称，如"input_folder_path"、"delete_source_files"
			widget_type (str): 控件类型，"lineedit"、"checkbox"或"auto"（自动检测）

		Returns:
			Union[str, bool, None]: LineEdit控件返回str，CheckBox控件返回bool，失败返回None

		使用示例:
			# 获取输入文件夹路径
			path = self.__get_widget_value("input_folder_path")

			# 获取是否删除源文件
			should_delete = self.__get_widget_value("delete_source_files")

			# 明确指定控件类型
			ratio = self.__get_widget_value("split_ratio", "lineedit")
		"""
		try:
			# 自动检测控件类型
			if widget_type == "auto":
				if logical_name in self.__WIDGET_CONFIG["line_edits"]:
					widget_type = "lineedit"
				elif logical_name in self.__WIDGET_CONFIG["checkboxes"]:
					widget_type = "checkbox"
				else:
					self.__log(f"未知的逻辑控件名称: {logical_name}", color="red", level="error")
					return None

			# 获取LineEdit控件值
			if widget_type == "lineedit":
				widget_name = self.__WIDGET_CONFIG["line_edits"].get(logical_name)
				if not widget_name:
					self.__log(f"LineEdit控件映射不存在: {logical_name}", color="red", level="error")
					return None

				line_edit = self.__line_edit_manager.get_line_edit(widget_name)
				if not line_edit:
					self.__log(f"LineEdit控件获取失败: {widget_name}", color="red", level="error")
					return None

				value = line_edit.text()
				self.__log(f"获取LineEdit控件值 {logical_name}({widget_name}): {value}", level="debug")
				return value

			# 获取CheckBox控件值
			elif widget_type == "checkbox":
				widget_name = self.__WIDGET_CONFIG["checkboxes"].get(logical_name)
				if not widget_name:
					self.__log(f"CheckBox控件映射不存在: {logical_name}", color="red", level="error")
					return None

				# 根据逻辑名称选择合适的获取方法
				if logical_name in ["delete_source_files"]:
					# 使用get_checkbox_by_object_name方法
					checkbox = self.__checkbox_manager.get_checkbox_by_object_name(widget_name)
					value = checkbox.isChecked() if checkbox else False
				else:
					# 使用get_checked_state_by_object_name方法
					value = self.__checkbox_manager.get_checked_state_by_object_name(widget_name)

				self.__log(f"获取CheckBox控件值 {logical_name}({widget_name}): {value}", level="debug")
				return value

			else:
				self.__log(f"不支持的控件类型: {widget_type}", color="red", level="error")
				return None

		except Exception as e:
			self.__log(f"获取控件值时出错 {logical_name}: {str(e)}", color="red", level="error")
			return None

	def __get_lineedit_value(self, logical_name: str) -> str:
		"""
		获取LineEdit控件值的便捷方法。

		Args:
			logical_name (str): 逻辑名称

		Returns:
			str: 控件文本值，失败时返回空字符串

		使用示例:
			path = self.__get_lineedit_value("input_folder_path")
		"""
		result = self.__get_widget_value(logical_name, "lineedit")
		return result if isinstance(result, str) else ""

	def __get_checkbox_value(self, logical_name: str) -> bool:
		"""
		获取CheckBox控件值的便捷方法。

		Args:
			logical_name (str): 逻辑名称

		Returns:
			bool: 控件选中状态，失败时返回False

		使用示例:
			should_delete = self.__get_checkbox_value("delete_source_files")
		"""
		result = self.__get_widget_value(logical_name, "checkbox")
		return result if isinstance(result, bool) else False

	def __log( self, message: str, color: str = None, level: str = "info" ):
		"""
		打印日志信息。

		Args:
			message: 要打印的信息
			color: 输出颜色，可选
			level: 日志级别，可选，默认为info
		"""
		# 判断是否应该输出到UI界面
		# 1. 带颜色的日志（通常表示重要信息）都输出到UI界面
		# 2. warning和error级别的日志都输出到UI界面
		# 3. 关键步骤的info日志也输出到UI界面（通常是进度相关的信息）
		should_log_to_ui = (
				color is not None or  # 有颜色的日志都显示到UI
				level.lower() == "warning" or level.lower() == "error"
		)

		# 处理log_output记录UI日志，只输出重要的日志
		if self.__log_output and should_log_to_ui:
			self.__log_output.append( message, color=color )

		# 处理logger记录控制台日志，所有级别都记录
		if self.__logger:
			if level.lower() == "debug":
				self.__logger.debug( message )
			elif level.lower() == "warning":
				self.__logger.warning( message )
			elif level.lower() == "error":
				self.__logger.error( message )
			else:
				self.__logger.info( message )
		# 如果两者都不存在，则使用print
		elif not self.__log_output:
			print( message )

	def __update_progress_label( self, label_name: str, current: int, total: int ):
		"""
		更新进度标签显示。

		Args:
			label_name: 标签对象名称
			current: 当前进度值
			total: 总进度值
		"""
		try:
			if not self.__label_manager:
				return

			# if not self.__label_manager.has_label( label_name ):
			# 	self.__log( f"找不到标签: {label_name}", color="yellow", level="warning" )
			# 	return

			progress_text = f"{current}/{total}"
			self.__label_manager.set_text( label_name, progress_text )

			# 使用淡入淡出动画效果提示用户
			self.__label_manager.start_fade_animation( label_name, duration_ms=200 )

			self.__log( f"更新进度显示 {label_name}: {progress_text}", level="debug" )
		except Exception as e:
			self.__log( f"更新进度标签时出错: {str( e )}", color="red", level="error" )

	# 此处不抛出异常，确保即使标签更新失败，主流程也能继续

	def __get_input_folder_path( self ) -> str:
		"""
		获取输入文件夹路径。

		Returns:
			输入文件夹路径字符串
		"""
		path = self.__get_lineedit_value("input_folder_path")
		self.__log( f"获取输入文件夹路径: {path}", level="debug" )
		return path

	def __get_output_folder_path( self ) -> str:
		"""
		获取输出文件夹路径。

		Returns:
			输出文件夹路径字符串
		"""
		path = self.__get_lineedit_value("output_folder_path")
		self.__log( f"获取输出文件夹路径: {path}", level="debug" )
		return path

	def __get_labels_file_path( self ) -> str:
		"""
		获取标签文件路径。

		Returns:
			标签文件路径字符串
		"""
		path = self.__get_lineedit_value("labels_file_path")
		self.__log( f"获取标签文件路径: {path}", level="debug" )
		return path

	def __get_split_ratio( self ) -> Tuple[ float, float ]:
		"""
		获取划分比例。

		Returns:
			包含训练集和验证集比例的元组，如(0.85, 0.15)
		"""
		ratio_text = self.__get_lineedit_value("split_ratio")
		train_ratio, val_ratio = map( int, ratio_text.split( ':' ) )
		total = train_ratio + val_ratio
		result = (train_ratio / total, val_ratio / total)
		self.__log( f"获取划分比例: {ratio_text} -> {result}", level="debug" )
		return result

	def __should_delete_source_files( self ) -> bool:
		"""
		判断是否删除源文件。

		Returns:
			如果勾选了删除选项，则返回True；否则返回False
		"""
		result = self.__get_checkbox_value("delete_source_files")
		self.__log( f"是否删除源文件: {result}", level="debug" )
		return result



	def __get_existing_output_files( self ) -> set:
		"""
		获取输出目录中已存在的图像文件名集合（不含扩展名）

		该方法扫描输出文件夹的images子目录，收集所有已存在的图像文件名，
		用于智能过滤，避免重复处理已存在的文件。

		扫描目录：
			- {output_folder}/images/train/
			- {output_folder}/images/val/

		Returns:
			set: 已存在文件名的集合（不含扩展名），如{'image1', 'image2', ...}

		异常处理：
			- 如果输出目录不存在，返回空集合
			- 如果子目录不存在，跳过该目录
			- 文件访问异常时记录日志并继续处理

		使用示例:
			existing_files = self.__get_existing_output_files()
			if 'image001' in existing_files:
				# 该文件已存在，跳过处理
				pass
		"""
		existing_files = set()

		try:
			output_folder = self.__get_output_folder_path()

			if not output_folder or not os.path.exists( output_folder ):
				self.__log( "输出文件夹不存在，跳过已存在文件检查", level="debug" )
				return existing_files

			# 检查的子目录列表
			subdirs = [ 'images/train', 'images/val' ]

			for subdir in subdirs:
				dir_path = os.path.join( output_folder, subdir )

				if not os.path.exists( dir_path ):
					self.__log( f"输出子目录不存在: {dir_path}", level="debug" )
					continue

				try:
					for filename in os.listdir( dir_path ):
						file_path = os.path.join( dir_path, filename )

						# 只处理文件，跳过目录
						if os.path.isfile( file_path ):
							base_name = os.path.splitext( filename )[ 0 ]
							existing_files.add( base_name )

				except OSError as e:
					self.__log( f"扫描目录时出错 {dir_path}: {str( e )}", level="warning" )
					continue

			self.__log( f"发现输出目录中已存在 {len( existing_files )} 个文件", level="debug" )

		except Exception as e:
			self.__log( f"获取已存在文件列表时出错: {str( e )}", level="warning" )

		return existing_files

	def __collect_files( self ) -> List[ Tuple[ str, str ] ]:
		"""
		收集输入文件夹中的图像和JSON文件，智能过滤已存在的文件

		新增功能：
			- 智能过滤：检查输出目录中已存在的文件，避免重复处理
			- 详细统计：提供过滤前后的文件数量统计
			- 增强日志：记录过滤过程和结果

		过滤逻辑：
			1. 扫描输出目录获取已存在的文件名
			2. 对比输入文件的基础名称（不含扩展名）
			3. 跳过已存在的文件，只处理新文件

		Returns:
			包含图像文件和对应JSON文件路径的列表，如[(image_path, json_path), ...]
			已过滤掉输出目录中已存在的文件
		"""
		input_folder = self.__get_input_folder_path()
		self.__log( f"开始从 {input_folder} 收集文件（启用智能过滤）", color="blue", level="info" )

		# 验证输入目录
		if not os.path.exists( input_folder ):
			self.__log( f"输入文件夹不存在: {input_folder}", color="red", level="error" )
			return [ ]

		if not os.path.isdir( input_folder ):
			self.__log( f"指定的路径不是一个文件夹: {input_folder}", color="red", level="error" )
			return [ ]

		# 获取已存在的文件（智能过滤核心）
		existing_files = self.__get_existing_output_files()

		# 文件扩展名和统计变量
		image_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff')
		file_pairs = [ ]

		# 统计数据
		total_image_files = 0
		total_json_files = 0
		filtered_count = 0
		valid_pairs = 0

		try:
			for filename in os.listdir( input_folder ):
				file_path = os.path.join( input_folder, filename )

				# 跳过非文件
				if not os.path.isfile( file_path ):
					continue

				base_name, ext = os.path.splitext( filename )
				ext = ext.lower()

				# 检查是否是图像文件
				if ext in image_extensions:
					total_image_files += 1

					# 智能过滤：检查是否已存在于输出目录
					if base_name in existing_files:
						filtered_count += 1
						self.__log( f"跳过已存在的文件: {filename}", level="debug" )
						continue

					# 查找对应的JSON文件
					json_path = os.path.join( input_folder, base_name + '.json' )
					if os.path.exists( json_path ) and os.path.isfile( json_path ):
						total_json_files += 1
						valid_pairs += 1
						file_pairs.append( (file_path, json_path) )
						self.__log( f"找到新文件对: {file_path} - {json_path}", level="debug" )

			# 输出详细统计信息
			self.__log(
				f"智能过滤结果: 发现 {total_image_files} 个图像文件, "
				f"过滤 {filtered_count} 个已存在文件, "
				f"找到 {total_json_files} 个JSON文件, "
				f"生成 {valid_pairs} 对有效配对",
				color="blue"
			)

		except Exception as e:
			self.__log( f"扫描文件时出错: {str( e )}", color="red", level="error" )
			return [ ]

		self.__log( f"智能过滤完成，找到 {len( file_pairs )} 对新的图像和JSON文件", color="blue" )
		return file_pairs

	def __read_labels_file( self ) -> List[ str ]:
		"""
		读取标签文件内容。

		Returns:
			标签列表，如['person', 'car', ...]
		"""
		labels_file = self.__get_labels_file_path()

		self.__log( f"读取标签文件: {labels_file}", color="blue", level="info" )

		if not os.path.exists( labels_file ):
			self.__log( f"标签文件不存在: {labels_file}", color="red", level="error" )
			return [ ]

		if not os.path.isfile( labels_file ):
			self.__log( f"指定的标签路径不是一个文件: {labels_file}", color="red", level="error" )
			return [ ]

		try:
			with open( labels_file, 'r', encoding='utf-8' ) as f:
				# 读取所有行并过滤空行
				lines = f.readlines()
				labels = [ ]

				for i, line in enumerate( lines, 1 ):
					line = line.strip()
					if line:  # 非空行
						if line in labels:
							self.__log(
								f"警告: 标签文件中发现重复标签 '{line}' (第{i}行)", color="yellow", level="warning"
							)
						else:
							labels.append( line )

			if not labels:
				self.__log( "警告: 标签文件为空或只包含空行", color="yellow", level="warning" )

			self.__log( f"读取到 {len( labels )} 个标签: {', '.join( labels )}", color="blue" )
			return labels

		except Exception as e:
			self.__log( f"读取标签文件出错: {labels_file}, 错误: {str( e )}", color="red", level="error" )
			return [ ]

	def __read_json_file( self, json_path: str ) -> List[ Dict ]:
		"""
		读取JSON文件中的标注信息。

		Args:
			json_path: JSON文件路径

		Returns:
			包含标注信息的列表
		"""
		try:
			with open( json_path, 'r', encoding='utf-8' ) as f:
				data = json.load( f )

			shapes = data.get( 'shapes', [ ] )
			self.__log( f"从 {json_path} 读取到 {len( shapes )} 个标注形状", level="debug" )
			return shapes
		except Exception as e:
			self.__log( f"读取JSON文件出错: {json_path}, 错误: {str( e )}", color="red", level="error" )
			return [ ]

	def __normalize_coordinates( self, points: List[ List[ float ] ], image_width: int, image_height: int ) -> List[
		float ]:
		"""
		将坐标归一化为YOLO格式。

		Args:
			points: 多边形坐标点列表，如[[x1, y1], [x2, y2], ...]
			image_width: 图像宽度
			image_height: 图像高度

		Returns:
			归一化后的坐标列表，如[x1, y1, x2, y2, ...]
		"""
		self.__log( f"归一化坐标点 (图像尺寸: {image_width}x{image_height})", level="debug" )

		# 检查宽高是否有效
		if image_width <= 0 or image_height <= 0:
			self.__log(
				f"错误: 图像尺寸无效 ({image_width}x{image_height})，无法进行坐标归一化", color="red", level="error"
			)
			return [ ]

		normalized = [ ]
		for point in points:
			# 归一化x坐标
			normalized.append( point[ 0 ] / image_width )
			# 归一化y坐标
			normalized.append( point[ 1 ] / image_height )
		return normalized

	def __convert_to_yolo_format( self, file_pairs: List[ Tuple[ str, str ] ], labels: List[ str ] ) -> List[
		Tuple[ str, List[ str ] ] ]:
		"""
		将标注信息转换为YOLO格式。

		Args:
			file_pairs: 图像和JSON文件对列表
			labels: 标签列表

		Returns:
			包含图像路径和对应YOLO格式标注的列表，如[(image_path, ["class_idx x1 y1 x2 y2..."]), ...]
		"""
		self.__log( f"开始将 {len( file_pairs )} 对文件转换为YOLO格式", color="blue", level="info" )

		# 检查使用哪种格式
		use_obb_format = False
		rotate_33_checked = self.__get_checkbox_value("use_obb_format")
		rotate_32_checked = self.__get_checkbox_value("use_segment_format")

		# 根据复选框状态决定使用的格式
		if rotate_33_checked:
			use_obb_format = True
			self.__log( "检测到rotate_33被选中，将使用YOLO11 OBB格式", color="blue", level="info" )
		elif rotate_32_checked:
			use_obb_format = False
			self.__log( "检测到rotate_32被选中，将使用YOLO11 Segment格式", color="blue", level="info" )
		else:
			use_obb_format = False
			self.__log( "未检测到特定的格式选择，默认使用YOLO11 Segment格式", color="blue", level="info" )

		yolo_data = [ ]

		for image_path, json_path in file_pairs:
			shapes = self.__read_json_file( json_path )

			if not shapes:
				self.__log( f"警告: JSON文件没有标注信息: {json_path}", color="yellow", level="warning" )
				continue

			# 获取图像尺寸，从JSON文件中读取
			with open( json_path, 'r', encoding='utf-8' ) as f:
				data = json.load( f )
				image_width = data.get( 'imageWidth', 0 )
				image_height = data.get( 'imageHeight', 0 )

			if image_width <= 0 or image_height <= 0:
				self.__log( f"警告: 无法获取图像尺寸: {image_path}", color="yellow", level="warning" )
				continue

			yolo_annotations = [ ]

			for shape in shapes:
				label_name = shape.get( 'label' )
				points = shape.get( 'points', [ ] )
				shape_type = shape.get( 'shape_type', '' )  # 获取形状类型

				# 检查标签是否在预定义列表中
				if label_name not in labels:
					self.__log( f"警告: 标签 '{label_name}' 不在标签列表中，跳过", color="yellow", level="warning" )
					continue

				# 获取标签索引
				label_idx = labels.index( label_name )

				# 根据不同的标注类型处理
				if use_obb_format and shape_type == 'rotation':
					# OBB定向边界框格式处理
					if len( points ) != 4:
						self.__log(
							f"警告: OBB标注点数量不正确，应为4点，实际为{len( points )}，跳过", color="yellow",
							level="warning"
						)
						continue

					# 归一化坐标
					normalized_coords = self.__normalize_coordinates( points, image_width, image_height )

					# 格式化为YOLO OBB格式字符串: "label_idx x1 y1 x2 y2 x3 y3 x4 y4"
					# OBB格式的点顺序必须是顺时针或逆时针
					coords_str = ' '.join( f"{coord:.6f}" for coord in normalized_coords )
					yolo_annotation = f"{label_idx} {coords_str}"

					self.__log( f"转换OBB标注: 标签={label_name}, 索引={label_idx}", level="debug" )
				else:
					# 原始的Segment格式处理
					normalized_coords = self.__normalize_coordinates( points, image_width, image_height )
					coords_str = ' '.join( f"{coord:.6f}" for coord in normalized_coords )
					yolo_annotation = f"{label_idx} {coords_str}"

					self.__log( f"转换Segment标注: 标签={label_name}, 索引={label_idx}", level="debug" )

				yolo_annotations.append( yolo_annotation )

			self.__log(
				f"文件 {os.path.basename( image_path )} 转换为 {len( yolo_annotations )} 个YOLO标注", level="debug"
			)

			yolo_data.append( (image_path, yolo_annotations) )

		format_type = "YOLO11 OBB" if use_obb_format else "YOLO11 Segment"
		self.__log( f"转换了 {len( yolo_data )} 个文件到{format_type}格式", color="blue" )
		return yolo_data

	def __split_dataset(
			self, yolo_data: List[ Tuple[ str, List[ str ] ] ]
	) -> Tuple[ List[ Tuple[ str, List[ str ] ] ], List[ Tuple[ str, List[ str ] ] ] ]:
		"""
		按比例划分训练集和验证集。

		Args:
			yolo_data: 包含图像路径和YOLO格式标注的列表

		Returns:
			训练集和验证集的元组
		"""
		self.__log( f"开始划分数据集，共 {len( yolo_data )} 个样本", color="blue", level="info" )

		train_ratio, _ = self.__get_split_ratio()

		# 随机打乱数据
		random.shuffle( yolo_data )

		# 计算训练集大小
		train_size = int( len( yolo_data ) * train_ratio )

		# 划分数据
		train_data = yolo_data[ :train_size ]
		val_data = yolo_data[ train_size: ]

		self.__log( f"数据集划分: 训练集 {len( train_data )}个, 验证集 {len( val_data )}个", color="blue" )
		return train_data, val_data

	def __prepare_output_dirs( self ) -> Tuple[ str, str, str, str ]:
		"""
		准备输出目录结构。

		Returns:
			训练集和验证集的图像和标签目录元组，如(train_images_dir, train_labels_dir, val_images_dir, val_labels_dir)
		"""
		output_folder = self.__get_output_folder_path()
		self.__log( f"准备输出目录结构: {output_folder}", color="blue", level="info" )

		# 创建目录结构
		train_images_dir = os.path.join( output_folder, 'images', 'train' )
		train_labels_dir = os.path.join( output_folder, 'labels', 'train' )
		val_images_dir = os.path.join( output_folder, 'images', 'val' )
		val_labels_dir = os.path.join( output_folder, 'labels', 'val' )
		train_cache_dir = os.path.join( output_folder, 'labels', 'train.cache' )
		val_cache_dir = os.path.join( output_folder, 'labels', 'val.cache' )

		if os.path.exists( train_cache_dir ):
			os.remove( train_cache_dir )
		if os.path.exists( val_cache_dir ):
			os.remove( val_cache_dir )



		# 确保目录存在
		for dir_path in [ train_images_dir, train_labels_dir, val_images_dir, val_labels_dir ]:
			os.makedirs( dir_path, exist_ok=True )
			self.__log( f"确保目录存在: {dir_path}", level="debug" )

		return train_images_dir, train_labels_dir, val_images_dir, val_labels_dir

	def __save_dataset(
			self, train_data: List[ Tuple[ str, List[ str ] ] ], val_data: List[ Tuple[ str, List[ str ] ] ]
	):
		"""
		保存处理后的数据集。

		Args:
			train_data: 训练集数据
			val_data: 验证集数据
		"""
		self.__log( "开始保存数据集", color="blue", level="info" )

		train_images_dir, train_labels_dir, val_images_dir, val_labels_dir = self.__prepare_output_dirs()

		# 处理函数，用于训练集和验证集
		def process_dataset( dataset, images_dir, labels_dir, dataset_type, label_name=None ):
			self.__log( f"处理{dataset_type}数据集: {len( dataset )}个文件", color="blue", level="info" )

			total_items = len( dataset )

			# 初始化进度显示
			if label_name and self.__label_manager:
				self.__update_progress_label( label_name, 0, total_items )

			for i, (image_path, annotations) in enumerate( dataset, 1 ):
				# 更新处理进度
				if label_name and self.__label_manager:  # 每次都更新UI
					self.__update_progress_label( label_name, i, total_items )

				# 复制图像文件
				image_filename = os.path.basename( image_path )
				target_image_path = os.path.join( images_dir, image_filename )
				shutil.copy2( image_path, target_image_path )

				# 保存标注文件
				base_name = os.path.splitext( image_filename )[ 0 ]
				target_label_path = os.path.join( labels_dir, f"{base_name}.txt" )

				with open( target_label_path, 'w', encoding='utf-8' ) as f:
					f.write( "\n".join( annotations ) )

				self.__log(
					f"保存{dataset_type}文件: {image_filename} -> {target_image_path}, 标签: {target_label_path}",
					level="debug"
				)

			# 更新最终进度
			if label_name and self.__label_manager:
				self.__update_progress_label( label_name, total_items, total_items )

		# 处理训练集，使用label_34显示进度
		process_dataset( train_data, train_images_dir, train_labels_dir, "训练集", "label_34" )
		self.__log( f"已保存训练集: {len( train_data )}个文件", color="green" )

		# 处理验证集，使用label_37显示进度
		process_dataset( val_data, val_images_dir, val_labels_dir, "验证集", "label_37" )
		self.__log( f"已保存验证集: {len( val_data )}个文件", color="green" )

	def __delete_source_files_if_needed( self, file_pairs: List[ Tuple[ str, str ] ] = None ):
		"""
		如果用户选择，删除源文件。

		重构说明：
		- 保持方法签名兼容性，file_pairs参数现在为可选参数
		- 当file_pairs为None时，自动扫描input_folder_path文件夹
		- 实现自动发现JSON文件与图像文件的配对关系
		- 删除所有找到的JSON文件和对应的图像文件

		Args:
			file_pairs: 图像和JSON文件对列表（可选，为None时自动扫描）
		"""
		if not self.__should_delete_source_files():
			return

		# 如果没有提供file_pairs，则自动扫描input_folder_path
		if file_pairs is None:
			file_pairs = self.__scan_input_folder_for_deletion()
			if not file_pairs:
				self.__log( "未找到需要删除的文件", color="yellow", level="warning" )
				return

		self.__log( f"删除源文件: {len( file_pairs )} 对文件", color="blue", level="info" )

		success_count = 0
		fail_count = 0

		for image_path, json_path in file_pairs:
			try:
				os.remove( image_path )
				os.remove( json_path )
				success_count += 1
				self.__log( f"删除文件: {image_path}, {json_path}", level="debug" )
			except Exception as e:
				fail_count += 1
				self.__log( f"删除文件失败: {image_path}, 错误: {str( e )}", color="red", level="error" )

		self.__log( f"已删除 {success_count} 对源文件, 失败 {fail_count} 对", color="blue" )
		self.__log( f"删除源文件完成: 成功 {success_count} 对, 失败 {fail_count} 对", color="blue", level="info" )

	def __scan_input_folder_for_deletion( self ) -> List[ Tuple[ str, str ] ]:
		"""
		扫描input_folder_path文件夹，自动发现JSON文件与图像文件的配对关系。

		该方法实现以下功能：
		1. 扫描input_folder_path下所有JSON文件
		2. 对每个JSON文件，查找同名的图像文件
		3. 支持常见图像格式：jpg, jpeg, png, bmp, tif, tiff
		4. 返回找到的文件配对列表

		Returns:
			List[Tuple[str, str]]: 图像文件和JSON文件路径的配对列表
		"""
		input_folder = self.__get_input_folder_path()

		if not input_folder:
			self.__log( "输入文件夹路径为空", color="red", level="error" )
			return []

		if not os.path.exists( input_folder ):
			self.__log( f"输入文件夹不存在: {input_folder}", color="red", level="error" )
			return []

		if not os.path.isdir( input_folder ):
			self.__log( f"指定的路径不是一个文件夹: {input_folder}", color="red", level="error" )
			return []

		self.__log( f"开始扫描输入文件夹进行删除: {input_folder}", color="blue", level="info" )

		# 支持的图像文件扩展名
		image_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff')
		file_pairs = []

		json_files_found = 0
		matched_pairs = 0

		try:
			for filename in os.listdir( input_folder ):
				file_path = os.path.join( input_folder, filename )

				# 跳过非文件
				if not os.path.isfile( file_path ):
					continue

				# 检查是否是JSON文件
				if filename.lower().endswith('.json'):
					json_files_found += 1
					base_name = os.path.splitext( filename )[0]

					# 查找对应的图像文件
					image_path = None
					for ext in image_extensions:
						potential_image_path = os.path.join( input_folder, base_name + ext )
						if os.path.exists( potential_image_path ) and os.path.isfile( potential_image_path ):
							image_path = potential_image_path
							break

					if image_path:
						matched_pairs += 1
						file_pairs.append( (image_path, file_path) )
						self.__log( f"找到配对文件: {image_path} - {file_path}", level="debug" )
					else:
						self.__log( f"JSON文件 {file_path} 没有找到对应的图像文件", level="warning" )

		except Exception as e:
			self.__log( f"扫描输入文件夹时出错: {str( e )}", color="red", level="error" )
			return []

		self.__log( f"扫描完成: 找到 {json_files_found} 个JSON文件, {matched_pairs} 对配对文件", color="blue", level="info" )

		return file_pairs

	def export_dataset( self ) -> bool:
		"""
		执行数据集导出流程。

		Returns:
			操作成功返回True，否则返回False
		"""
		try:
			self.__log( "开始执行数据集导出流程", color="blue", level="info" )

			# 获取输入输出路径检查
			input_path = self.__get_input_folder_path()
			output_path = self.__get_output_folder_path()
			labels_path = self.__get_labels_file_path()

			if not (input_path and output_path and labels_path):
				self.__log( "输入、输出或标签文件路径不能为空", color="red", level="error" )
				return False

			if not os.path.exists( input_path ):
				self.__log( f"输入文件夹不存在: {input_path}", color="red", level="error" )
				return False

			if not os.path.exists( labels_path ):
				self.__log( f"标签文件不存在: {labels_path}", color="red", level="error" )
				return False

			# 1. 收集文件对
			self.__log( "开始收集文件...", color="blue" )
			file_pairs = self.__collect_files()
			if not file_pairs:
				self.__log( "没有找到有效的图像和JSON文件对", color="red", level="error" )
				return False

			# 2. 读取标签文件
			self.__log( "读取标签文件...", color="blue" )
			labels = self.__read_labels_file()
			if not labels:
				self.__log( "标签文件为空或读取失败", color="red", level="error" )
				return False

			# 3. 转换为YOLO格式
			self.__log( "转换标注为YOLO格式...", color="blue" )
			yolo_data = self.__convert_to_yolo_format( file_pairs, labels )
			if not yolo_data:
				self.__log( "转换标注数据失败", color="red", level="error" )
				return False

			# 4. 划分数据集
			self.__log( "划分训练集和验证集...", color="blue" )
			train_data, val_data = self.__split_dataset( yolo_data )

			# 5. 保存数据集
			self.__log( "保存数据集...", color="blue" )
			self.__save_dataset( train_data, val_data )

			# 6. 如果需要，删除源文件
			if self.__should_delete_source_files():
				self.__log( "删除源文件...", color="blue" )
				# self.__delete_source_files_if_needed( file_pairs )
				self.__delete_source_files_if_needed( )

			self.__log( "数据集导出完成!", color="green" )
			return True

		except Exception as e:
			self.__log( f"导出过程中发生错误: {str( e )}", color="red", level="error" )
			import traceback
			self.__log( traceback.format_exc(), color="red", level="error" )
			return False


class AnnotationCounter:
	"""
	增强型标注对象统计器，用于统计指定目录中所有标注文件的标注对象总数量。

	该类处理以下功能：
	1. 从输入路径中收集所有的图像和对应的JSON标注文件对
	2. 统计所有JSON标注文件中的标注对象总数量
	3. 更新UI中的相关标签显示统计结果
	4. 提供实时进度显示和取消操作功能
	5. 增强的错误处理和重试机制
	6. 详细的统计报告和性能监控

	使用示例:
	```python
	# 基本使用
	counter = AnnotationCounter(
		line_edit_manager=self.__line_edit_manager,
		log_output=self.__log_output,
		logger=self.__logger,
		label_manager=self.__label_manager
	)
	total_count = counter.count_annotations()

	# 高级使用（带配置）
	counter = AnnotationCounter(
		line_edit_manager=self.__line_edit_manager,
		log_output=self.__log_output,
		logger=self.__logger,
		label_manager=self.__label_manager,
		batch_size=50,
		enable_progress=True,
		max_retries=3
	)
	total_count = counter.count_annotations()

	# 获取详细统计报告
	report = counter.get_statistics_report()
	print(f"处理文件数: {report['processed_files']}")
	print(f"处理时间: {report['processing_time']:.2f}秒")
	```
	"""

	def __init__(
			self,
			line_edit_manager: LineEditManager,
			log_output: LogOutput | None = None,
			logger: Logger | None = None,
			label_manager: QLabelManager | None = None,
			batch_size: int = 20,
			enable_progress: bool = True,
			max_retries: int = 2,
			progress_update_interval: int = 5
	):
		"""
		初始化增强型标注对象统计器。

		Args:
			line_edit_manager: 用于获取输入文本的LineEditManager实例
			log_output: 用于输出日志的LogOutput实例，可选
			logger: 用于记录日志的Logger实例，可选
			label_manager: 用于更新UI标签的LabelManager实例，可选
			batch_size: 批量处理文件数量，默认20
			enable_progress: 是否启用进度显示，默认True
			max_retries: 文件读取失败时的最大重试次数，默认2
			progress_update_interval: 进度更新间隔（文件数），默认5
		"""
		self.__line_edit_manager = line_edit_manager
		self.__log_output = log_output
		self.__logger = logger
		self.__label_manager = label_manager

		# 配置参数
		self.__batch_size = max(1, batch_size)
		self.__enable_progress = enable_progress
		self.__max_retries = max(0, max_retries)
		self.__progress_update_interval = max(1, progress_update_interval)

		# 统计信息
		self.__statistics = {
			'total_files': 0,
			'processed_files': 0,
			'failed_files': 0,
			'total_annotations': 0,
			'processing_time': 0.0,
			'start_time': None,
			'end_time': None,
			'errors': []
		}

		# 控制标志
		self.__is_cancelled = False

		# 控件映射字典 - 统一管理所有控件获取
		# 这个字典提供了语义化的键名到实际控件ID的映射
		# 便于维护和理解，避免在代码中直接使用控件ID
		# 只映射AnnotationCounter类中实际使用到的控件
		self.__WIDGET_MAPPING = {
			# LineEdit 控件映射
			"input_folder_path": "lineEdit_6",   # 输入文件夹路径

			# Label 控件映射
			"result_label": "label_75",          # 总标注对象结果显示标签
			"valid_files_label": "label_180",    # 有效文件数量显示标签
			"progress_label": "label_276",       # 进度显示标签（主要）
		}

	def __get_widget_value(self, logical_name: str, widget_type: str = "auto") -> str:
		"""
		统一的控件值获取方法，支持字典映射模式。

		Args:
			logical_name: 逻辑名称，对应 __WIDGET_MAPPING 字典中的键
			widget_type: 控件类型，可选值: "auto", "lineEdit", "label"

		Returns:
			str: 控件的文本值，如果获取失败则返回空字符串

		Raises:
			KeyError: 当逻辑名称不存在于映射字典中时

		使用示例:
		```python
		# 获取输入文件夹路径（LineEdit控件）
		folder_path = self.__get_widget_value("input_folder_path")

		# 获取标签文本（Label控件）
		result_text = self.__get_widget_value("result_label", "label")

		# 等价于原来的方式：
		# folder_path = self.__line_edit_manager.get_text("lineEdit_6")
		```
		"""
		if logical_name not in self.__WIDGET_MAPPING:
			raise KeyError(f"逻辑名称 '{logical_name}' 不存在于控件映射字典中。可用的键: {list(self.__WIDGET_MAPPING.keys())}")

		widget_id = self.__WIDGET_MAPPING[logical_name]

		try:
			# 自动判断控件类型
			if widget_type == "auto":
				if widget_id.startswith("lineEdit_"):
					widget_type = "lineEdit"
				elif widget_id.startswith("label_"):
					widget_type = "label"
				else:
					widget_type = "lineEdit"  # 默认为lineEdit

			# 根据控件类型获取值
			if widget_type == "lineEdit":
				value = self.__line_edit_manager.get_text(widget_id)
			elif widget_type == "label":
				# 对于label控件，我们通常是设置而不是获取，这里返回空字符串
				value = ""
			else:
				raise ValueError(f"不支持的控件类型: {widget_type}")

			self.__log(f"获取控件值: {logical_name} ({widget_id}) = '{value}'", level="debug")
			return value or ""
		except Exception as e:
			self.__log(f"获取控件值失败: {logical_name} ({widget_id}), 错误: {str(e)}", color="red", level="error")
			return ""

	def __set_widget_text(self, logical_name: str, text: str, with_animation: bool = True) -> bool:
		"""
		统一的控件文本设置方法，支持字典映射模式。

		Args:
			logical_name: 逻辑名称，对应 __WIDGET_MAPPING 字典中的键
			text: 要设置的文本内容
			with_animation: 是否使用淡入淡出动画效果，默认True

		Returns:
			bool: 设置是否成功

		使用示例:
		```python
		# 设置结果标签文本
		self.__set_widget_text("result_label", "总标注对象: 100")

		# 设置进度标签文本（不使用动画）
		self.__set_widget_text("progress_label", "进度: 50/100", with_animation=False)
		```
		"""
		if logical_name not in self.__WIDGET_MAPPING:
			self.__log(f"逻辑名称 '{logical_name}' 不存在于控件映射字典中。可用的键: {list(self.__WIDGET_MAPPING.keys())}",
					  color="red", level="error")
			return False

		widget_id = self.__WIDGET_MAPPING[logical_name]

		try:
			if not self.__label_manager:
				self.__log("标签管理器未初始化，无法设置控件文本", color="yellow", level="warning")
				return False

			# 设置标签文本
			self.__label_manager.set_text(widget_id, text)

			# 如果启用动画效果
			if with_animation and widget_id.startswith("label_"):
				self.__label_manager.start_fade_animation(widget_id, duration_ms=200)

			self.__log(f"设置控件文本: {logical_name} ({widget_id}) = '{text}'", level="debug")
			return True

		except Exception as e:
			self.__log(f"设置控件文本失败: {logical_name} ({widget_id}), 错误: {str(e)}", color="red", level="error")
			return False

	def cancel_operation(self):
		"""
		取消当前统计操作。

		使用示例:
		```python
		counter = AnnotationCounter(...)
		# 在另一个线程中取消操作
		counter.cancel_operation()
		```
		"""
		self.__is_cancelled = True
		self.__log("用户取消了标注统计操作", color="yellow", level="warning")

	def get_statistics_report(self) -> dict:
		"""
		获取详细的统计报告。

		Returns:
			dict: 包含详细统计信息的字典

		使用示例:
		```python
		counter = AnnotationCounter(...)
		total_count = counter.count_annotations()
		report = counter.get_statistics_report()

		print(f"总文件数: {report['total_files']}")
		print(f"处理成功: {report['processed_files']}")
		print(f"处理失败: {report['failed_files']}")
		print(f"总标注数: {report['total_annotations']}")
		print(f"处理时间: {report['processing_time']:.2f}秒")
		print(f"平均速度: {report['avg_files_per_second']:.2f}文件/秒")
		```
		"""
		report = self.__statistics.copy()

		# 计算额外的统计信息
		if report['processing_time'] > 0:
			report['avg_files_per_second'] = report['processed_files'] / report['processing_time']
			report['avg_annotations_per_file'] = (
				report['total_annotations'] / report['processed_files']
				if report['processed_files'] > 0 else 0
			)
		else:
			report['avg_files_per_second'] = 0
			report['avg_annotations_per_file'] = 0

		report['success_rate'] = (
			(report['processed_files'] / report['total_files']) * 100
			if report['total_files'] > 0 else 0
		)

		return report

	def __reset_statistics(self):
		"""重置统计信息。"""
		self.__statistics = {
			'total_files': 0,
			'processed_files': 0,
			'failed_files': 0,
			'total_annotations': 0,
			'processing_time': 0.0,
			'start_time': None,
			'end_time': None,
			'errors': []
		}
		self.__is_cancelled = False

	def __get_input_folder_path( self ) -> str:
		"""
		获取输入文件夹路径（重构为映射方式）。

		Returns:
			输入文件夹的路径字符串

		使用示例:
		```python
		# 现在使用统一的映射机制获取路径
		folder_path = self.__get_input_folder_path()
		```
		"""
		return self.__get_widget_value("input_folder_path")

	def __collect_files( self ) -> List[ Tuple[ str, str ] ]:
		"""
		收集输入文件夹中的图像和JSON文件。

		Returns:
			包含图像文件和对应JSON文件路径的列表，如[(image_path, json_path), ...]
		"""
		input_folder = self.__get_input_folder_path()
		self.__log( f"开始从 {input_folder} 收集文件", color="blue", level="info" )

		if not os.path.exists( input_folder ):
			self.__log( f"输入文件夹不存在: {input_folder}", color="red", level="error" )
			return [ ]

		if not os.path.isdir( input_folder ):
			self.__log( f"指定的路径不是一个文件夹: {input_folder}", color="red", level="error" )
			return [ ]

		image_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff')
		file_pairs = [ ]

		# 统计数据
		total_image_files = 0
		total_json_files = 0
		valid_pairs = 0

		try:
			for filename in os.listdir( input_folder ):
				file_path = os.path.join( input_folder, filename )

				# 跳过非文件
				if not os.path.isfile( file_path ):
					continue

				base_name, ext = os.path.splitext( filename )
				ext = ext.lower()  # 转为小写以进行不区分大小写的比较

				# 检查是否是图像文件
				if ext in image_extensions:
					total_image_files += 1

					# 查找对应的JSON文件
					json_path = os.path.join( input_folder, base_name + '.json' )
					if os.path.exists( json_path ) and os.path.isfile( json_path ):
						total_json_files += 1
						valid_pairs += 1
						file_pairs.append( (file_path, json_path) )
						self.__log( f"找到文件对: {file_path} - {json_path}", level="debug" )

			self.__log(
				f"扫描结果: {total_image_files} 个图像文件, {total_json_files} 个JSON文件, {valid_pairs} 对有效配对",
				color="blue"
			)

		except Exception as e:
			self.__log( f"扫描文件时出错: {str( e )}", color="red", level="error" )
			return [ ]

		self.__log( f"找到 {len( file_pairs )} 对图像和JSON文件", color="blue" )
		return file_pairs

	def __log( self, message: str, color: str = None, level: str = "info" ):
		"""
		打印日志信息。

		Args:
			message: 要打印的信息
			color: 输出颜色，可选
			level: 日志级别，可选，默认为info
		"""
		# 判断是否应该输出到UI界面
		# 1. 带颜色的日志（通常表示重要信息）都输出到UI界面
		# 2. warning和error级别的日志都输出到UI界面
		should_log_to_ui = (
				color is not None or  # 有颜色的日志都显示到UI
				level.lower() == "warning" or level.lower() == "error"
		)

		# 处理log_output记录UI日志，只输出重要的日志
		if self.__log_output and should_log_to_ui:
			self.__log_output.append( message, color=color )

		# 处理logger记录控制台日志，所有级别都记录
		if self.__logger:
			if level.lower() == "debug":
				self.__logger.debug( message )
			elif level.lower() == "warning":
				self.__logger.warning( message )
			elif level.lower() == "error":
				self.__logger.error( message )
			else:
				self.__logger.info( message )
		# 如果两者都不存在，则使用print
		elif not self.__log_output:
			print( message )

	def __update_result_label( self, total_count: int ):
		"""
		更新结果标签显示（重构为映射方式）。

		Args:
			total_count: 标注对象总数量

		使用示例:
		```python
		# 现在使用统一的映射机制更新结果标签
		self.__update_result_label(100)
		```
		"""
		result_text = f"总标注对象: {total_count}"
		success = self.__set_widget_text("result_label", result_text, with_animation=True)

		if success:
			self.__log( f"更新结果显示: {result_text}", level="debug" )
		else:
			self.__log( f"更新结果标签失败", color="red", level="error" )

	def __update_valid_files_label( self, valid_files_count: int ):
		"""
		更新有效文件数量标签显示（重构为映射方式）。

		Args:
			valid_files_count: 有效文件数量

		使用示例:
		```python
		# 现在使用统一的映射机制更新有效文件数量标签
		self.__update_valid_files_label(50)
		```
		"""
		result_text = f"有效文件数量: {valid_files_count}"
		success = self.__set_widget_text("valid_files_label", result_text, with_animation=True)

		if success:
			self.__log( f"更新有效文件数量显示: {result_text}", level="debug" )
		else:
			self.__log( f"更新有效文件数量标签失败", color="red", level="error" )





	def __read_json_file_with_retry(self, json_path: str) -> List[Dict]:
		"""
		带重试机制的JSON文件读取方法。

		Args:
			json_path: JSON文件路径

		Returns:
			包含标注信息的列表
		"""
		for attempt in range(self.__max_retries + 1):
			try:
				# 检查文件是否存在和可读
				if not os.path.exists(json_path):
					raise FileNotFoundError(f"JSON文件不存在: {json_path}")

				if not os.access(json_path, os.R_OK):
					raise PermissionError(f"无法读取JSON文件: {json_path}")

				# 读取文件
				with open(json_path, 'r', encoding='utf-8') as f:
					data = json.load(f)

				# 验证数据结构
				if not isinstance(data, dict):
					raise ValueError(f"JSON文件格式错误，期望dict类型: {json_path}")

				shapes = data.get('shapes', [])
				if not isinstance(shapes, list):
					raise ValueError(f"shapes字段格式错误，期望list类型: {json_path}")

				self.__log(f"从 {json_path} 读取到 {len(shapes)} 个标注形状", level="debug")
				return shapes

			except (FileNotFoundError, PermissionError) as e:
				# 这些错误不需要重试
				error_msg = f"文件访问错误: {json_path}, 错误: {str(e)}"
				self.__log(error_msg, color="red", level="error")
				self.__statistics['errors'].append({
					'file': json_path,
					'error': str(e),
					'type': 'file_access_error'
				})
				return []

			except (json.JSONDecodeError, ValueError) as e:
				# JSON格式错误，不需要重试
				error_msg = f"JSON格式错误: {json_path}, 错误: {str(e)}"
				self.__log(error_msg, color="red", level="error")
				self.__statistics['errors'].append({
					'file': json_path,
					'error': str(e),
					'type': 'json_format_error'
				})
				return []

			except Exception as e:
				# 其他错误，可能是临时性的，尝试重试
				if attempt < self.__max_retries:
					self.__log(
						f"读取JSON文件失败 (尝试 {attempt + 1}/{self.__max_retries + 1}): {json_path}, 错误: {str(e)}",
						color="yellow",
						level="warning"
					)
					time.sleep(0.1 * (attempt + 1))  # 递增延迟
					continue
				else:
					error_msg = f"读取JSON文件最终失败: {json_path}, 错误: {str(e)}"
					self.__log(error_msg, color="red", level="error")
					self.__statistics['errors'].append({
						'file': json_path,
						'error': str(e),
						'type': 'unknown_error',
						'attempts': attempt + 1
					})
					return []

		return []

	def __read_json_file( self, json_path: str ) -> List[ Dict ]:
		"""
		读取JSON文件中的标注信息（保持向后兼容性）。

		Args:
			json_path: JSON文件路径

		Returns:
			包含标注信息的列表
		"""
		return self.__read_json_file_with_retry(json_path)

	def count_annotations( self ) -> int:
		"""
		统计标注对象的总数量（增强版本）。

		Returns:
			标注对象总数量

		使用示例:
		```python
		counter = AnnotationCounter(...)
		total_count = counter.count_annotations()

		# 获取详细报告
		report = counter.get_statistics_report()
		print(f"处理成功率: {report['success_rate']:.1f}%")
		```
		"""
		# 重置统计信息
		self.__reset_statistics()
		self.__statistics['start_time'] = time.time()

		self.__log( "开始统计标注对象总数量...", color="blue", level="info" )

		# 收集文件对
		file_pairs = self.__collect_files()
		if not file_pairs:
			self.__log( "未找到有效的图像和JSON文件对", color="yellow", level="warning" )
			self.__update_result_label( 0 )
			self.__update_valid_files_label( 0 )
			self.__statistics['end_time'] = time.time()
			self.__statistics['processing_time'] = self.__statistics['end_time'] - self.__statistics['start_time']
			return 0

		# 更新统计信息
		self.__statistics['total_files'] = len(file_pairs)
		valid_files_count = len( file_pairs )
		self.__update_valid_files_label( valid_files_count )

		# 统计标注对象总数
		total_count = 0
		processed_count = 0
		failed_count = 0
		total_files = len( file_pairs )

		self.__log( f"开始处理 {total_files} 个文件...", color="blue", level="info" )

		try:
			for image_path, json_path in file_pairs:
				# 检查是否被取消
				if self.__is_cancelled:
					self.__log("操作已被用户取消", color="yellow", level="warning")
					break

				processed_count += 1

				# 读取JSON文件（带重试机制）
				shapes = self.__read_json_file_with_retry( json_path )
				if shapes:
					total_count += len( shapes )
					self.__statistics['processed_files'] += 1
				else:
					failed_count += 1
					self.__statistics['failed_files'] += 1

				# 更新进度显示
				if self.__enable_progress and (
					processed_count % self.__progress_update_interval == 0 or
					processed_count == total_files
				):
					progress_percent = (processed_count / total_files) * 100
					self.__log(
						f"进度: {processed_count}/{total_files} ({progress_percent:.1f}%) - "
						f"已找到 {total_count} 个标注对象",
						color="blue",
						level="info"
					)

					# 更新进度条（如果有的话）
					self.__update_progress_display(processed_count, total_files, total_count)

		except Exception as e:
			self.__log( f"统计标注对象时出错: {str( e )}", color="red", level="error" )
			self.__statistics['errors'].append({
				'error': str(e),
				'type': 'processing_error',
				'context': 'main_loop'
			})

		# 完成统计
		self.__statistics['end_time'] = time.time()
		self.__statistics['processing_time'] = self.__statistics['end_time'] - self.__statistics['start_time']
		self.__statistics['total_annotations'] = total_count

		# 更新结果标签
		self.__update_result_label( total_count )

		# 输出详细的完成报告
		self.__log_completion_report(total_count, valid_files_count, failed_count)

		return total_count

	def __update_progress_display(self, processed: int, total: int, annotations_found: int):
		"""
		更新进度显示（重构为映射方式）。

		Args:
			processed: 已处理文件数
			total: 总文件数
			annotations_found: 已找到的标注数量

		使用示例:
		```python
		# 现在使用统一的映射机制更新进度显示
		self.__update_progress_display(50, 100, 250)
		```
		"""
		try:
			if not self.__label_manager:
				return

			# 更新进度标签
			progress_text = f"进度: {processed}/{total} ({(processed/total)*100:.1f}%)"

			# 使用映射方式更新进度标签
			success = self.__set_widget_text("progress_label", progress_text, with_animation=False)

			if not success:
				# 如果映射的标签不存在，尝试备选标签（直接使用原来的方式作为后备）
				try:
					self.__label_manager.set_text("label_progress", progress_text)
				except:
					try:
						self.__label_manager.set_text("label_status", progress_text)
					except:
						pass  # 如果所有标签都不存在，静默忽略

		except Exception as e:
			self.__log(f"更新进度显示时出错: {str(e)}", level="debug")

	def __log_completion_report(self, total_count: int, valid_files: int, failed_count: int):
		"""
		输出完成报告。

		Args:
			total_count: 总标注数量
			valid_files: 有效文件数量
			failed_count: 失败文件数量
		"""
		processing_time = self.__statistics['processing_time']
		success_rate = ((valid_files - failed_count) / valid_files * 100) if valid_files > 0 else 0

		# 主要完成信息
		self.__log(
			f"统计完成！共找到 {total_count} 个标注对象，有效文件: {valid_files}个",
			color="green",
			level="info"
		)

		# 详细统计信息
		if processing_time > 0:
			files_per_second = (valid_files - failed_count) / processing_time
			self.__log(
				f"处理统计: 成功率 {success_rate:.1f}%, 处理速度 {files_per_second:.1f}文件/秒, "
				f"总耗时 {processing_time:.2f}秒",
				color="blue",
				level="info"
			)

		# 错误报告
		if failed_count > 0:
			self.__log(
				f"警告: {failed_count} 个文件处理失败，请检查文件格式和权限",
				color="yellow",
				level="warning"
			)

		# 如果有错误详情，记录到日志
		if self.__statistics['errors'] and self.__logger:
			self.__logger.debug(f"错误详情: {self.__statistics['errors']}")


class YAMLConfigGenerator:
	"""
	YAML配置文件生成器，用于生成YOLO训练所需的配置文件。

	该类处理以下功能：
	1. 获取输出路径、配置文件路径和标签文件路径
	2. 读取标签文件内容
	3. 生成YAML配置文件
	4. 更新UI中的相关标签显示生成结果

	使用示例:
	```python
	# 在HomeUiOperate中使用
	generator = YAMLConfigGenerator(
		self.__line_edit_manager,
		self.__log_output,
		self.__logger,
		self.__label_manager
	)
	generator.generate_yaml_config()
	```
	"""

	def __init__(
			self, line_edit_manager: LineEditManager, log_output: LogOutput = None, logger: Logger = None,
			label_manager: QLabelManager = None
	):
		"""
		初始化YAML配置文件生成器。

		Args:
			line_edit_manager: 用于获取输入文本的LineEditManager实例
			log_output: 用于输出日志的LogOutput实例，可选
			logger: 用于记录日志的Logger实例，可选
			label_manager: 用于更新UI标签的LabelManager实例，可选
		"""
		self.__line_edit_manager = line_edit_manager
		self.__log_output = log_output
		self.__logger = logger
		self.__label_manager = label_manager

	def __log( self, message: str, color: str = None, level: str = "info" ):
		"""
		打印日志信息。

		Args:
			message: 要打印的信息
			color: 输出颜色，可选
			level: 日志级别，可选，默认为info
		"""
		# 判断是否应该输出到UI界面
		# 1. 带颜色的日志（通常表示重要信息）都输出到UI界面
		# 2. warning和error级别的日志都输出到UI界面
		should_log_to_ui = (
				color is not None or  # 有颜色的日志都显示到UI
				level.lower() == "warning" or level.lower() == "error"
		)

		# 处理log_output记录UI日志，只输出重要的日志
		if self.__log_output and should_log_to_ui:
			self.__log_output.append( message, color=color )

		# 处理logger记录控制台日志，所有级别都记录
		if self.__logger:
			if level.lower() == "debug":
				self.__logger.debug( message )
			elif level.lower() == "warning":
				self.__logger.warning( message )
			elif level.lower() == "error":
				self.__logger.error( message )
			else:
				self.__logger.info( message )
		# 如果两者都不存在，则使用print
		elif not self.__log_output:
			print( message )

	def __update_result_label( self, config_file_name: str ):
		"""
		更新结果标签显示。

		Args:
			config_file_name: 生成的配置文件名
		"""
		try:
			if not self.__label_manager:
				return

			result_text = f"已生成: {config_file_name}"
			self.__label_manager.set_text( "label_51", result_text )

			# 使用淡入淡出动画效果提示用户
			self.__label_manager.start_fade_animation( "label_51", duration_ms=200 )

			self.__log( f"更新结果显示: {result_text}", level="debug" )
		except Exception as e:
			self.__log( f"更新结果标签时出错: {str( e )}", color="red", level="error" )

	def __get_output_path( self ) -> str:
		"""
		获取标注数据输出路径。

		Returns:
			标注数据输出路径字符串
		"""
		path = self.__line_edit_manager.get_line_edit( "lineEdit_7" ).text()  # type: ignore
		self.__log( f"获取标注数据输出路径: {path}", level="debug" )
		return path

	def __get_config_path( self ) -> str:
		"""
		获取配置文件路径。

		Returns:
			配置文件路径字符串
		"""
		path = self.__line_edit_manager.get_line_edit( "lineEdit_12" ).text()  # type: ignore
		self.__log( f"获取配置文件路径: {path}", level="debug" )
		return path

	def __get_labels_file_path( self ) -> str:
		"""
		获取标签文件路径。

		Returns:
			标签文件路径字符串
		"""
		path = self.__line_edit_manager.get_line_edit( "lineEdit_9" ).text()  # type: ignore
		self.__log( f"获取标签文件路径: {path}", level="debug" )
		return path

	def __read_labels_file( self ) -> List[ str ]:
		"""
		读取标签文件内容。

		Returns:
			标签列表，如['person', 'car', ...]
		"""
		labels_file = self.__get_labels_file_path()

		self.__log( f"读取标签文件: {labels_file}", color="blue", level="info" )

		if not os.path.exists( labels_file ):
			self.__log( f"标签文件不存在: {labels_file}", color="red", level="error" )
			return [ ]

		if not os.path.isfile( labels_file ):
			self.__log( f"指定的标签路径不是一个文件: {labels_file}", color="red", level="error" )
			return [ ]

		try:
			with open( labels_file, 'r', encoding='utf-8' ) as f:
				# 读取所有行并过滤空行
				lines = f.readlines()
				labels = [ ]

				for i, line in enumerate( lines, 1 ):
					line = line.strip()
					if line:  # 非空行
						if line in labels:
							self.__log(
								f"警告: 标签文件中发现重复标签 '{line}' (第{i}行)", color="yellow", level="warning"
							)
						else:
							labels.append( line )

			if not labels:
				self.__log( "警告: 标签文件为空或只包含空行", color="yellow", level="warning" )

			self.__log( f"读取到 {len( labels )} 个标签: {', '.join( labels )}", color="blue" )
			return labels

		except Exception as e:
			self.__log( f"读取标签文件出错: {labels_file}, 错误: {str( e )}", color="red", level="error" )
			return [ ]

	def generate_yaml_config( self ) -> bool:
		"""
		生成YAML配置文件。

		Returns:
			操作成功返回True，否则返回False
		"""
		try:
			self.__log( "开始生成YAML配置文件...", color="blue", level="info" )

			# 获取路径
			output_path = self.__get_output_path()
			config_path = self.__get_config_path()

			# 检查路径是否有效
			if not output_path:
				self.__log( "错误: 标注数据输出路径不能为空", color="red", level="error" )
				return False

			if not config_path:
				self.__log( "错误: 配置文件路径不能为空", color="red", level="error" )
				return False

			# 读取标签文件
			labels = self.__read_labels_file()
			if not labels:
				self.__log( "错误: 未能读取到有效的标签", color="red", level="error" )
				return False

			# 创建YAML配置
			yaml_config = {
				'path':  output_path,
				'train': os.sep.join( [ 'images', 'train' ] ),
				'val':   os.sep.join( [ 'images', 'val' ] ),
				'names': {
					i: label
					for i, label in enumerate( labels )
				},
			}

			# 确保配置文件目录存在
			os.makedirs( os.path.dirname( config_path ) if os.path.dirname( config_path ) else '.', exist_ok=True )

			# 保存YAML配置文件
			with open( config_path, 'w', encoding='utf-8' ) as f:
				yaml.dump( yaml_config, f, default_flow_style=False, sort_keys=False, allow_unicode=True )

			self.__log( f"YAML配置文件已生成: {config_path}", color="green", level="info" )

			# 更新结果标签
			self.__update_result_label( os.path.basename( config_path ) )

			return True

		except Exception as e:
			import traceback
			self.__log( f"生成YAML配置文件时发生错误: {str( e )}", color="red", level="error" )
			self.__log( traceback.format_exc(), color="red", level="error" )
			return False


class ExclusiveControlManager:
	"""
	管理一组PyQt5控件（目前支持QCheckBox和QLineEdit），确保在任何时候只有一个控件处于"激活"状态。
	它内嵌了一套复杂的规则，用于判断用户的意图（是按"倍数"还是按"数量"生成），并返回相应的数据。
	- QCheckBox 和 QLineEdit 之间互斥。
	- 控件组内部也互斥。
	"""

	def __init__( self, container: QWidget | QLayout, logger: Optional[ Logger ] = None ):
		"""
		初始化管理器。

		Args:
			container (QWidget | QLayout): 包含受管控件的容器或布局。
			logger (Optional[Logger]): 用于记录日志的Logger实例。
		"""
		self.__container = container
		self.__logger = logger
		self.__checkboxes: Dict[ str, QCheckBox ] = { }
		self.__line_edits: Dict[ str, QLineEdit ] = { }

		self.__find_controls()
		self.__connect_signals()

	def __find_controls( self ):
		"""从容器中递归地查找所有的 QCheckBox 和 QLineEdit 控件。"""
		self.__checkboxes.clear()
		self.__line_edits.clear()

		def __recursive_search( parent_obj ):
			if isinstance( parent_obj, QCheckBox ):
				if parent_obj.objectName() not in self.__checkboxes:
					self.__checkboxes[ parent_obj.objectName() ] = parent_obj
			elif isinstance( parent_obj, QLineEdit ):
				if parent_obj.objectName() not in self.__line_edits:
					self.__line_edits[ parent_obj.objectName() ] = parent_obj

			# 遍历子项
			children = [ ]
			if hasattr( parent_obj, 'children' ):
				children.extend( parent_obj.children() )

			if isinstance( parent_obj, QLayout ):
				for i in range( parent_obj.count() ):
					child_item = parent_obj.itemAt( i )
					if child_item:
						if child_item.widget():
							children.append( child_item.widget() )
						elif child_item.layout():
							children.append( child_item.layout() )

			for child in children:
				if isinstance( child, (QWidget, QLayout) ):
					__recursive_search( child )

		__recursive_search( self.__container )

		if self.__logger:
			self.__logger.debug(
				f"ExclusiveControlManager found {len( self.__checkboxes )} checkboxes and {len( self.__line_edits )} line edits."
			)

	def __connect_signals( self ):
		"""连接所有控件的信号到处理槽函数。"""
		for checkbox in self.__checkboxes.values():
			checkbox.stateChanged.connect( partial( self.__on_checkbox_state_changed, checkbox ) )
		for line_edit in self.__line_edits.values():
			line_edit.textChanged.connect( partial( self.__on_line_edit_text_changed, line_edit ) )

	def __on_checkbox_state_changed( self, checkbox: QCheckBox, state: int ):
		"""当一个 QCheckBox 状态改变时，取消其他所有受管控件的激活状态。"""
		if state == Qt.CheckState.Checked:
			# 取消其他所有 checkbox 的选中状态
			for other_checkbox in self.__checkboxes.values():
				if other_checkbox is not checkbox:
					other_checkbox.blockSignals( True )
					other_checkbox.setChecked( False )
					other_checkbox.blockSignals( False )
			# 清空所有 line edit 的文本
			for line_edit in self.__line_edits.values():
				line_edit.blockSignals( True )
				line_edit.clear()
				line_edit.blockSignals( False )

	def __on_line_edit_text_changed( self, line_edit: QLineEdit, text: str ):
		"""当一个 QLineEdit 文本改变时，取消其他所有受管控件的激活状态。"""
		if text:
			# 取消所有 checkbox 的选中状态
			for checkbox in self.__checkboxes.values():
				checkbox.blockSignals( True )
				checkbox.setChecked( False )
				checkbox.blockSignals( False )
			# 清空其他所有 line edit 的文本
			for other_line_edit in self.__line_edits.values():
				if other_line_edit is not line_edit:
					other_line_edit.blockSignals( True )
					other_line_edit.clear()
					other_line_edit.blockSignals( False )

	def get_active_value( self ) -> Optional[ Dict[ str, Any ] ]:
		"""
		实时扫描并根据预设规则获取当前激活模式的类型和值。

		该方法会根据UI控件的当前状态（哪个被选中、哪个有值）来决定用户的意图。

		Returns:
			Optional[Dict[str, Any]]:
				如果找到一个有效的激活模式，则返回一个字典，
				包含 'type' ('multiple' 或 'quantity') 和 'value' (int)。
				如果状态不明确或无效，则返回 None。

		使用示例:
			```python
			# manager = ExclusiveControlManager(container)

			# 场景1: rotate_2 (文本为"x5") 被选中
			# active_data = manager.get_active_value()
			# print(active_data)  # 输出: {'type': 'multiple', 'value': 5}

			# 场景2: lineEdit_2 输入了 '3'
			# active_data = manager.get_active_value()
			# print(active_data)  # 输出: {'type': 'multiple', 'value': 3}

			# 场景3: lineEdit_3 输入了 '500'
			# active_data = manager.get_active_value()
			# print(active_data)  # 输出: {'type': 'quantity', 'value': 500}
			```
		"""
		checked_checkboxes = [ cb for cb in self.__checkboxes.values() if cb.isChecked() ]
		active_line_edits = {
			name: le.text()
			for name, le in self.__line_edits.items()
			if le.text().strip()
		}

		# 规则1: 当某个QCheckBox被选中，且所有QLineEdit都没有输入时
		if len( checked_checkboxes ) == 1 and not active_line_edits:
			checkbox = checked_checkboxes[ 0 ]
			# 从CheckBox的文本中提取数值作为倍数
			match = re.search( r'\d+', checkbox.text() )
			if match:
				try:
					value = int( match.group( 0 ) )
					if self.__logger:
						self.__logger.debug( f"模式: CheckBox倍数. 控件: {checkbox.objectName()}, 值: {value}" )
					return {
						'type':  'multiple',
						'value': value
					}
				except (ValueError, TypeError):
					if self.__logger:
						self.__logger.warning(
							f"无法从CheckBox {checkbox.objectName()} 的文本 '{checkbox.text()}' 中解析数值。"
						)
			return None

		# 规则2 & 3: 当只有一个QLineEdit有输入，且所有QCheckBox都未选中时
		if not checked_checkboxes and len( active_line_edits ) == 1:
			name, text_value = next( iter( active_line_edits.items() ) )
			try:
				value = int( text_value )
				# 规则2: lineEdit_2 代表"倍数"
				if name == 'lineEdit_2':
					if self.__logger:
						self.__logger.debug( f"模式: LineEdit倍数. 控件: {name}, 值: {value}" )
					return {
						'type':  'multiple',
						'value': value
					}
				# 规则3: lineEdit_3 代表"数量"
				elif name == 'lineEdit_3':
					if self.__logger:
						self.__logger.debug( f"模式: LineEdit数量. 控件: {name}, 值: {value}" )
					return {
						'type':  'quantity',
						'value': value
					}
			except (ValueError, TypeError):
				if self.__logger:
					self.__logger.warning( f"无法从LineEdit {name} 的文本 '{text_value}' 中解析数值。" )

		# 如果不符合任何明确规则（例如多个控件被激活，或没有控件激活）
		if self.__logger:
			self.__logger.debug( "没有找到唯一激活的控件或不符合任何预设规则。" )
		return None

	def cleanup( self ):
		"""断开所有信号连接，以避免内存泄漏。"""
		try:
			for checkbox in self.__checkboxes.values():
				checkbox.stateChanged.disconnect()
			for line_edit in self.__line_edits.values():
				line_edit.textChanged.disconnect()
			if self.__logger:
				self.__logger.info( "ExclusiveControlManager 信号已断开。" )
		except TypeError:
			# 在某些情况下，如果信号从未连接，disconnect可能会引发TypeError。
			# 我们可以安全地忽略它。
			pass


def get_dataset_image_paths( line_edit_manager: LineEditManager, logger: Logger ) -> List[ str ]:
	"""
	扫描数据集中 'images/train' 和 'images/val' 两个子目录，并返回所有有效图像的绝对路径列表。
	这是一个纯逻辑函数，不执行任何UI更新。

	Args:
		line_edit_manager (LineEditManager): 用于获取基础路径的LineEditManager实例。
		logger (Logger): 用于记录详细日志的Logger实例。

	Returns:
		List[str]: 所有有效图像的绝对路径列表。如果发生错误或找不到路径，则返回空列表。
	"""
	IMAGE_EXTENSIONS = { ".jpg", ".jpeg", ".png", ".bmp", ".gif", ".tiff", ".webp" }

	try:
		base_path_edit = line_edit_manager.get_line_edit( "lineEdit_1" )
		if not base_path_edit:
			logger.error( "错误: 未能找到基础路径输入框(objectName='lineEdit_1')。" )
			return [ ]

		base_path = base_path_edit.text()
		if not base_path or not os.path.isdir( base_path ):
			logger.warning( f"基础路径 '{base_path}' 为空或不是一个有效的目录。" )
			return [ ]

		subfolders = [ os.path.join( "images", "train" ), os.path.join( "images", "val" ) ]
		all_image_paths = [ ]

		for subfolder in subfolders:
			target_path = os.path.join( base_path, subfolder )

			if not os.path.isdir( target_path ):
				logger.debug( f"路径无效或不是一个文件夹: '{target_path}'，已跳过。" )
				continue

			for filename in os.listdir( target_path ):
				if os.path.splitext( filename )[ 1 ].lower() in IMAGE_EXTENSIONS:
					all_image_paths.append( os.path.join( target_path, filename ) )

		return all_image_paths

	except Exception as e:
		logger.error( f"扫描图像文件时发生未知错误: {e}", exc_info=True )
		return [ ]


def count_dataset_images(
		line_edit_manager: LineEditManager,
		label_manager: QLabelManager,
		log_output: LogOutput,
		logger: Logger,
		update_ui: bool = True
) -> Tuple[ int, List[ str ] ]:
	"""
	统计数据集中 'images/train' 和 'images/val' 两个子目录下的图像文件总数，并更新UI。
	该函数现在通过调用 get_dataset_image_paths 来获取数据，然后执行UI更新。

	Args:
		line_edit_manager (LineEditManager): 用于获取基础路径的LineEditManager实例。
		label_manager (QLabelManager): 用于更新UI标签的LabelManager实例。
		log_output (LogOutput): 用于向UI输出操作信息的LogOutput实例。
		logger (Logger): 用于记录详细日志的Logger实例。
		update_ui (bool): 是否更新UI。默认为True。

	Returns:
		Tuple[int, List[str]]: 包含图像文件总数量和所有有效图像绝对路径列表的元组。如果发生错误，则返回 (0, [])。
	"""
	if update_ui:
		log_output.append( "开始统计数据集中 train 和 val 文件夹的图像数量...", color="blue" )

	try:
		# 调用核心逻辑函数获取数据
		all_image_paths = get_dataset_image_paths( line_edit_manager, logger )
		total_count = len( all_image_paths )

		# 执行UI更新（如果需要）
		if update_ui:
			base_path = line_edit_manager.get_line_edit( "lineEdit_1" ).text()  # type: ignore
			if not base_path:
				log_output.append( "警告: 基础路径为空，无法进行统计。", color="yellow" )
				label_manager.set_text( "label_12", "路径为空" )
			else:
				log_output.append( f"统计完成：总共找到 {total_count} 张图像。", color="green" )
				label_manager.set_text( "label_12", f"共 {total_count} 张图像" )

		return total_count, all_image_paths

	except Exception as e:
		import traceback
		error_msg = f"统计图像文件时发生未知错误: {e}"
		if update_ui:
			log_output.append( error_msg, color="red" )
			log_output.append( traceback.format_exc(), color="red" )
			label_manager.set_text( "label_12", "统计失败" )
		logger.error( error_msg, exc_info=True )
		return 0, [ ]


def calculate_and_display_augmentation_count(
		line_edit_manager: LineEditManager,
		label_manager: QLabelManager,
		exclusive_manager: ExclusiveControlManager,
		log_output: LogOutput,
		logger: Logger,
		update_ui: bool = True
) -> int:
	"""
	计算并根据用户选择（数量或倍数）显示计划生成的增强图像总数。

	Args:
		line_edit_manager (LineEditManager): LineEdit管理器实例。
		label_manager (QLabelManager): QLabel管理器实例。
		exclusive_manager (ExclusiveControlManager): 互斥控件管理器实例。
		log_output (LogOutput): 日志输出实例。
		logger (Logger): 日志记录器实例。
		update_ui (bool, optional): 是否更新UI。默认为True。

	Returns:
		int: 计算出的增强图像总数。
	"""
	if logger:
		logger.info( "开始计算增强图像数量..." )

	try:
		# 1. 首先，获取数据集中的总图像数量
		if logger:
			logger.debug( "从文件系统扫描获取图像总数。" )
		total_images, _ = count_dataset_images(
			line_edit_manager,
			label_manager,
			log_output,
			logger,
			update_ui=False  # 在此上下文中，我们不希望此调用更新UI
		)

		if total_images == 0:
			if update_ui:
				log_output.append( "数据集中没有图像，无法计算增强数量。", color="yellow" )
			return 0

		# 2. 获取用户选择的增强模式（例如，按总数或按图像）
		active_control = exclusive_manager.get_active_value()
		if not active_control:
			if update_ui:
				log_output.append( "请选择一种增强数量模式（例如，按总数或按图像）。", color="yellow" )
			return 0

		mode_type = active_control.get( "type" )
		mode_value = active_control.get( "value" )
		final_count = 0

		# 3. 根据模式类型计算最终数量
		if mode_type == "quantity":
			if isinstance( mode_value, int ):
				final_count = mode_value
				if update_ui:
					log_output.append( f"模式：按总数。计划生成 {final_count} 张图像。", color="gray" )
			else:
				if update_ui:
					log_output.append( f"警告：无效的数量值 '{mode_value}'，无法计算。", color="yellow" )
					label_manager.set_text( "label_8", "无效值" )
				return 0

		elif mode_type == "multiple":
			if isinstance( mode_value, int ):
				final_count = total_images * mode_value
				if update_ui:
					log_output.append(
						f"模式：按图像。{total_images} 张图像 * {mode_value} 倍 = {final_count} 张。", color="gray"
					)
			else:
				if update_ui:
					log_output.append( f"警告：无效的倍数值 '{mode_value}'，无法计算。", color="yellow" )
					label_manager.set_text( "label_8", "无效值" )
				return 0

		else:
			if update_ui:
				log_output.append( f"警告：未知的模式类型 '{mode_type}'，无法计算。", color="yellow" )
				label_manager.set_text( "label_8", "未知模式" )
			return 0

		# 4. 更新UI并返回结果
		if update_ui:
			label_manager.set_text( "label_8", str( final_count ) )
			log_output.append( f"计算完成：最终计划生成 {final_count} 张增强图像。", color="green" )
		return final_count

	except Exception as e:
		import traceback
		error_msg = f"计算增强数量时发生未知错误: {e}"
		if update_ui:
			log_output.append( error_msg, color="red" )
			log_output.append( traceback.format_exc(), color="red" )
			label_manager.set_text( "label_8", "计算失败" )
		logger.error( error_msg, exc_info=True )
		return 0


def load_yolo_format_data( line_edit_manager: LineEditManager, logger: Logger ) -> List[ List[ Any ] ]:
	"""
	加载YOLO11格式数据集，解析标签并将其转换为包含绝对坐标的特定格式。
	支持两种格式：YOLO11 Segment（多边形分割）和YOLO11 OBB（定向边界框）。

	此函数执行以下操作：
	1.  从指定的QLineEdit (objectName='lineEdit_1') 获取数据集的基础路径。
	2.  扫描 'images/train' 和 'images/val' 子目录以查找所有图像文件。
	3.  对于每个图像文件，在 'labels/train' 和 'labels/val' 中查找同名的标签文件 (.txt)。
	4.  读取每个图像的尺寸（宽度和高度）。
	5.  读取每个标签文件的内容。每一行代表一个目标，格式为 "class_index norm_x1 norm_y1 ..."。
	   - 对于Segment格式，点的数量可变
	   - 对于OBB格式，点的数量固定为4（表示4个角点）
	6.  将归一化的坐标点 (norm_x, norm_y) 转换为绝对像素坐标 (x, y)。
	7.  将每个图像及其所有标注数据组装成一个列表，格式如下：
		`[image_path, [polygon_1, polygon_2, ...], [class_index_1, class_index_2, ...]]`
		其中:
		- `image_path` (str): 图像的绝对路径。
		- `[polygon_1, ...]` (List[List[float]]): 一个包含所有多边形列表的列表。每个子列表都是一个扁平化的坐标列表 `[x1, y1, ...]`。
		- `[class_index_1, ...]` (List[int]): 一个包含该图像所有多边形对应类别索引的列表。

	Args:
		line_edit_manager (LineEditManager): 用于获取基础路径的LineEditManager实例。
		logger (Logger): 用于记录详细日志的Logger实例。

	Returns:
		List[List[Any]]:
			一个列表，其中每个子列表代表一个带标注的图像。
			例如: `[['path/to/img1.jpg', [[x1,y1,...]], [c1]], ['path/to/img2.jpg', [[...]], [c2]]]`
			如果基础路径无效或未找到文件，则返回空列表。
	"""
	import cv2
	logger.info( "开始加载YOLO11格式数据集..." )

	IMAGE_EXTENSIONS = { ".jpg", ".jpeg", ".png", ".bmp", ".gif", ".tiff", ".webp" }

	base_path_edit = line_edit_manager.get_line_edit( "lineEdit_1" )
	if not base_path_edit:
		logger.error( "错误: 未能找到基础路径输入框(objectName='lineEdit_1')。" )
		return [ ]

	base_path = base_path_edit.text()
	if not base_path or not os.path.isdir( base_path ):
		logger.error( f"错误: 基础路径 '{base_path}' 无效或不是一个目录。" )
		return [ ]

	final_data_list = [ ]
	subfolders = [ 'train', 'val' ]

	for folder in subfolders:
		image_dir = os.path.join( base_path, 'images', folder )
		label_dir = os.path.join( base_path, 'labels', folder )

		if not os.path.isdir( image_dir ):
			logger.warning( f"警告: 图像目录不存在，已跳过: '{image_dir}'" )
			continue
		if not os.path.isdir( label_dir ):
			logger.warning( f"警告: 标签目录不存在，已跳过: '{label_dir}'" )
			continue

		for image_filename in os.listdir( image_dir ):
			# 确保处理的是文件且是受支持的图像格式
			if not os.path.isfile( os.path.join( image_dir, image_filename ) ) or \
					not any( image_filename.lower().endswith( ext ) for ext in IMAGE_EXTENSIONS ):
				continue

			base_name, image_ext = os.path.splitext( image_filename )
			label_filename = base_name + '.txt'
			image_path = os.path.join( image_dir, image_filename )
			label_path = os.path.join( label_dir, label_filename )

			if not os.path.exists( label_path ):
				logger.debug( f"调试: 未找到对应的标签文件，已跳过图像: '{image_path}'" )
				continue

			try:
				# 1. 读取图像以获取尺寸，用于坐标反归一化
				img = cv2.imread( image_path )
				if img is None:
					logger.error( f"错误: 无法读取图像文件: '{image_path}'" )
					continue
				img_height, img_width = img.shape[ :2 ]

				# 2. 读取并解析标签文件
				with open( label_path, 'r', encoding='utf-8' ) as f:
					lines = f.readlines()

				if not lines:
					continue

				all_polygons = [ ]
				all_class_indices = [ ]

				for line in lines:
					parts = line.strip().split()
					if len( parts ) < 3:  # 必须至少有 class_id 和一个点 (x, y)
						continue

					# 第一个元素是类别索引
					all_class_indices.append( int( parts[ 0 ] ) )

					# 后续元素是成对的归一化坐标
					normalized_coords = [ float( p ) for p in parts[ 1: ] ]

					# 反归一化
					denormalized_polygon = [ ]
					for i in range( 0, len( normalized_coords ), 2 ):
						norm_x = normalized_coords[ i ]
						norm_y = normalized_coords[ i + 1 ]
						abs_x = norm_x * img_width
						abs_y = norm_y * img_height
						denormalized_polygon.extend( [ abs_x, abs_y ] )

					all_polygons.append( denormalized_polygon )

				# 3. 按照指定的特殊格式组装数据
				# 格式: [image_path, list_of_polygons, list_of_class_indices]
				if all_polygons:
					image_data_entry = [ image_path, all_polygons, all_class_indices ]
					final_data_list.append( image_data_entry )

			except Exception as e:
				logger.error( f"处理文件 '{image_path}' 时发生错误: {e}", exc_info=True )

	logger.info( f"数据集加载完成。共处理了 {len( final_data_list )} 个带标注的图像。" )
	return final_data_list


class AnnotatedDataProcessor:
	"""
	一个功能强大的标注数据处理器，用于将特定格式的JSON标注数据转换为YOLOv11 Segment训练所需的格式。

	该类将复杂的处理流程封装起来，包括：
	1.  从UI获取所有必要的路径和参数。
	2.  读取包含图像文件和对应JSON标注文件（每个图像文件对应一个JSON文件）。
	3.  根据用户指定的比例，将数据集随机划分为训练集和验证集。
	4.  为训练集和验证集创建标准的YOLO目录结构 (`images/train`, `labels/train`, etc.)。
	5.  将图像文件复制到相应的目录。
	6.  读取每个图像的尺寸，将多边形的绝对坐标归一化，并生成YOLO格式的`.txt`标签文件。
	7.  通过UI管理器（标签）实时反馈处理进度。
	8.  根据用户选择，在处理完成后选择性地清空源数据目录。

	使用示例:
	```python
	# 在您的UI操作类中 (例如 HomeUiOperate)
	class HomeUiOperate:
		def __init__(self):
			# ... 初始化所有管理器 ...
			self.processor = AnnotatedDataProcessor(
				line_edit_manager=self.__line_edit_manager,
				checkbox_manager=self.__checkbox_manager,
				label_manager=self.__label_manager,
				log_output=self.__log_output,
				logger=self.__logger
			)

		def on_start_processing_button_clicked(self):
			# 异步执行以避免UI阻塞
			from threading import Thread
			thread = Thread(target=self.processor.process_dataset)
			thread.daemon = True
			thread.start()
	```
	"""

	def __init__(
			self,
			line_edit_manager: LineEditManager,
			checkbox_manager: CheckBoxManager,
			label_manager: QLabelManager,
			log_output: Optional[ LogOutput ] = None,
			logger: Optional[ Logger ] = None,
	):
		"""
		初始化标注数据处理器。

		Args:
			line_edit_manager (LineEditManager): LineEdit管理器，用于获取输入路径。
			checkbox_manager (CheckBoxManager): CheckBox管理器，用于获取选项状态。
			label_manager (QLabelManager): 标签管理器，用于更新状态文本。
			log_output (Optional[LogOutput]): UI日志输出实例。
			logger (Optional[Logger]): 标准日志记录器实例。
		"""
		self.__line_edit_manager = line_edit_manager
		self.__checkbox_manager = checkbox_manager
		self.__label_manager = label_manager
		self.__log_output = log_output
		self.__logger = logger
		self.__IMAGE_EXTENSIONS = { '.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff', '.webp' }

		# PyQt5控件统一映射配置字典
		# 建立语义化的控件访问接口，便于后续修改和扩展
		self.__WIDGET_MAPPING = {
			# LineEdit控件 - 用户输入相关
			"line_edits": {
				"source_folder_path": {
					"widget_id": "lineEdit_4",
					"widget_type": "QLineEdit",
					"description": "源数据文件夹路径输入框 - 用于指定包含图像和对应JSON标注文件的源数据文件夹路径",
					"function": "获取用户输入的源数据文件夹路径，系统将从该路径读取图像文件和对应的JSON标注文件进行处理"
				},
				"output_folder_path": {
					"widget_id": "lineEdit_1",
					"widget_type": "QLineEdit",
					"description": "输出文件夹路径输入框 - 用于指定YOLO格式数据集的输出目录路径",
					"function": "获取用户输入的输出文件夹路径，系统将在该路径下创建标准YOLO目录结构并保存转换后的数据集"
				},
				"split_ratio": {
					"widget_id": "lineEdit_5",
					"widget_type": "QLineEdit",
					"description": "数据集划分比例输入框 - 用于指定训练集和验证集的划分比例",
					"function": "获取用户输入的划分比例（格式如'85:15'），系统将按此比例随机划分数据集为训练集和验证集"
				}
			},

			# CheckBox控件 - 选项配置相关
			"checkboxes": {
				"clear_source_folder": {
					"widget_id": "rotate_8",
					"widget_type": "QCheckBox",
					"description": "清空源文件夹复选框 - 控制处理完成后是否清空源数据文件夹",
					"function": "当选中时，系统在数据集转换完成后将自动清空源数据文件夹中的所有文件和子文件夹"
				},
				"use_validation_set": {
					"widget_id": "rotate_31",
					"widget_type": "QCheckBox",
					"description": "使用验证集复选框 - 控制是否创建验证集",
					"function": "当选中时，系统按指定比例划分训练集和验证集；未选中时，所有数据仅划分到训练集中"
				}
			},

			# Label控件 - 进度显示相关
			"labels": {
				"train_progress_display": {
					"widget_id": "label_24",
					"widget_type": "QLabel",
					"description": "训练集处理进度显示标签 - 实时显示训练集数据处理的当前进度",
					"function": "显示训练集处理进度，格式为'当前处理数量 / 总数量'，为用户提供实时的处理状态反馈"
				},
				"validation_progress_display": {
					"widget_id": "label_27",
					"widget_type": "QLabel",
					"description": "验证集处理进度显示标签 - 实时显示验证集数据处理的当前进度",
					"function": "显示验证集处理进度，格式为'当前处理数量 / 总数量'，为用户提供实时的处理状态反馈"
				}
			}
		}

	def __get_widget_value(self, semantic_name: str) -> str:
		"""
		根据语义化名称获取控件的文本值。

		Args:
			semantic_name (str): 控件的语义化名称（在__WIDGET_MAPPING中定义）

		Returns:
			str: 控件的文本值，如果控件不存在则返回空字符串

		Usage Example:
			```python
			# 获取源文件夹路径
			source_path = self.__get_widget_value("source_folder_path")

			# 获取输出文件夹路径
			output_path = self.__get_widget_value("output_folder_path")

			# 获取划分比例
			split_ratio = self.__get_widget_value("split_ratio")
			```
		"""
		try:
			# 查找控件配置
			widget_config = None
			for category, widgets in self.__WIDGET_MAPPING.items():
				if semantic_name in widgets:
					widget_config = widgets[semantic_name]
					break

			if not widget_config:
				if self.__logger:
					self.__logger.warning(f"未找到语义化名称 '{semantic_name}' 对应的控件映射")
				return ""

			widget_id = widget_config["widget_id"]
			widget_type = widget_config["widget_type"]

			# 根据控件类型获取值
			if widget_type == "QLineEdit":
				text_value = self.__line_edit_manager.get_text(widget_id)
				return text_value if text_value is not None else ""
			else:
				if self.__logger:
					self.__logger.warning(f"控件类型 '{widget_type}' 不支持获取文本值")
				return ""

		except Exception as e:
			if self.__logger:
				self.__logger.error(f"获取控件 '{semantic_name}' 的值时发生错误: {e}")
			return ""

	def __set_widget_text(self, semantic_name: str, text: str) -> bool:
		"""
		根据语义化名称设置控件的文本值。

		Args:
			semantic_name (str): 控件的语义化名称（在__WIDGET_MAPPING中定义）
			text (str): 要设置的文本值

		Returns:
			bool: 设置成功返回True，失败返回False

		Usage Example:
			```python
			# 设置源文件夹路径
			success = self.__set_widget_text("source_folder_path", "/path/to/source")

			# 设置输出文件夹路径
			success = self.__set_widget_text("output_folder_path", "/path/to/output")

			# 设置划分比例
			success = self.__set_widget_text("split_ratio", "85:15")
			```
		"""
		try:
			# 查找控件配置
			widget_config = None
			for category, widgets in self.__WIDGET_MAPPING.items():
				if semantic_name in widgets:
					widget_config = widgets[semantic_name]
					break

			if not widget_config:
				if self.__logger:
					self.__logger.warning(f"未找到语义化名称 '{semantic_name}' 对应的控件映射")
				return False

			widget_id = widget_config["widget_id"]
			widget_type = widget_config["widget_type"]

			# 根据控件类型设置值
			if widget_type == "QLineEdit":
				self.__line_edit_manager.set_text(widget_id, text)
				return True
			else:
				if self.__logger:
					self.__logger.warning(f"控件类型 '{widget_type}' 不支持设置文本值")
				return False

		except Exception as e:
			if self.__logger:
				self.__logger.error(f"设置控件 '{semantic_name}' 的值时发生错误: {e}")
			return False

	def __get_checkbox_state(self, semantic_name: str) -> bool:
		"""
		根据语义化名称获取复选框的选中状态。

		Args:
			semantic_name (str): 控件的语义化名称（在__WIDGET_MAPPING中定义）

		Returns:
			bool: 复选框选中返回True，未选中或获取失败返回False

		Usage Example:
			```python
			# 获取清空源文件夹选项状态
			clear_source = self.__get_checkbox_state("clear_source_folder")

			# 获取使用验证集选项状态
			use_validation = self.__get_checkbox_state("use_validation_set")
			```
		"""
		try:
			# 查找控件配置
			widget_config = None
			for category, widgets in self.__WIDGET_MAPPING.items():
				if semantic_name in widgets:
					widget_config = widgets[semantic_name]
					break

			if not widget_config:
				if self.__logger:
					self.__logger.warning(f"未找到语义化名称 '{semantic_name}' 对应的控件映射")
				return False

			widget_id = widget_config["widget_id"]
			widget_type = widget_config["widget_type"]

			# 根据控件类型获取状态
			if widget_type == "QCheckBox":
				checkbox = self.__checkbox_manager.get_checkbox_by_object_name(widget_id)
				if checkbox:
					return checkbox.isChecked()
				else:
					if self.__logger:
						self.__logger.warning(f"复选框控件 '{widget_id}' 不存在")
					return False
			else:
				if self.__logger:
					self.__logger.warning(f"控件类型 '{widget_type}' 不是复选框")
				return False

		except Exception as e:
			if self.__logger:
				self.__logger.error(f"获取复选框 '{semantic_name}' 的状态时发生错误: {e}")
			return False

	def __update_label_progress(self, semantic_name: str, current_value: int, total_value: int) -> bool:
		"""
		根据语义化名称更新标签的进度显示。

		Args:
			semantic_name (str): 控件的语义化名称（在__WIDGET_MAPPING中定义）
			current_value (int): 当前进度值
			total_value (int): 总进度值

		Returns:
			bool: 更新成功返回True，失败返回False

		Usage Example:
			```python
			# 更新训练集处理进度
			success = self.__update_label_progress("train_progress_display", 50, 100)

			# 更新验证集处理进度
			success = self.__update_label_progress("validation_progress_display", 25, 50)
			```
		"""
		try:
			# 查找控件配置
			widget_config = None
			for category, widgets in self.__WIDGET_MAPPING.items():
				if semantic_name in widgets:
					widget_config = widgets[semantic_name]
					break

			if not widget_config:
				if self.__logger:
					self.__logger.warning(f"未找到语义化名称 '{semantic_name}' 对应的控件映射")
				return False

			widget_id = widget_config["widget_id"]
			widget_type = widget_config["widget_type"]

			# 根据控件类型更新进度
			if widget_type == "QLabel":
				if self.__label_manager:
					self.__label_manager.set_text(widget_id, f"{current_value} / {total_value}")
					return True
				else:
					if self.__logger:
						self.__logger.warning("标签管理器不可用")
					return False
			else:
				if self.__logger:
					self.__logger.warning(f"控件类型 '{widget_type}' 不是标签")
				return False

		except Exception as e:
			if self.__logger:
				self.__logger.error(f"更新标签 '{semantic_name}' 的进度时发生错误: {e}")
			return False

	def __get_widget_info(self, semantic_name: str) -> Optional[dict]:
		"""
		获取控件的详细配置信息。

		Args:
			semantic_name (str): 控件的语义化名称

		Returns:
			Optional[dict]: 控件的配置信息字典，如果不存在则返回None

		Usage Example:
			```python
			# 获取控件配置信息
			info = self.__get_widget_info("source_folder_path")
			if info:
				print(f"控件ID: {info['widget_id']}")
				print(f"控件类型: {info['widget_type']}")
				print(f"功能描述: {info['description']}")
			```
		"""
		try:
			# 查找控件配置
			for category, widgets in self.__WIDGET_MAPPING.items():
				if semantic_name in widgets:
					return widgets[semantic_name].copy()
			return None

		except Exception as e:
			if self.__logger:
				self.__logger.error(f"获取控件 '{semantic_name}' 的信息时发生错误: {e}")
			return None

	def __log( self, message: str, color: Optional[ str ] = None, level: str = "info" ):
		"""
		统一的日志记录方法。

		Args:
			message (str): 日志消息。
			color (Optional[str]): UI日志的颜色。
			level (str): 日志级别 ('info', 'warning', 'error', 'debug')。
		"""
		if self.__log_output:
			self.__log_output.append( message, color=color )

		if self.__logger:
			log_func = getattr( self.__logger, level.lower(), self.__logger.info )
			log_func( message )

	def __update_label_text( self, label_name: str, current_value: int, total_value: int ):
		"""
		更新标签文本显示当前进度。

		Args:
			label_name (str): 文本标签的objectName。
			current_value (int): 当前进度值。
			total_value (int): 总进度值。
		"""
		if self.__label_manager:
			self.__label_manager.set_text( label_name, f"{current_value} / {total_value}" )

	def __get_paths_and_ratio( self ) -> Optional[ Tuple[ str, str, Tuple[ float, float ] ] ]:
		"""从UI获取所有必要的路径和划分比例。"""
		try:
			# 使用统一的控件映射接口获取配置值
			source_dir = self.__get_widget_value("source_folder_path")
			output_dir = self.__get_widget_value("output_folder_path")
			ratio_str = self.__get_widget_value("split_ratio")

			if not all( [ source_dir, output_dir, ratio_str ] ):
				self.__log( message="错误: 源数据文件夹、输出文件夹和划分比例均不能为空。", color="red", level="error" )
				return None

			if not re.match( r'^\d+:\d+$', ratio_str ):
				self.__log(
					message=f"错误: 划分比例 '{ratio_str}' 格式不正确，应为 '数字:数字' (例如 '85:15')。", color="red",
					level="error"
				)
				return None

			train_part, val_part = map( int, ratio_str.split( ':' ) )
			total = train_part + val_part
			if total == 0:
				self.__log( message="错误: 划分比例总和不能为0。", color="red", level="error" )
				return None

			ratio = (train_part / total, val_part / total)

			return source_dir, output_dir, ratio
		except Exception as e:
			self.__log( message=f"获取路径或比例时出错: {e}", color="red", level="error" )
			return None

	def __collect_annotation_data( self, source_dir: str ) -> Optional[ List[ List[ Any ] ] ]:
		"""
		收集并处理源目录中的所有图像和对应的JSON标注文件。

		新的数据组织方式是：
		- 每个图像文件对应一个同名的JSON文件
		- JSON文件内容格式为：[图像路径, [多边形标注], [类别索引]]

		Args:
			source_dir (str): 源数据目录路径。

		Returns:
			Optional[List[List[Any]]]: 处理后的标注数据列表，格式与原__read_annotated_json方法兼容。
			                          如果处理失败，则返回None。
		"""
		self.__log( message=f"开始扫描源目录: {source_dir}", color="blue", level="info" )

		if not os.path.isdir( source_dir ):
			self.__log( message=f"错误: 源目录 '{source_dir}' 不存在或不是一个有效的目录。", color="red", level="error" )
			return None

		# 扫描目录中的所有文件
		try:
			all_files = os.listdir( source_dir )
		except Exception as e:
			self.__log( message=f"扫描源目录失败: {e}", color="red", level="error" )
			return None

		# 识别所有图像文件
		image_files = [ f for f in all_files if any( f.lower().endswith( ext ) for ext in self.__IMAGE_EXTENSIONS ) ]

		if not image_files:
			self.__log( message=f"警告: 在源目录中未找到任何图像文件。", color="yellow", level="warning" )
			return None

		self.__log( message=f"找到 {len( image_files )} 个图像文件，开始查找对应的JSON文件...", color="blue" )

		# 初始化结果列表和统计信息
		result_data = [ ]
		valid_pairs = 0
		missing_json = 0
		invalid_json = 0

		# 设置标签显示总文件数
		total_files = len( image_files )
		self.__update_label_progress( "train_progress_display", 0, total_files )

		# 处理每个图像文件
		for i, img_file in enumerate( image_files ):
			try:
				# 更新进度标签
				self.__update_label_progress( "train_progress_display", i + 1, total_files )

				# 获取对应的JSON文件名
				base_name, _ = os.path.splitext( img_file )
				json_file = base_name + '.json'
				json_path = os.path.join( source_dir, json_file )
				img_path = os.path.join( source_dir, img_file )

				# 检查JSON文件是否存在
				if not os.path.exists( json_path ):
					missing_json += 1
					if missing_json <= 5:  # 只记录前5个，避免日志过多
						self.__log(
							message=f"警告: 未找到图像 '{img_file}' 的对应JSON文件。",
							color="yellow", level="warning"
							)
					continue

				# 读取JSON文件
				try:
					with open( json_path, 'r', encoding='utf-8' ) as f:
						data = json.load( f )

					# 验证数据格式
					if not isinstance( data, list ) or len( data ) != 3:
						invalid_json += 1
						if invalid_json <= 5:
							self.__log(
								message=f"警告: JSON文件 '{json_file}' 格式不正确，应为 [图像路径, [多边形], [类别]]。",
								color="yellow", level="warning"
								)
						continue

					# 获取图像路径、多边形和类别
					_, polygons, class_indices = data

					# 确保多边形和类别数量一致
					if len( polygons ) != len( class_indices ):
						invalid_json += 1
						if invalid_json <= 5:
							self.__log(
								message=f"警告: JSON文件 '{json_file}' 中的多边形数量与类别数量不一致。",
								color="yellow", level="warning"
								)
						continue

					# 使用实际的图像路径，而不是JSON中可能包含的路径
					# 这确保了正确的图像被处理，无论JSON中记录的路径是什么
					valid_pairs += 1
					result_data.append( [ img_path, polygons, class_indices ] )

				except json.JSONDecodeError:
					invalid_json += 1
					if invalid_json <= 5:
						self.__log(
							message=f"警告: JSON文件 '{json_file}' 不是有效的JSON格式。",
							color="yellow", level="warning"
							)
					continue
				except Exception as e:
					invalid_json += 1
					if invalid_json <= 5:
						self.__log(
							message=f"处理JSON文件 '{json_file}' 时出错: {e}",
							color="yellow", level="warning"
							)
					continue

			except Exception as e:
				self.__log( message=f"处理文件对时发生未知错误: {e}", color="red", level="error" )

		# 完成处理后显示统计信息
		self.__log(
			message=f"扫描完成: 共处理 {total_files} 个图像文件，"
			        f"找到 {valid_pairs} 对有效图像-JSON对，"
			        f"缺少 {missing_json} 个JSON文件，"
			        f"有 {invalid_json} 个JSON文件格式无效。",
			color="green"
			)

		if not result_data:
			self.__log( message="错误: 未找到任何有效的图像-JSON对。", color="red", level="error" )
			return None

		return result_data

	def __split_data( self, data: List[ List[ Any ] ], ratio: Tuple[ float, float ] ) -> Tuple[ List, List ]:
		"""将数据随机划分为训练集和验证集。"""
		self.__log(
			message=f"开始划分数据集，比例: {int( ratio[ 0 ] * 100 )}% 训练 / {int( ratio[ 1 ] * 100 )}% 验证..."
		)
		random.shuffle( data )
		train_size = int( len( data ) * ratio[ 0 ] )
		train_data = data[ :train_size ]
		val_data = data[ train_size: ]
		self.__log( message=f"划分完成: {len( train_data )} 条训练数据, {len( val_data )} 条验证数据。", color="blue" )
		return train_data, val_data

	def __save_yolo_dataset(
			self, dataset: List[ List[ Any ] ], image_dir: str, label_dir: str,
			semantic_label_name: str, dataset_name: str
	):
		"""
		核心处理函数：保存单个数据集（训练或验证）为YOLO格式。
		"""
		os.makedirs( image_dir, exist_ok=True )
		os.makedirs( label_dir, exist_ok=True )

		total_files = len( dataset )
		self.__log( message=f"开始处理 {dataset_name}: 共 {total_files} 个文件...", color="blue" )

		# 初始化标签
		self.__update_label_progress( semantic_label_name, 0, total_files )

		for i, item in enumerate( dataset ):
			try:
				image_path, polygons, class_indices = item

				# 1. 复制图像
				shutil.copy( image_path, image_dir )

				# 2. 读取图像尺寸
				img = cv2.imread( image_path )
				if img is None:
					self.__log( message=f"警告: 无法读取图像 '{image_path}'，已跳过。", color="yellow", level="warning" )
					continue
				height, width = img.shape[ :2 ]

				# 3. 创建并写入标签文件
				label_filename = os.path.splitext( os.path.basename( image_path ) )[ 0 ] + ".txt"
				label_path = os.path.join( label_dir, label_filename )

				yolo_lines = [ ]
				for polygon, class_index in zip( polygons, class_indices ):
					# 归一化坐标
					normalized_coords = [ ]

					# 遍历多边形的每个点 [x, y]
					for point in polygon:
						try:
							# 确保point是有效的坐标对
							x, y = point
							norm_x = x / width
							norm_y = y / height
							normalized_coords.append( f"{norm_x:.6f}" )
							normalized_coords.append( f"{norm_y:.6f}" )
						except (ValueError, TypeError):
							self.__log(
								message=f"警告: 在文件 {os.path.basename( image_path )} 中发现无效的坐标点格式，已跳过: {point}",
								color="yellow", level="warning"
							)
							continue

					# 格式化为YOLO行
					if normalized_coords:
						line = f"{class_index} {' '.join( normalized_coords )}"
						yolo_lines.append( line )

				with open( label_path, 'w', encoding='utf-8' ) as f:
					f.write( "\n".join( yolo_lines ) )

				# 4. 更新进度
				self.__update_label_progress( semantic_label_name, i + 1, total_files )

			except Exception as e:
				self.__log(
					message=f"处理文件 '{item[ 0 ] if item else 'N/A'}' 时发生错误: {e}", color="red", level="error"
				)

		self.__log( message=f"{dataset_name} 处理完成。", color="green" )

	def __cleanup_source_if_needed( self, source_dir: str ):
		"""如果复选框被选中，则清空源文件夹内容。"""
		# 使用统一的控件映射接口获取复选框状态
		if self.__get_checkbox_state("clear_source_folder"):
			self.__log( message=f"开始清空源数据文件夹: {source_dir}", color="orange", level="warning" )
			try:
				for filename in os.listdir( source_dir ):
					file_path = os.path.join( source_dir, filename )
					if os.path.isfile( file_path ) or os.path.islink( file_path ):
						os.unlink( file_path )
					elif os.path.isdir( file_path ):
						shutil.rmtree( file_path )
				self.__log( message="源数据文件夹已清空。", color="green" )
			except Exception as e:
				self.__log( message=f"清空源文件夹时出错: {e}", color="red", level="error" )

	def process_dataset( self ):
		"""
		执行完整的数据集处理与转换流程。
		这是一个入口方法。
		"""
		self.__log( message="开始YOLOv11数据集生成流程...", color="blue", level="info" )
		try:
			# 1. 获取路径和比例
			paths_and_ratio = self.__get_paths_and_ratio()
			if not paths_and_ratio:
				return
			source_dir, output_dir, ratio = paths_and_ratio

			# 2. 收集标注数据
			annotated_data = self.__collect_annotation_data( source_dir )
			if not annotated_data:
				return

			# 3. 检查是否使用验证集的状态，决定是否划分验证集
			use_validation_set = self.__get_checkbox_state("use_validation_set")
			if not use_validation_set:
				self.__log( message="检测到未选中使用验证集选项，所有数据将仅划分到训练集中，忽略比例设置。", color="blue" )

			# 4. 根据use_validation_set决定如何划分数据
			if use_validation_set:
				# 正常划分训练集和验证集
				train_data, val_data = self.__split_data( annotated_data, ratio )
			else:
				# 所有数据作为训练集，验证集为空
				train_data = annotated_data
				val_data = [ ]
				self.__log( message=f"数据集划分完成: {len( train_data )} 条训练数据, 0 条验证数据。", color="blue" )

			# 5. 保存训练集
			self.__save_yolo_dataset(
				dataset=train_data,
				image_dir=os.path.join( output_dir, 'images', 'train' ),
				label_dir=os.path.join( output_dir, 'labels', 'train' ),
				semantic_label_name="train_progress_display",
				dataset_name="训练集"
			)

			# 6. 保存验证集(如果有)
			if val_data:
				self.__save_yolo_dataset(
					dataset=val_data,
					image_dir=os.path.join( output_dir, 'images', 'val' ),
					label_dir=os.path.join( output_dir, 'labels', 'val' ),
					semantic_label_name="validation_progress_display",
					dataset_name="验证集"
				)

			# 7. 清理源文件夹 (如果需要)
			self.__cleanup_source_if_needed( source_dir )

			self.__log( message="所有任务处理完成！", color="green", level="info" )

		except Exception as e:
			import traceback
			self.__log( message=f"处理过程中发生严重错误: {e}", color="red", level="error" )
			self.__log( message=traceback.format_exc(), color="red", level="error" )


class AnnotationViewer:
	"""
	一个用于预览带有多边形标注图像的工具类。

	该类能够读取一个包含标注信息的JSON文件，随机选择指定数量的图像，
	然后在这些图像上绘制多边形和对应的类别索引，并使用matplotlib弹窗进行预览。

	核心功能:
	1. 从UI获取数据源路径和预览数量。
	2. 读取图像对应的JSON标注文件。
	3. 随机打乱数据并选择指定数量的样本进行预览。
	4. 在图像上可视化多边形标注和类别。
	5. 支持在独立的弹窗中以网格形式显示多张图像。
	"""

	def __init__(
			self, line_edit_manager: LineEditManager, logger: Optional[ Logger ] = None,
			log_output: Optional[ LogOutput ] = None
	):
		"""
		初始化标注预览器。

		Args:
			line_edit_manager (LineEditManager): LineEdit管理器，用于获取用户输入的路径和参数。
			logger (Optional[Logger]): 用于记录详细调试信息的日志记录器实例。
			log_output (Optional[LogOutput]): 用于向UI界面输出用户可见日志的实例。
		"""
		self.__line_edit_manager = line_edit_manager
		self.__logger = logger
		self.__log_output = log_output
		self.__IMAGE_EXTENSIONS = { '.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff', '.webp' }

		# PyQt5控件统一映射配置
		# 建立语义化的控件访问接口，便于后续修改和扩展
		self.__WIDGET_MAPPING = {
			# 数据源配置控件
			"data_folder_path": {
				"widget_id": "lineEdit_4",
				"widget_type": "QLineEdit",
				"description": "数据文件夹路径输入框 - 用于指定包含图像和对应JSON标注文件的文件夹路径",
				"function": "获取用户输入的数据源文件夹路径，系统将从该路径加载图像和标注数据进行预览"
			},

			# 预览配置控件
			"preview_count": {
				"widget_id": "lineEdit_58",
				"widget_type": "QLineEdit",
				"description": "预览数量输入框 - 用于指定要随机预览的图像数量",
				"function": "获取用户输入的预览数量，系统将随机选择指定数量的图像进行标注预览显示"
			}
		}

	def __get_widget_value(self, semantic_name: str) -> str:
		"""
		根据语义化名称获取控件的文本值。

		Args:
			semantic_name (str): 控件的语义化名称（在__WIDGET_MAPPING中定义）

		Returns:
			str: 控件的文本值，如果控件不存在则返回空字符串

		Usage Example:
			```python
			# 获取数据文件夹路径
			folder_path = self.__get_widget_value("data_folder_path")

			# 获取预览数量
			preview_count = self.__get_widget_value("preview_count")
			```
		"""
		try:
			if semantic_name not in self.__WIDGET_MAPPING:
				if self.__logger:
					self.__logger.warning(f"未找到语义化名称 '{semantic_name}' 对应的控件映射")
				return ""

			widget_config = self.__WIDGET_MAPPING[semantic_name]
			widget_id = widget_config["widget_id"]

			# 使用LineEditManager获取控件文本值
			text_value = self.__line_edit_manager.get_text(widget_id)
			return text_value if text_value is not None else ""

		except Exception as e:
			if self.__logger:
				self.__logger.error(f"获取控件 '{semantic_name}' 的值时发生错误: {e}")
			return ""

	def __set_widget_text(self, semantic_name: str, text: str) -> bool:
		"""
		根据语义化名称设置控件的文本值。

		Args:
			semantic_name (str): 控件的语义化名称（在__WIDGET_MAPPING中定义）
			text (str): 要设置的文本值

		Returns:
			bool: 设置成功返回True，失败返回False

		Usage Example:
			```python
			# 设置数据文件夹路径
			success = self.__set_widget_text("data_folder_path", "/path/to/data")

			# 设置预览数量
			success = self.__set_widget_text("preview_count", "10")
			```
		"""
		try:
			if semantic_name not in self.__WIDGET_MAPPING:
				if self.__logger:
					self.__logger.warning(f"未找到语义化名称 '{semantic_name}' 对应的控件映射")
				return False

			widget_config = self.__WIDGET_MAPPING[semantic_name]
			widget_id = widget_config["widget_id"]

			# 使用LineEditManager设置控件文本值
			self.__line_edit_manager.set_text(widget_id, text)
			return True

		except Exception as e:
			if self.__logger:
				self.__logger.error(f"设置控件 '{semantic_name}' 的值时发生错误: {e}")
			return False

	def __get_widget_info(self, semantic_name: str) -> Optional[dict]:
		"""
		获取控件的详细配置信息。

		Args:
			semantic_name (str): 控件的语义化名称

		Returns:
			Optional[dict]: 控件的配置信息字典，如果不存在则返回None

		Usage Example:
			```python
			# 获取控件配置信息
			info = self.__get_widget_info("data_folder_path")
			if info:
				print(f"控件ID: {info['widget_id']}")
				print(f"控件类型: {info['widget_type']}")
				print(f"功能描述: {info['description']}")
			```
		"""
		return self.__WIDGET_MAPPING.get(semantic_name)

	def __log( self, message: str, color: Optional[ str ] = None, level: str = "info" ):
		"""
		统一的日志记录方法，可同时向UI和控制台输出。

		Args:
			message (str): 日志消息。
			color (Optional[str]): 在UI上显示的文本颜色。
			level (str): 日志级别 ('info', 'warning', 'error', 'debug')。
		"""
		if self.__log_output and (color or level in [ "info", "warning", "error" ]):
			self.__log_output.append( message, color=color )

		if self.__logger:
			log_func = getattr( self.__logger, level.lower(), self.__logger.info )
			log_func( message )

	def __get_config( self ) -> Optional[ Tuple[ str, int ] ]:
		"""
		从UI输入框获取并验证配置（数据路径和预览数量）。

		Returns:
			Optional[Tuple[str, int]]: 如果配置有效，则返回一个包含数据文件夹路径和预览数量的元组。
								       如果无效，则返回 None。
		"""
		try:
			# 使用统一的控件映射接口获取配置值
			folder_path = self.__get_widget_value("data_folder_path")
			preview_count_str = self.__get_widget_value("preview_count")

			if not folder_path or not os.path.isdir( folder_path ):
				self.__log( f"错误：提供的文件夹路径 '{folder_path}' 无效或不存在。", color="red", level="error" )
				return None

			# 检查文件夹中是否存在图像文件
			image_files = [ f for f in os.listdir( folder_path ) if
			                any( f.lower().endswith( ext ) for ext in self.__IMAGE_EXTENSIONS ) ]
			if not image_files:
				self.__log( f"错误：在 '{folder_path}' 中未找到任何图像文件。", color="red", level="error" )
				return None

			# 检查是否至少有一个图像对应的JSON文件
			has_json = False
			for img_file in image_files:
				base_name = os.path.splitext( img_file )[ 0 ]
				json_file = os.path.join( folder_path, f"{base_name}.json" )
				if os.path.exists( json_file ):
					has_json = True
					break

			if not has_json:
				self.__log(
					f"错误：在 '{folder_path}' 中未找到任何与图像对应的JSON标注文件。", color="red", level="error"
					)
				return None

			if not preview_count_str or not preview_count_str.isdigit() or int( preview_count_str ) <= 0:
				self.__log( f"错误：预览数量必须是一个正整数，而不是 '{preview_count_str}'。", color="red", level="error" )
				return None

			preview_count = int( preview_count_str )
			return folder_path, preview_count

		except Exception as e:
			self.__log( f"获取配置时发生错误: {e}", color="red", level="error" )
			return None

	def __load_and_prepare_data( self, folder_path: str, count: int ) -> Optional[ List[ Any ] ]:
		"""
		从文件夹中加载图像和对应的JSON标注文件，随机打乱并提取指定数量的样本。

		Args:
			folder_path (str): 包含图像和JSON文件的文件夹路径。
			count (int): 需要提取的样本数量。

		Returns:
			Optional[List[Any]]: 一个包含随机样本的列表，如果失败则返回 None。
		"""
		try:
			# 获取所有图像文件
			image_files = [ f for f in os.listdir( folder_path ) if
			                any( f.lower().endswith( ext ) for ext in self.__IMAGE_EXTENSIONS ) ]

			if not image_files:
				self.__log( "错误：文件夹中没有图像文件。", color="red", level="error" )
				return None

			# 收集所有有效的图像-JSON对
			valid_data = [ ]

			for img_file in image_files:
				img_path = os.path.join( folder_path, img_file )
				base_name = os.path.splitext( img_file )[ 0 ]
				json_path = os.path.join( folder_path, f"{base_name}.json" )

				# 检查对应的JSON文件是否存在
				if not os.path.exists( json_path ):
					self.__log(
						f"警告：未找到图像 '{img_file}' 的JSON标注文件，已跳过。", color="yellow", level="warning"
						)
					continue

				try:
					# 读取JSON文件
					with open( json_path, 'r', encoding='utf-8' ) as f:
						json_data = json.load( f )

					# 验证JSON数据格式
					if isinstance( json_data, list ) and len( json_data ) == 3:
						# 使用实际图像路径，而不是JSON中可能存储的路径
						_, polygons_data, class_indices = json_data

						# 验证多边形和类别数据
						if (isinstance( polygons_data, list ) and isinstance( class_indices, list )
								and len( polygons_data ) == len( class_indices )):

							# 将嵌套的多边形点转换为扁平化列表
							polygons = [ ]
							for polygon in polygons_data:
								flat_points = [ ]
								for point in polygon:
									flat_points.extend( point )
								polygons.append( flat_points )

							# 只有当有有效标注时才添加数据
							if polygons and class_indices:
								valid_data.append( [ img_path, polygons, class_indices ] )
							else:
								self.__log(
									f"警告：JSON文件 '{base_name}.json' 中没有有效的多边形数据或类别索引。",
									color="yellow", level="warning"
									)
						else:
							self.__log(
								f"警告：JSON文件 '{base_name}.json' 的数据结构无效，多边形和类别数量不匹配。",
								color="yellow", level="warning"
								)
					else:
						self.__log(
							f"警告：JSON文件 '{base_name}.json' 的格式不符合预期，应为包含三个元素的数组。",
							color="yellow", level="warning"
							)

				except json.JSONDecodeError:
					self.__log( f"警告：JSON文件 '{base_name}.json' 格式无效，已跳过。", color="yellow", level="warning" )
					continue
				except Exception as e:
					self.__log( f"处理JSON文件 '{base_name}.json' 时出错: {e}", color="yellow", level="warning" )
					continue

			if not valid_data:
				self.__log( "错误：未能从任何JSON文件中提取有效的标注数据。", color="red", level="error" )
				return None

			# 随机打乱数据
			random.shuffle( valid_data )

			# 限制预览数量
			num_to_preview = min( count, len( valid_data ) )
			self.__log( f"成功加载并随机打乱数据。将预览 {num_to_preview} / {len( valid_data )} 张图像。", color="green" )

			return valid_data[ :num_to_preview ]

		except Exception as e:
			self.__log( f"加载和准备数据时发生错误: {e}", color="red", level="error" )
			return None

	def __display_images( self, data_to_preview: List[ Any ] ):
		"""
		使用matplotlib在弹窗中以网格形式显示带标注的图像。

		Args:
			data_to_preview (List[Any]): 要显示的标注数据列表。
		"""
		if not data_to_preview:
			self.__log( "没有可供预览的数据。", color="yellow", level="warning" )
			return

		num_images = len( data_to_preview )
		cols = 3  # 每行最多显示3张图
		rows = (num_images + cols - 1) // cols

		fig, axes = plt.subplots( rows, cols, figsize=(5 * cols, 5 * rows) )
		if num_images == 1:
			axes = np.array( [ axes ] )  # 保证axes总是一个可迭代的数组
		axes = axes.flatten()

		for i, item in enumerate( data_to_preview ):
			try:
				image_path, polygons, class_indices = item
				ax = axes[ i ]

				if not os.path.exists( image_path ):
					self.__log( f"警告: 图像文件不存在，已跳过: {image_path}", color="yellow", level="warning" )
					ax.text(
						0.5, 0.5, f"Image not found:\n{os.path.basename( image_path )}", ha='center', va='center',
						color='red'
					)
					ax.axis( 'off' )
					continue

				# 读取图像并转换为RGB
				image = cv2.imread( image_path )
				image = cv2.cvtColor( image, cv2.COLOR_BGR2RGB )  # type: ignore
				ax.imshow( image )

				# 绘制多边形和标签
				for polygon_coords, class_index in zip( polygons, class_indices ):
					# Matplotlib的Polygon需要一个 (N, 2) 的numpy数组
					poly_np = np.array( polygon_coords ).reshape( -1, 2 )

					# 生成随机颜色
					rand_color = np.random.rand( 3, )

					# 创建多边形补丁
					poly_patch = patches.Polygon(
						poly_np, closed=True, linewidth=2, edgecolor=rand_color, facecolor='none'
					)
					ax.add_patch( poly_patch )

					# 在第一个点附近添加类别索引文本
					ax.text(
						poly_np[ 0, 0 ],
						poly_np[ 0, 1 ] - 10,
						str( class_index ),
						color='white',
						fontsize=12,
						bbox=dict( facecolor=rand_color, alpha=0.7, edgecolor='none', boxstyle='round,pad=0.2' )
					)

				ax.axis( 'off' )
				ax.set_title( os.path.basename( image_path ), fontsize=10 )

			except Exception as e:
				self.__log( f"处理图像 {item[ 0 ] if item else 'N/A'} 时发生错误: {e}", color="red", level="error" )
				axes[ i ].text( 0.5, 0.5, "Error processing image", ha='center', va='center', color='red' )
				axes[ i ].axis( 'off' )

		# 隐藏任何未使用的子图
		for j in range( num_images, len( axes ) ):
			axes[ j ].axis( 'off' )

		plt.tight_layout()
		plt.show()

	def preview_annotations( self ):
		"""
		执行完整的数据预览流程。

		这是该类的主要公共接口。它会获取配置，加载数据，并最终弹窗显示带标注的图像。

		使用示例:
		```python
		# 假设 viewer 是 AnnotationViewer 的一个实例
		# viewer.preview_annotations()
		```
		"""
		self.__log( "开始执行标注预览流程...", color="blue" )
		try:
			# 1. 获取配置
			config = self.__get_config()
			if not config:
				return

			folder_path, preview_count = config

			# 2. 加载和准备数据
			data_to_preview = self.__load_and_prepare_data( folder_path, preview_count )
			if not data_to_preview:
				return

			# 3. 显示图像
			self.__display_images( data_to_preview )

			self.__log( "标注预览流程执行完毕。", color="green" )

		except Exception as e:
			import traceback
			self.__log( f"预览过程中发生未知错误: {e}", color="red", level="error" )
			self.__log( traceback.format_exc(), color="red", level="error" )


class DirectoryCleaner:
	"""
	智能目录清理功能类，实现基于UI控件状态的条件性images和labels文件夹清空操作。

	该类专门用于清理指定目录下的 images 和 labels 文件夹，具有以下特性：
	1. 条件性执行：仅当指定的复选框被选中时才执行清理操作
	2. 精确清理：只清理 images 和 labels 文件夹，不影响其他文件
	3. 路径验证：对目标目录路径进行完整的安全性验证
	4. 完整的错误处理：处理权限不足、路径不存在等各种异常情况
	5. 详细的日志记录：记录操作过程和结果，便于调试和监控
	6. UI集成：与现有的UI管理器无缝集成，提供实时反馈

	清理范围:
	- 仅清理指定目录下的 images 文件夹内容
	- 仅清理指定目录下的 labels 文件夹内容
	- 不会删除文件夹本身，只清空文件夹内容
	- 不会影响目录下的其他文件和文件夹

	使用示例:
	```python
	# 基本使用
	cleaner = DirectoryCleaner(
		checkbox_manager=self.__checkbox_manager,
		lineedit_manager=self.__lineedit_manager,
		log_output=self.__log_output,
		logger=self.__logger
	)

	# 执行条件性目录清理（清理 images 和 labels 文件夹）
	success = cleaner.clean_directory_conditionally()
	if success:
		print("images 和 labels 文件夹清理成功")
	else:
		print("清理失败或被跳过")
	```

	安全特性:
	- 路径安全验证：防止清理系统关键目录
	- 权限检查：确保有足够权限执行操作
	- 条件执行：只有明确选中时才执行，避免误操作
	- 精确清理：只影响指定的 images 和 labels 文件夹
	- 详细日志：记录所有操作步骤，便于审计
	"""

	def __init__(
		self,
		checkbox_manager: CheckBoxManager,
		lineedit_manager: LineEditManager,
		log_output: LogOutput,
		logger: Logger
	):
		"""
		初始化目录清理器。

		Args:
			checkbox_manager (CheckBoxManager): 用于获取rotate_6控件状态的管理器实例
			lineedit_manager (LineEditManager): 用于获取lineEdit_7控件值的管理器实例
			log_output (LogOutput): 用于输出操作日志到UI界面的实例
			logger (Logger): 用于记录详细日志的实例

		Raises:
			TypeError: 当传入的参数类型不正确时
			ValueError: 当传入的参数为None时

		使用示例:
		```python
		cleaner = DirectoryCleaner(
			checkbox_manager=self.__checkbox_manager,
			lineedit_manager=self.__lineedit_manager,
			log_output=self.__log_output,
			logger=self.__logger
		)
		```
		"""
		# 参数验证
		if not isinstance(checkbox_manager, CheckBoxManager):
			raise TypeError(f"checkbox_manager必须是CheckBoxManager实例，实际类型: {type(checkbox_manager)}")
		if not isinstance(lineedit_manager, LineEditManager):
			raise TypeError(f"lineedit_manager必须是LineEditManager实例，实际类型: {type(lineedit_manager)}")
		if not isinstance(log_output, LogOutput):
			raise TypeError(f"log_output必须是LogOutput实例，实际类型: {type(log_output)}")
		if not isinstance(logger, Logger):
			raise TypeError(f"logger必须是Logger实例，实际类型: {type(logger)}")

		if checkbox_manager is None:
			raise ValueError("checkbox_manager不能为None")
		if lineedit_manager is None:
			raise ValueError("lineedit_manager不能为None")
		if log_output is None:
			raise ValueError("log_output不能为None")
		if logger is None:
			raise ValueError("logger不能为None")

		# 存储管理器实例
		self.__checkbox_manager = checkbox_manager
		self.__lineedit_manager = lineedit_manager
		self.__log_output = log_output
		self.__logger = logger

		# 控件ID常量
		self.__CHECKBOX_ID = "rotate_6"  # 控制是否执行清理的复选框ID
		self.__LINEEDIT_ID = "lineEdit_7"  # 目标目录路径的输入框ID

		self.__logger.debug("DirectoryCleaner初始化完成")

	def clean_directory_conditionally(self) -> bool:
		"""
		执行条件性images和labels文件夹清理操作。

		该方法是类的核心功能，实现以下逻辑：
		1. 检查rotate_6控件的选中状态
		2. 如果未选中，记录日志并返回False
		3. 如果选中，获取lineEdit_7控件的目录路径
		4. 验证路径的有效性和安全性
		5. 清理目录下的 images 和 labels 文件夹
		6. 记录操作结果

		清理范围:
			- 只清理指定目录下的 images 文件夹内容
			- 只清理指定目录下的 labels 文件夹内容
			- 不删除文件夹本身，只清空内容
			- 不影响其他文件和文件夹

		Returns:
			bool: 操作成功返回True，失败或跳过返回False

		异常处理:
			- 捕获所有可能的异常并记录详细错误信息
			- 确保方法不会因异常而中断程序运行
			- 提供清晰的错误诊断信息

		使用示例:
		```python
		cleaner = DirectoryCleaner(...)

		# 执行条件性清理（清理 images 和 labels 文件夹）
		if cleaner.clean_directory_conditionally():
			print("images 和 labels 文件夹清理成功完成")
		else:
			print("清理被跳过或失败")
		```
		"""
		try:
			self.__logger.info("开始执行条件性目录清理检查")

			# 1. 检查复选框状态
			is_checked = self.__get_checkbox_state()
			if not is_checked:
				self.__log_skip_operation()
				return False

			# 2. 获取目录路径
			directory_path = self.__get_directory_path()
			if not directory_path:
				self.__log_empty_path_error()
				return False

			# 3. 验证路径
			if not self.__validate_directory_path(directory_path):
				return False

			# 4. 执行清理操作
			return self.__execute_directory_cleaning(directory_path)

		except Exception as e:
			error_msg = f"执行条件性目录清理时发生未预期的错误: {str(e)}"
			self.__log_output.append(error_msg, color="red")
			self.__logger.error(error_msg, exc_info=True)
			return False
	def __get_checkbox_state(self) -> bool:
		"""
		获取rotate_6控件的选中状态。

		Returns:
			bool: 控件选中返回True，未选中或获取失败返回False

		异常处理:
			- 处理控件不存在的情况
			- 处理获取状态失败的情况
		"""
		try:
			state = self.__checkbox_manager.get_checked_state_by_object_name(self.__CHECKBOX_ID)
			self.__logger.debug(f"获取复选框{self.__CHECKBOX_ID}状态: {state}")

			if state is None:
				self.__logger.warning(f"复选框{self.__CHECKBOX_ID}不存在或获取状态失败")
				return False

			return bool(state)

		except Exception as e:
			error_msg = f"获取复选框状态时出错: {str(e)}"
			self.__logger.error(error_msg)
			return False

	def __get_directory_path(self) -> str:
		"""
		获取lineEdit_7控件的目录路径文本。

		Returns:
			str: 目录路径字符串，获取失败返回空字符串

		异常处理:
			- 处理控件不存在的情况
			- 处理获取文本失败的情况
			- 自动去除路径前后的空白字符
		"""
		try:
			line_edit = self.__lineedit_manager.get_line_edit(self.__LINEEDIT_ID)
			if line_edit is None:
				self.__logger.error(f"LineEdit控件{self.__LINEEDIT_ID}不存在")
				return ""

			path = line_edit.text().strip()
			self.__logger.debug(f"获取目录路径: '{path}'")
			return path

		except Exception as e:
			error_msg = f"获取目录路径时出错: {str(e)}"
			self.__logger.error(error_msg)
			return ""

	def __validate_directory_path(self, directory_path: str) -> bool:
		"""
		验证目录路径的有效性和安全性。

		Args:
			directory_path (str): 要验证的目录路径

		Returns:
			bool: 路径有效且安全返回True，否则返回False

		验证项目:
			- 路径是否存在
			- 路径是否为目录
			- 路径是否可写
			- 路径安全性检查（避免清理系统关键目录）
		"""
		try:
			# 检查路径是否存在
			if not os.path.exists(directory_path):
				error_msg = f"目录路径不存在: {directory_path}"
				self.__log_output.append(error_msg, color="red")
				return False

			# 检查是否为目录
			if not os.path.isdir(directory_path):
				error_msg = f"指定路径不是目录: {directory_path}"
				self.__log_output.append(error_msg, color="red")
				return False

			# 检查是否有写权限
			if not os.access(directory_path, os.W_OK):
				error_msg = f"对目录没有写权限: {directory_path}"
				self.__log_output.append(error_msg, color="red")
				return False

			# 安全性检查：防止清理系统关键目录
			if self.__is_system_critical_directory(directory_path):
				error_msg = f"拒绝清理系统关键目录: {directory_path}"
				self.__log_output.append(error_msg, color="red")
				return False

			self.__logger.debug(f"目录路径验证通过: {directory_path}")
			return True

		except Exception as e:
			error_msg = f"验证目录路径时出错: {str(e)}"
			self.__log_output.append(error_msg, color="red")
			return False

	def __is_system_critical_directory(self, directory_path: str) -> bool:
		"""
		检查是否为系统关键目录。

		Args:
			directory_path (str): 要检查的目录路径

		Returns:
			bool: 是系统关键目录返回True，否则返回False

		检查的关键目录包括:
			- 系统根目录
			- Windows系统目录
			- 程序文件目录
			- 用户主目录的根级别
		"""
		try:
			# 规范化路径
			normalized_path = os.path.normpath(os.path.abspath(directory_path)).lower()

			# 定义系统关键目录列表
			critical_directories = [
				"c:\\",
				"c:\\windows",
				"c:\\windows\\system32",
				"c:\\program files",
				"c:\\program files (x86)",
				"c:\\users",
				"c:\\programdata",
				os.path.normpath(os.path.expanduser("~")).lower(),  # 用户主目录
			]

			# 检查是否匹配关键目录
			for critical_dir in critical_directories:
				if normalized_path == critical_dir or normalized_path.startswith(critical_dir + os.sep):
					self.__logger.warning(f"检测到系统关键目录: {directory_path}")
					return True

			return False

		except Exception as e:
			self.__logger.error(f"检查系统关键目录时出错: {str(e)}")
			# 出错时采用保守策略，认为是关键目录
			return True
	def __execute_directory_cleaning(self, directory_path: str) -> bool:
		"""
		执行实际的目录清理操作。

		清理指定目录下的 images 和 labels 文件夹，而不是清理整个目录。

		Args:
			directory_path (str): 包含 images 和 labels 文件夹的父目录路径

		Returns:
			bool: 清理成功返回True，失败返回False

		操作流程:
			1. 检查 images 和 labels 文件夹是否存在
			2. 分别清理这两个文件夹
			3. 记录清理结果
			4. 返回操作状态
		"""
		try:
			self.__logger.info(f"开始清理目录下的 images 和 labels 文件夹: {directory_path}")
			self.__log_output.append(f"正在清理 {directory_path} 下的 images 和 labels 文件夹", color="blue")

			# 定义要清理的子文件夹
			target_folders = ["images", "labels"]
			cleaned_folders = []
			skipped_folders = []

			for folder_name in target_folders:
				folder_path = os.path.join(directory_path, folder_name)

				if os.path.exists(folder_path) and os.path.isdir(folder_path):
					try:
						# 清理文件夹
						clean_directory(folder_path)
						cleaned_folders.append(folder_name)
						self.__logger.info(f"成功清理文件夹: {folder_path}")
					except Exception as e:
						error_msg = f"清理 {folder_name} 文件夹时出错: {str(e)}"
						self.__log_output.append(error_msg, color="red")
						self.__logger.error(error_msg)
						return False
				else:
					skipped_folders.append(folder_name)
					self.__logger.info(f"文件夹不存在，跳过: {folder_path}")

			# 生成结果报告
			if cleaned_folders:
				success_msg = f"成功清理文件夹: {', '.join(cleaned_folders)}"
				self.__log_output.append(success_msg, color="green")
				self.__logger.info(success_msg)

			if skipped_folders:
				skip_msg = f"跳过不存在的文件夹: {', '.join(skipped_folders)}"
				self.__log_output.append(skip_msg, color="blue")
				self.__logger.info(skip_msg)

			if not cleaned_folders and not skipped_folders:
				warning_msg = "未找到 images 或 labels 文件夹"
				self.__log_output.append(warning_msg, color="red")
				self.__logger.warning(warning_msg)
				return False

			return True

		except Exception as e:
			error_msg = f"清理目录时发生错误: {str(e)}"
			self.__log_output.append(error_msg, color="red")
			self.__logger.error(error_msg, exc_info=True)
			return False

	def __log_skip_operation(self):
		"""记录跳过操作的日志。"""
		skip_msg = "复选框未选中，跳过目录清理操作"
		self.__log_output.append(skip_msg, color="blue")
		self.__logger.info(skip_msg)

	def __log_empty_path_error(self):
		"""记录路径为空的错误日志。"""
		error_msg = "目录路径为空，无法执行清理操作"
		self.__log_output.append(error_msg, color="red")
		self.__logger.warning(error_msg)

	def get_configuration_status(self) -> dict:
		"""
		获取当前配置状态信息。

		Returns:
			dict: 包含配置状态的字典

		返回信息包括:
			- 复选框状态
			- 目录路径
			- 路径验证结果
			- 系统信息

		使用示例:
		```python
		cleaner = DirectoryCleaner(...)
		status = cleaner.get_configuration_status()
		print(f"复选框状态: {status['checkbox_checked']}")
		print(f"目录路径: {status['directory_path']}")
		print(f"路径有效: {status['path_valid']}")
		```
		"""
		try:
			# 获取复选框状态
			checkbox_checked = self.__get_checkbox_state()

			# 获取目录路径
			directory_path = self.__get_directory_path()

			# 验证路径（如果路径不为空）
			path_valid = False
			if directory_path:
				path_valid = self.__validate_directory_path(directory_path)

			# 构建状态字典
			status = {
				"checkbox_checked": checkbox_checked,
				"directory_path": directory_path,
				"path_valid": path_valid,
				"path_empty": not bool(directory_path),
				"ready_to_clean": checkbox_checked and path_valid,
				"checkbox_id": self.__CHECKBOX_ID,
				"lineedit_id": self.__LINEEDIT_ID,
				"timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
			}

			self.__logger.debug(f"配置状态: {status}")
			return status

		except Exception as e:
			error_msg = f"获取配置状态时出错: {str(e)}"
			self.__logger.error(error_msg)
			return {
				"error": error_msg,
				"timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
			}