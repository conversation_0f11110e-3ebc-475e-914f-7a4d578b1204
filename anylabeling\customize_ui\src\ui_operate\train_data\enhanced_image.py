from os import name
from typing import Optional, Any, List
import traceback
import itertools
import threading
from .helper import AnnotationCounter, load_yolo_format_data, CheckboxSelector
from global_tools.ui_tools import LineEditManager, LogOutput, QLabelManager, QProgressBarHelper, QPushButtonManager, CheckBoxManager
from global_tools.utils import Logger, Colors, clean_directory
from global_tools.utils import ManagedMultiProcess, SharedDataManager, ProcessEventManager
import global_tools.utils as global_tools_utils
import time


def get_annotation_count(
		line_edit_manager: LineEditManager,
		log_output: LogOutput,
		logger: Optional[ Logger ],
		label_manager: Optional[ QLabelManager ]
):
	"""
	统计标注对象总数量并更新UI。
	这是一个封装了 AnnotationCounter 逻辑的辅助函数。
	"""
	try:
		log_output.append( "开始统计标注对象总数量...", color="blue" )

		# 创建标注对象计数器实例
		counter = AnnotationCounter(
			line_edit_manager,
			log_output,
			logger,
			label_manager
		)

		# 执行统计
		total_count = counter.count_annotations()

		# 显示结果
		if total_count > 0:
			log_output.append( f"成功统计完成，共找到 {total_count} 个标注对象。", color="green" )
		else:
			log_output.append( "未找到有效的标注对象，请检查输入路径。", color="yellow" )

	except Exception as e:
		log_output.append( f"统计标注对象时发生错误: {str( e )}", color="red" )
		log_output.append( traceback.format_exc(), color="red" )


def start_augmentation(
		line_edit_manager: LineEditManager,
		label_manager: QLabelManager,
		exclusive_manager: Any,  # ExclusiveControlManager类型，避免循环导入
		log_output: LogOutput,
		logger: Optional[ Logger ]
) -> int:
	"""
	开始执行图像增强预计算。
	这是一个封装了图像增强预计算逻辑的辅助函数。

	Args:
					line_edit_manager (LineEditManager): 用于获取各种输入路径和值的LineEditManager实例。
					label_manager (QLabelManager): 用于更新UI标签的LabelManager实例。
					exclusive_manager: 用于确定用户选择的增强模式的管理器实例。
					log_output (LogOutput): 用于向UI输出操作信息的LogOutput实例。
					logger (Optional[Logger]): 用于记录详细日志的Logger实例。

	Returns:
					int: 计算出的最终增强图像数量。如果发生错误或无法计算，则返回0。
	"""
	try:
		from .helper import calculate_and_display_augmentation_count

		log_output.append( "开始执行图像增强预计算...", color="blue" )
		# 调用辅助函数来执行计算，但不更新UI
		count = calculate_and_display_augmentation_count(
			line_edit_manager=line_edit_manager,
			label_manager=label_manager,
			exclusive_manager=exclusive_manager,
			log_output=log_output,
			logger=logger,
			update_ui=False
		)

		if count > 0:
			log_output.append( f"预计算完成，计划生成 {count} 张增强图像。", color="green" )
			if logger:
				logger.info( f"计划生成 {count} 张增强图像。" )
		else:
			log_output.append( "预计算失败或数量为0，请检查配置。", color="yellow" )

		return count

	except Exception as e:
		import traceback
		log_output.append( f"执行图像增强时发生未知错误: {str( e )}", color="red" )
		log_output.append( traceback.format_exc(), color="red" )
		return 0


def process_target( shared_manager_proxy: SharedDataManager, task_item: Any, *args, **kwargs ):
	if not isinstance( task_item, list ):
		raise Exception( f"task_item 参数必须是 list" )
	
	try:
		from anylabeling.customize_ui.services.image_enhancement import AugmentationPipeline
		import uuid
		import os
		import cv2
		import time
		import json
		with shared_manager_proxy.get_lock():
			one_flag = shared_manager_proxy.get_value( key="one_flag", default=False )
			if not one_flag:
				shared_manager_proxy.add_value( key="one_flag", value=True )

		selected_checkboxes = kwargs.get( "selected_checkboxes", [ ] )
		save_dir_path = kwargs.get( "save_dir_path", "" )

		image_path = task_item[ 0 ]
		polygons_nested = task_item[ 1 ]
		labels_name = task_item[ 2 ]

		pipeline_default = AugmentationPipeline( priority_apis=selected_checkboxes )

		t_img, t_poly, t_labels = pipeline_default.apply(
			image_path=image_path,
			polygons_nested=polygons_nested,
			labels_name=labels_name
			# 注意：此处未传递 enabled_strategies，因此使用默认行为
		)
		file_name = str( uuid.uuid4() ).replace( "-", "" )
		image_path = os.sep.join( [ save_dir_path, file_name + ".jpg" ] )

		# 保存增强后的图像到指定路径
		cv2.imwrite( image_path, t_img )
		with open(os.path.join(save_dir_path, file_name + ".json"), "w", encoding="utf-8") as f:
			json.dump([image_path, t_poly, t_labels], f, ensure_ascii=False, indent=4)

		# with shared_manager_proxy.get_lock():
		# 	shared_manager_proxy.append_to_list( key="annotated_data", value=[ image_path, t_poly, t_labels ] )

		
	except Exception as e:
		import traceback
		traceback.print_exc()

	finally:
		with shared_manager_proxy.get_lock():
			progress_value = shared_manager_proxy.get_value( key="progress_value", default=0 )
			shared_manager_proxy.add_value( key="progress_value", value=progress_value + 1 )


class EnhancedImage:
	"""
	数据增强处理类，负责管理图像数据增强的完整流程。

	该类实现了统一的PyQt5控件访问机制，通过语义化的控件映射字典
	和统一的访问接口，提供了更好的代码可维护性和可读性。

	主要功能:
	- 多进程图像数据增强处理
	- 统一的UI控件访问管理
	- 进度监控和状态更新
	- 完整的错误处理机制

	控件映射说明:
	- augmentation_progress_bar: 数据增强进度条 (progressBar)
	- augmentation_start_button: 数据增强启动按钮 (pushButton_15)
	- progress_display_label: 进度显示标签 (label_15)
	- save_directory_path: 保存目录路径输入框 (lineEdit_4)
	- clear_save_directory: 清空保存目录复选框 (rotate_5)

	使用示例:
	```python
	# 创建EnhancedImage实例
	enhanced_image = EnhancedImage(
		line_edit_manager=line_edit_manager,
		label_manager=label_manager,
		exclusive_manager=exclusive_manager,
		log_output=log_output,
		logger=logger,
		checkbox_manager=checkbox_manager,
		push_button_manager=push_button_manager,
		progress_bar_helper=progress_bar_helper,
		ui_form=ui_form
	)

	# 启动数据增强
	success = enhanced_image.start_data_augmentation()
	```
	"""

	def __init__(
			self,
			line_edit_manager: LineEditManager,
			label_manager: QLabelManager,
			exclusive_manager: Any,  # ExclusiveControlManager类型，避免循环导入
			log_output: LogOutput,
			logger: Optional[Logger],
			checkbox_manager: CheckBoxManager,
			push_button_manager: QPushButtonManager,
			progress_bar_helper: QProgressBarHelper,
			ui_form: Any  # Ui_Form类型，避免循环导入
	):
		"""
		初始化EnhancedImage实例，包含数据增强所需的所有参数。

		Args:
			line_edit_manager: 用于获取UI输入的LineEditManager实例
			label_manager: 用于更新UI标签的QLabelManager实例
			exclusive_manager: 互斥控制管理器实例
			log_output: 用于输出日志的LogOutput实例
			logger: 用于记录日志的Logger实例，可选
			checkbox_manager: 复选框管理器实例
			push_button_manager: 按钮管理器实例
			progress_bar_helper: 进度条辅助器实例
			ui_form: UI表单对象
		"""
		# 保存传入的参数
		self.__line_edit_manager = line_edit_manager
		self.__exclusive_manager = exclusive_manager
		self.__logger = logger
		self.__checkbox_manager = checkbox_manager
		self.__ui_form = ui_form

		# 保存UI相关的参数
		self.__log_output = log_output
		self.__progress_bar_helper = progress_bar_helper
		self.__label_manager = label_manager
		self.__push_button_manager = push_button_manager

		# 初始化为空，将在start_data_augmentation中设置
		self.__all_image_paths = []
		self.__count = 0
		self.__selected_checkboxes = []
		self.__save_dir_path = ""
		self.__augmented_count = 0

		# 控件映射字典：语义化名称到实际控件ID的映射
		self.__WIDGET_MAPPING = {
			# 进度条相关控件
			"augmentation_progress_bar": "progressBar",

			# 按钮相关控件
			"augmentation_start_button": "pushButton_15",

			# 标签相关控件
			"progress_display_label": "label_15",

			# 输入框相关控件
			"save_directory_path": "lineEdit_4",

			# 复选框相关控件
			"clear_save_directory": "rotate_5"
		}

	def __get_widget_value(self, widget_name: str) -> Any:
		"""
		统一的控件值获取接口。

		该方法根据控件名称的语义化特征自动判断控件类型，并调用相应的管理器获取控件值。
		判断逻辑采用优先级策略，确保更具体的控件类型优先判断，避免误判。

		Args:
			widget_name: 语义化的控件名称（来自__WIDGET_MAPPING）

		Returns:
			Any: 控件的值，类型取决于控件类型
			- CheckBox控件返回bool值（True/False）
			- LineEdit控件返回str值（文本内容）

		Raises:
			KeyError: 当widget_name不存在于映射字典中时
			ValueError: 当控件类型无法识别时

		控件类型判断规则:
		1. CheckBox控件：以"clear_"开头或以"_checkbox"结尾
		   - 例如："clear_save_directory", "auto_save_checkbox"
		2. LineEdit控件：以"_path"结尾或包含"directory"关键字（但不是CheckBox）
		   - 例如："save_directory_path", "output_directory"

		使用示例:
		```python
		# 获取保存目录路径（LineEdit控件）
		save_path = self.__get_widget_value("save_directory_path")  # 返回str

		# 获取复选框状态（CheckBox控件）
		clear_dir = self.__get_widget_value("clear_save_directory")  # 返回bool
		```
		"""
		if widget_name not in self.__WIDGET_MAPPING:
			raise KeyError(f"控件名称 '{widget_name}' 不存在于映射字典中")

		widget_id = self.__WIDGET_MAPPING[widget_name]

		# 根据控件类型选择合适的管理器
		# 注意：判断顺序很重要，优先判断更具体的控件类型，避免误判

		# 1. 优先判断CheckBox控件（避免"clear_save_directory"被误判为LineEdit）
		if widget_name.startswith("clear_") or widget_name.endswith("_checkbox"):
			# CheckBox控件：以"clear_"开头或以"_checkbox"结尾的控件
			return self.__checkbox_manager.get_checked_state_by_object_name(widget_id)

		# 2. 判断LineEdit控件（在排除CheckBox后再判断包含"directory"的控件）
		elif widget_name.endswith("_path") or "directory" in widget_name:
			# LineEdit控件：以"_path"结尾或包含"directory"关键字的控件（但不是CheckBox）
			return self.__line_edit_manager.get_text(name=widget_id)

		else:
			raise ValueError(f"未知的控件类型，无法获取值: {widget_name}")

	def __set_widget_text(self, widget_name: str, text: str) -> None:
		"""
		统一的控件文本设置接口。

		Args:
			widget_name: 语义化的控件名称（来自__WIDGET_MAPPING）
			text: 要设置的文本内容

		Raises:
			KeyError: 当widget_name不存在于映射字典中时

		使用示例:
		```python
		# 设置进度显示标签文本
		self.__set_widget_text("progress_display_label", "10/100")
		```
		"""
		if widget_name not in self.__WIDGET_MAPPING:
			raise KeyError(f"控件名称 '{widget_name}' 不存在于映射字典中")

		widget_id = self.__WIDGET_MAPPING[widget_name]

		# 根据控件类型选择合适的管理器
		if widget_name.endswith("_label") or "display" in widget_name:
			# Label控件
			self.__label_manager.set_text(label_name=widget_id, text=text)
		else:
			raise ValueError(f"控件类型不支持文本设置: {widget_name}")

	def __set_widget_enabled(self, widget_name: str, enabled: bool) -> None:
		"""
		统一的控件启用/禁用接口。

		Args:
			widget_name: 语义化的控件名称（来自__WIDGET_MAPPING）
			enabled: 是否启用控件

		Raises:
			KeyError: 当widget_name不存在于映射字典中时

		使用示例:
		```python
		# 禁用数据增强启动按钮
		self.__set_widget_enabled("augmentation_start_button", False)
		```
		"""
		if widget_name not in self.__WIDGET_MAPPING:
			raise KeyError(f"控件名称 '{widget_name}' 不存在于映射字典中")

		widget_id = self.__WIDGET_MAPPING[widget_name]

		# 根据控件类型选择合适的管理器
		if widget_name.endswith("_button") or "button" in widget_name:
			# Button控件
			self.__push_button_manager.set_enabled(button_name=widget_id, enabled=enabled)
		else:
			raise ValueError(f"控件类型不支持启用/禁用设置: {widget_name}")

	def __reset_progress_bar(self, widget_name: str, animate: bool = False) -> None:
		"""
		重置进度条的统一接口。

		Args:
			widget_name: 语义化的控件名称（来自__WIDGET_MAPPING）
			animate: 是否使用动画效果

		Raises:
			KeyError: 当widget_name不存在于映射字典中时

		使用示例:
		```python
		# 重置数据增强进度条
		self.__reset_progress_bar("augmentation_progress_bar")
		```
		"""
		if widget_name not in self.__WIDGET_MAPPING:
			raise KeyError(f"控件名称 '{widget_name}' 不存在于映射字典中")

		widget_id = self.__WIDGET_MAPPING[widget_name]

		if widget_name.endswith("_progress_bar") or "progress" in widget_name:
			if self.__progress_bar_helper is not None:
				self.__progress_bar_helper.reset(name=widget_id, animate=animate)
			else:
				if self.__logger:
					self.__logger.warning(f"进度条辅助器未初始化，无法重置进度条: {widget_name}")
		else:
			raise ValueError(f"控件类型不是进度条: {widget_name}")

	def __set_progress_bar_range(self, widget_name: str, minimum: int, maximum: int) -> None:
		"""
		设置进度条范围的统一接口。

		Args:
			widget_name: 语义化的控件名称（来自__WIDGET_MAPPING）
			minimum: 最小值
			maximum: 最大值

		Raises:
			KeyError: 当widget_name不存在于映射字典中时

		使用示例:
		```python
		# 设置数据增强进度条范围
		self.__set_progress_bar_range("augmentation_progress_bar", 0, 100)
		```
		"""
		if widget_name not in self.__WIDGET_MAPPING:
			raise KeyError(f"控件名称 '{widget_name}' 不存在于映射字典中")

		widget_id = self.__WIDGET_MAPPING[widget_name]

		if widget_name.endswith("_progress_bar") or "progress" in widget_name:
			if self.__progress_bar_helper is not None:
				self.__progress_bar_helper.set_range(name=widget_id, minimum=minimum, maximum=maximum) # type: ignore
			else:
				if self.__logger:
					self.__logger.warning(f"进度条辅助器未初始化，无法设置进度条范围: {widget_name}")
		else:
			raise ValueError(f"控件类型不是进度条: {widget_name}")

	def __set_progress_bar_value(self, widget_name: str, value: int) -> None:
		"""
		设置进度条值的统一接口。

		Args:
			widget_name: 语义化的控件名称（来自__WIDGET_MAPPING）
			value: 进度值

		Raises:
			KeyError: 当widget_name不存在于映射字典中时

		使用示例:
		```python
		# 设置数据增强进度条值
		self.__set_progress_bar_value("augmentation_progress_bar", 50)
		```
		"""
		if widget_name not in self.__WIDGET_MAPPING:
			raise KeyError(f"控件名称 '{widget_name}' 不存在于映射字典中")

		widget_id = self.__WIDGET_MAPPING[widget_name]

		if widget_name.endswith("_progress_bar") or "progress" in widget_name:
			if self.__progress_bar_helper is not None:
				self.__progress_bar_helper.set_value(name=widget_id, value=value) # type: ignore
			else:
				if self.__logger:
					self.__logger.warning(f"进度条辅助器未初始化，无法设置进度条值: {widget_name}")
		else:
			raise ValueError(f"控件类型不是进度条: {widget_name}")

	def __call__( self ) -> Any:
		self.__reset_progress_bar("augmentation_progress_bar", animate=False)
		self.__set_progress_bar_range("augmentation_progress_bar", minimum=0, maximum=self.__count)
		self.__set_widget_enabled("augmentation_start_button", enabled=False)

		self.__managed_multi_process = ManagedMultiProcess(
			callback_func=process_target,
			input_data=self.__all_image_paths,
			num_processes=4,

			# ---------------------
			selected_checkboxes=self.__selected_checkboxes,
			save_dir_path=self.__save_dir_path
		)
		self.__managed_multi_process.watch_shared_data(
			keys=[ "progress_value", "one_flag" ], callback=self.on_progress_value_change
		)
		# 所有子进程执行完成且数据更新队列为空后触发
		self.__managed_multi_process.listen_event(
			event_key=ProcessEventManager.PROCESS_COMPLETED_WITH_DATA,
			callback=self.PROCESS_COMPLETED_WITH_DATA_CALLBACK
		)

		# 调用stop_all后，所有子进程正常执行完成且数据更新队列为空时触发
		self.__managed_multi_process.listen_event( 
			event_key=ProcessEventManager.PROCESS_STOPPED_WITH_DATA,
			callback=self.PROCESS_STOPPED_CALLBACK
		)
		self.__managed_multi_process.create_event(key="event_stop")

		self.__managed_multi_process.run()

		
		
		
	def on_progress_value_change(
			self, key, old_value, new_value, lock, shared_data_manager: SharedDataManager, *args, **kwargs
	):
		pass
		if key == "one_flag" and new_value == True:
			self.__log_output.append( "多进程已启动......", color=Colors.ORANGE )

		if key == "progress_value":
			self.__set_progress_bar_value("augmentation_progress_bar", value=new_value)
			self.__set_widget_text("progress_display_label", text=f"{new_value}/{self.__count}")

	def save_annotated_data( self ):
		
		annotated_data = self.__managed_multi_process.get_shared_list( key="annotated_data" )
		import json
		import os
		
		if annotated_data:
			try:
				# 创建保存路径
				json_file_path = os.path.join(self.__save_dir_path, "annotated_data.json") # type: ignore
				
				# 将数据保存为JSON文件
				with open(json_file_path, 'w', encoding='utf-8') as f:
					json.dump(annotated_data, f, ensure_ascii=False, indent=4)
				
				self.__log_output.append(f"标注数据已保存至: {json_file_path}", color=Colors.FOREST_GREEN)
			except Exception as e:
				self.__log_output.append(f"保存标注数据失败: {str(e)}", color=Colors.RED)
				traceback.print_exc()
		else:
			self.__log_output.append("没有标注数据可保存", color=Colors.ORANGE)
		
	
	def stop_all( self ):
		
		self.__managed_multi_process.set_event( key="event_stop" )
		
		# # 定义回调函数
		def stop_callback( mp_instance: ManagedMultiProcess, *args, **kwargs ):
			if mp_instance.wait_event( key="event_stop" ):
				return True
		
		self.__managed_multi_process.stop_all(callback=stop_callback, block=False)
		

	def PROCESS_COMPLETED_WITH_DATA_CALLBACK( self, *args, **kwargs ):
		# 所有子进程执行完成且数据更新队列为空后触发
		self.__log_output.append( "所有子进程执行完成，处理增强图像完成", color=Colors.FOREST_GREEN )
		self.__managed_multi_process.check_and_release_lock(force=True)
		self.__set_widget_enabled("augmentation_start_button", enabled=True)
		# self.save_annotated_data()

	def PROCESS_STOPPED_CALLBACK( self, *args, **kwargs ):
		# 调用stop_all后，所有子进程正常执行完成且数据更新队列为空时触发
		self.__log_output.append( "所有子进程已停止，不再继续图像增强", color=Colors.DARK_ORANGE )
		self.__managed_multi_process.check_and_release_lock(force=True)
		self.__set_widget_enabled("augmentation_start_button", enabled=True)
		# self.save_annotated_data()

	def start_data_augmentation(self) -> bool:
		"""
		开始数据增强处理的完整流程（实例方法）。

		这个方法包含了原来在train_data.py中__start_augmentation_button_function方法的全部业务逻辑，
		包括数据加载、预计算、多进程启动等完整流程。所有参数都在实例化时传递。

		Returns:
			bool: 启动是否成功

		使用示例:
		```python
		# 在train_data.py中调用
		enhanced_image_instance = EnhancedImage(
			line_edit_manager=self.__line_edit_manager,
			label_manager=self.__label_manager,
			exclusive_manager=self.__exclusive_manager,
			log_output=self.__log_output,
			logger=self.__logger,
			checkbox_manager=self.__checkbox_manager,
			push_button_manager=self.__push_button_manager,
			progress_bar_helper=self.__progress_bar_helper,
			ui_form=self.__ui_form
		)
		success = enhanced_image_instance.start_data_augmentation()
		```
		"""
		try:
			# 1. 获取数据集中所有图像的绝对路径列表
			all_image_paths = load_yolo_format_data(
				line_edit_manager=self.__line_edit_manager,
				logger=self.__logger
			)

			if not all_image_paths:
				self.__log_output.append("错误：未能找到任何图像文件，无法开始增强任务。", color="red")
				return False

			# 2. 开始增强流程的预计算
			count = start_augmentation(
				line_edit_manager=self.__line_edit_manager,
				label_manager=self.__label_manager,
				exclusive_manager=self.__exclusive_manager,
				log_output=self.__log_output,
				logger=self.__logger
			)

			# 如果需要，将图像列表循环复制到与计数相等的长度
			if count > 0 and all_image_paths:
				all_image_paths = list(itertools.islice(itertools.cycle(all_image_paths), count))

			# 3. 如果计划数量大于0，则可以继续执行实际的多进程增强
			if count > 0:
				self.__log_output.append("启动多进程......", color="blue")
				if self.__logger:
					self.__logger.info("启动多进程......")

				# 获取保存路径
				save_dir_path = self.__get_widget_value("save_directory_path")

				# 清空保存路径
				if self.__get_widget_value("clear_save_directory"):
					self.__log_output.append(f"清空保存路径 {save_dir_path} 中的内容......", color="red")
					if self.__logger:
						self.__logger.info(f"清空保存路径 {save_dir_path} 中的内容......")
					clean_directory(save_dir_path)
					self.__log_output.append(
						f"清空保存路径 {save_dir_path} 中的内容完成",
						color=global_tools_utils.Colors.GREEN
					)
					if self.__logger:
						self.__logger.info(f"清空保存路径 {save_dir_path} 中的内容完成")

				# 获取选中的复选框
				checkboxes = CheckboxSelector()
				selected_checkboxes = checkboxes.get_checked_checkboxes(layout_object=self.__ui_form.verticalLayout_2)

				# 重新初始化当前实例的属性
				self.__all_image_paths = all_image_paths
				self.__count = len(all_image_paths)
				self.__selected_checkboxes = selected_checkboxes
				self.__save_dir_path = save_dir_path

				# 保存计划增强数量（用于train_data.py中的属性更新）
				self.__augmented_count = count

				# 启动增强处理
				self()

				return True
			else:
				self.__log_output.append("计划增强数量为0，无法启动增强任务。", color="yellow")
				return False

		except Exception as e:
			error_msg = f"启动数据增强时发生错误: {str(e)}"
			self.__log_output.append(error_msg, color="red")
			self.__log_output.append(traceback.format_exc(), color="red")
			if self.__logger:
				self.__logger.error(error_msg)
				self.__logger.error(traceback.format_exc())
			return False


