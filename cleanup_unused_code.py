#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理CoordinateSimilarityProcessor中未使用的代码
"""

import re

def cleanup_unused_code():
    """清理未使用的代码"""
    file_path = "anylabeling/customize_ui/src/ui_operate/save_data/coordinate_similarity_processor.py"
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 需要删除的类列表
    classes_to_remove = [
        'MorphologySimilarityCalculator',
        'AdaptiveFusionModule', 
        'DecisionEngine'
    ]
    
    # 删除每个类
    for class_name in classes_to_remove:
        # 找到类的开始位置
        class_pattern = rf'(\t*)class {class_name}:.*?(?=\n\t*class|\n\t*def|\Z)'
        content = re.sub(class_pattern, '', content, flags=re.DOTALL)
    
    # 清理多余的空行
    content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 未使用的代码清理完成！")

if __name__ == "__main__":
    cleanup_unused_code()
