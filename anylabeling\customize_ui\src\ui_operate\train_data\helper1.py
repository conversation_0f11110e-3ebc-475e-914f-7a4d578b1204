import json
import os
import shutil
import traceback
from pathlib import Path
from typing import Dict, List, Tuple, Any, TYPE_CHECKING

from global_tools.postgre_sql import PostgreSQLClient
from global_tools.ui_tools import LineEditManager, CheckBoxManager, LogOutput
from global_tools.utils import Colors, Logger

# 类型注解导入
if TYPE_CHECKING:
	from global_tools.ui_tools import QLabelManager


class TrainingDataExporter:
	"""
    训练数据导出管理类，实现从数据库获取标注数据并按照YOLO格式导出到指定目录结构的功能

    该类负责：
    1. 从UI控件动态获取导出配置参数（包括数据库配置）
    2. 根据复选框状态查询数据库中的标注数据
    3. 按照YOLO标准目录结构导出图像和标注文件
    4. 提供详细的进度跟踪和错误处理

    重构后的新特性：
    - 支持动态数据库配置：从UI控件获取数据库名称、表名称、数据类型过滤条件
    - 增强的错误处理：包括数据库连接失败、表不存在、数据类型不匹配等情况
    - 配置验证机制：验证数据库配置的有效性和数据源的存在性
    - 灵活的参数传递：支持通过参数或UI控件获取配置
    - 进度显示功能：集成QLabelManager实现简洁的总进度显示和动画效果

    属性:
        __line_edit_manager: 用于操作QLineEdit控件，获取路径和数据库配置
        __checkbox_manager: 用于操作QCheckBox控件，获取数据类型选择
        __log_output: 用于在UI界面输出日志
        __logger: 用于在控制台输出日志
        __label_manager: 用于进度显示的QLabelManager实例（可选）
        __db_client: PostgreSQL数据库客户端
        __control_config: 控件配置映射字典，定义UI控件与配置参数的对应关系
        __database_config: 数据库配置缓存，存储从UI控件获取的配置参数
        __progress_widget_config: 进度显示控件配置映射
        __export_stats: 导出统计信息

    控件配置映射：
        - lineEdit_174: 目标数据库名称
        - lineEdit_175: 目标表名称
        - lineEdit_176: 数据类型过滤条件
        - rotate_34: 分割数据选择控件（segmentation_data）
        - rotate_35: OBB数据选择控件（obb_data）
        - rotate_152: 预训练数据类型选择控件（新增）
        - rotate_153: 训练数据类型选择控件（新增）
        - label_297: 进度显示标签控件（新增）

    使用示例:
        # 创建实例
        exporter = TrainingDataExporter(
            line_edit_manager,
            checkbox_manager,
            log_output,
            logger
        )

        # 使用动态配置执行导出
        success = exporter.export_training_data()

        # 使用指定配置执行导出
        config = {
            "database_name": "my_database",
            "table_name": "my_table",
            "data_type": "my_type"
        }
        success = exporter.export_training_data(config)

        # 检查结果
        if success:
            print("导出成功完成")
        else:
            print("导出过程中出现错误")
    """

	def __init__(
			self,
			line_edit_manager: LineEditManager,
			checkbox_manager: CheckBoxManager,
			log_output: LogOutput,
			logger: Logger,
		label_manager: 'QLabelManager' = None
	):
		"""
        初始化训练数据导出管理器

        参数:
            line_edit_manager: 操作QLineEdit的管理器实例，用于获取目标路径和数据库配置
            checkbox_manager: 操作QCheckBox的管理器实例，用于获取数据类型选择
            log_output: UI界面日志输出工具，用于显示关键进度信息
            logger: 控制台日志输出工具，用于详细调试信息
            label_manager: QLabelManager实例，用于显示进度信息（可选）

        使用示例:
            # 基本使用（向后兼容）
            exporter = TrainingDataExporter(
                line_edit_manager=my_line_edit_manager,
                checkbox_manager=my_checkbox_manager,
                log_output=my_log_output,
                logger=my_logger
            )

            # 带进度显示功能（简洁模式）
            exporter = TrainingDataExporter(
                line_edit_manager=my_line_edit_manager,
                checkbox_manager=my_checkbox_manager,
                log_output=my_log_output,
                logger=my_logger,
                label_manager=my_label_manager
            )
        """
		self.__line_edit_manager = line_edit_manager
		self.__checkbox_manager = checkbox_manager
		self.__log_output = log_output
		self.__logger = logger
		self.__label_manager = label_manager
		self.__db_client = None  # 数据库连接

		# 控件配置映射字典 - 用于从UI控件动态获取数据库配置参数
		self.__control_config = {
			'database_name': 'lineEdit_174',  # 目标数据库名称控件
			'table_name':    'lineEdit_175',  # 目标表名称控件
			'data_type':     'lineEdit_176'  # 数据类型过滤条件控件
		}

		# 数据库配置缓存 - 存储从UI控件获取的配置参数
		self.__database_config = {
			'database_name': '',  # 数据库名称
			'table_name':    '',  # 表名称
			'data_type':     ''  # 数据类型过滤条件
		}

		# 进度显示控件配置映射
		self.__progress_widget_config = {
			'progress_label': 'label_297'  # 进度显示标签控件
		}

		# 导出统计信息
		self.__export_stats = {
			"total_records":    0,  # 总记录数
			"processed_images": 0,  # 已处理图像数
			"processed_labels": 0,  # 已处理标注数
			"train_count":      0,  # 训练数据计数
			"val_count":        0,  # 验证数据计数
			"success_count":    0,  # 成功计数
			"error_count":      0,  # 错误计数
			"errors":           [ ]  # 错误详情列表
		}

		# 初始化进度显示功能
		self.__initialize_progress_display()

		self.__logger.info( "TrainingDataExporter 初始化完成" )
		self.__logger.debug( f"控件配置映射: {self.__control_config}" )

	def __initialize_progress_display(self) -> None:
		"""
        初始化进度显示功能

        建立进度显示控件的配置化管理机制，确保代码的可维护性和扩展性

        异常处理:
            - 捕获并记录所有初始化过程中的异常
            - 确保即使进度显示初始化失败，也不影响主要功能
        """
		try:
			if self.__label_manager is None:
				self.__logger.debug("未提供QLabelManager实例，跳过进度显示初始化")
				return

			# 验证进度显示控件配置
			if 'progress_label' not in self.__progress_widget_config:
				self.__logger.error("进度显示控件配置缺失，无法初始化进度显示功能")
				return

			# 验证进度显示控件是否存在
			progress_label_name = self.__progress_widget_config['progress_label']
			if not progress_label_name:
				self.__logger.error("进度显示控件名称为空，无法初始化进度显示功能")
				return

			if not self.__label_manager.has_label(progress_label_name):
				self.__logger.warning(f"进度显示控件 '{progress_label_name}' 不存在，进度显示功能将不可用")
				return

			# 初始化进度显示
			self.__set_total_progress("初始化...")
			self.__logger.debug(f"进度显示功能初始化完成，使用控件: {progress_label_name}")

		except Exception as e:
			self.__logger.error(f"初始化进度显示功能时出错: {str(e)}")
			import traceback
			self.__logger.debug(f"初始化进度显示功能异常详情: {traceback.format_exc()}")

	def __set_total_progress(self, status: str = "处理中...") -> None:
		"""
        设置总进度显示

        参数:
            status: 状态描述

        异常处理:
            - 控件验证：确保进度显示控件存在且可用
            - 操作异常：捕获并记录所有更新过程中的异常
        """
		try:
			if self.__label_manager is None:
				return

			# 验证进度显示控件配置
			if 'progress_label' not in self.__progress_widget_config:
				return

			progress_label_name = self.__progress_widget_config['progress_label']
			if not progress_label_name or not self.__label_manager.has_label(progress_label_name):
				return

			# 格式化进度信息 - 只显示状态
			progress_text = f"当前进度：{status.strip() if status else '处理中...'}"

			# 更新进度显示
			if self.__label_manager.set_text(progress_label_name, progress_text):
				# 应用淡入淡出动画效果
				self.__label_manager.start_fade_animation(progress_label_name, duration_ms=100)
				self.__logger.debug(f"进度显示已设置: {progress_text}")
			else:
				self.__logger.warning(f"设置进度显示失败，控件可能不存在: {progress_label_name}")

		except Exception as e:
			self.__logger.error(f"设置进度显示时出错: {str(e)}")
			import traceback
			self.__logger.debug(f"设置进度显示异常详情: {traceback.format_exc()}")

	def __set_completion_status(self, status: str = "完成") -> None:
		"""
        设置完成状态显示

        参数:
            status: 完成状态描述

        异常处理:
            - 控件验证：确保进度显示控件存在且可用
            - 操作异常：捕获并记录所有更新过程中的异常
        """
		try:
			if self.__label_manager is None:
				return

			# 验证进度显示控件配置
			if 'progress_label' not in self.__progress_widget_config:
				return

			progress_label_name = self.__progress_widget_config['progress_label']
			if not progress_label_name or not self.__label_manager.has_label(progress_label_name):
				return

			# 格式化完成状态信息
			progress_text = f"当前进度：{status.strip() if status else '完成'}"

			# 更新进度显示
			if self.__label_manager.set_text(progress_label_name, progress_text):
				# 应用淡入淡出动画效果
				self.__label_manager.start_fade_animation(progress_label_name, duration_ms=100)
				self.__logger.debug(f"完成状态已设置: {progress_text}")
			else:
				self.__logger.warning(f"设置完成状态失败，控件可能不存在: {progress_label_name}")

		except Exception as e:
			self.__logger.error(f"设置完成状态时出错: {str(e)}")
			import traceback
			self.__logger.debug(f"设置完成状态异常详情: {traceback.format_exc()}")

	def __clear_progress_display(self) -> None:
		"""
        清空进度显示

        异常处理:
            - 控件验证：确保进度显示控件存在且可用
            - 操作异常：捕获并记录所有清空过程中的异常
        """
		try:
			if self.__label_manager is None:
				return

			# 验证进度显示控件配置
			if 'progress_label' not in self.__progress_widget_config:
				return

			progress_label_name = self.__progress_widget_config['progress_label']
			if not progress_label_name:
				return

			if self.__label_manager.has_label(progress_label_name):
				if self.__label_manager.clear_text(progress_label_name):
					self.__logger.debug("进度显示已清空")
				else:
					self.__logger.warning(f"清空进度显示失败，控件可能不存在: {progress_label_name}")
			else:
				self.__logger.debug(f"进度显示控件不存在，跳过清空操作: {progress_label_name}")

		except Exception as e:
			self.__logger.error(f"清空进度显示时出错: {str(e)}")
			import traceback
			self.__logger.debug(f"清空进度显示异常详情: {traceback.format_exc()}")

	def __get_database_config( self ) -> Dict[ str, str ]:
		"""
        从UI控件获取数据库配置参数

        返回:
            包含数据库配置的字典，包含以下键：
            - database_name: 目标数据库名称
            - table_name: 目标表名称
            - data_type: 数据类型过滤条件

        异常:
            ValueError: 当配置参数为空或无效时
            RuntimeError: 当UI控件访问失败时

        使用示例:
            config = self.__get_database_config()
            print(f"数据库名称: {config['database_name']}")
            print(f"表名称: {config['table_name']}")
            print(f"数据类型: {config['data_type']}")
        """
		try:
			self.__logger.debug( "开始获取数据库配置参数" )

			# 获取数据库名称
			database_name = ""
			try:
				database_name_control = self.__control_config[ "database_name" ]
				database_name = self.__line_edit_manager.get_text( database_name_control )
				if not database_name or not database_name.strip():
					raise ValueError( "数据库名称不能为空" )
				database_name = database_name.strip()
				self.__logger.debug( f"获取到数据库名称: {database_name}" )
			except Exception as e:
				error_msg = f"获取数据库名称失败: {str( e )}"
				self.__logger.error( error_msg )
				self.__log_output.append( error_msg, Colors.ERROR )
				raise ValueError( error_msg )

			# 获取表名称
			table_name = ""
			try:
				table_name_control = self.__control_config[ "table_name" ]
				table_name = self.__line_edit_manager.get_text( table_name_control )
				if not table_name or not table_name.strip():
					raise ValueError( "表名称不能为空" )
				table_name = table_name.strip()
				self.__logger.debug( f"获取到表名称: {table_name}" )
			except Exception as e:
				error_msg = f"获取表名称失败: {str( e )}"
				self.__logger.error( error_msg )
				self.__log_output.append( error_msg, Colors.ERROR )
				raise ValueError( error_msg )

			# 获取数据类型过滤条件
			data_type = ""
			try:
				data_type_control = self.__control_config[ "data_type" ]
				data_type = self.__line_edit_manager.get_text( data_type_control )
				if not data_type or not data_type.strip():
					raise ValueError( "数据类型过滤条件不能为空" )
				data_type = data_type.strip()
				self.__logger.debug( f"获取到数据类型: {data_type}" )
			except Exception as e:
				error_msg = f"获取数据类型过滤条件失败: {str( e )}"
				self.__logger.error( error_msg )
				self.__log_output.append( error_msg, Colors.ERROR )
				raise ValueError( error_msg )

			# 验证配置参数的有效性
			config = {
				"database_name": database_name,
				"table_name":    table_name,
				"data_type":     data_type
			}

			if not self.__validate_database_config( config ):
				error_msg = "数据库配置验证失败"
				self.__logger.error( error_msg )
				self.__log_output.append( error_msg, Colors.ERROR )
				raise ValueError( error_msg )

			# 缓存配置参数
			self.__database_config = config.copy()

			self.__logger.info( f"数据库配置获取成功: {config}" )
			self.__log_output.append(
				f"数据库配置 - 数据库: {database_name}, 表: {table_name}, 数据类型: {data_type}",
				Colors.INFO
			)

			return config

		except Exception as e:
			error_msg = f"获取数据库配置时发生未预期错误: {str( e )}"
			self.__logger.error( error_msg )
			self.__logger.error( traceback.format_exc() )
			self.__log_output.append( error_msg, Colors.ERROR )
			raise RuntimeError( error_msg )

	def __validate_database_config( self, config: Dict[ str, str ] ) -> bool:
		"""
        验证数据库配置的有效性

        参数:
            config: 数据库配置字典

        返回:
            配置有效返回True，否则返回False

        使用示例:
            config = {"database_name": "test_db", "table_name": "test_table", "data_type": "test_type"}
            is_valid = self.__validate_database_config(config)
        """
		try:
			database_name = config.get( "database_name", "" )
			table_name = config.get( "table_name", "" )
			data_type = config.get( "data_type", "" )

			# 验证数据库名称格式（PostgreSQL标识符规范）
			if not database_name.replace( '_', '' ).replace( '-', '' ).isalnum():
				self.__logger.error( f"数据库名称格式无效: {database_name}" )
				return False

			# 验证表名称格式（SQL标识符规范）
			if not table_name.replace( '_', '' ).isalnum():
				self.__logger.error( f"表名称格式无效: {table_name}" )
				return False

			# 验证数据类型不为空
			if len( data_type ) == 0:
				self.__logger.error( "数据类型过滤条件不能为空" )
				return False

			self.__logger.debug( "数据库配置验证通过" )
			return True

		except Exception as e:
			self.__logger.error( f"数据库配置验证时出错: {str( e )}" )
			return False

	def __get_ui_control_values( self ) -> Dict[ str, Any ]:
		"""
        从UI控件获取导出配置参数

        返回:
            包含导出配置的字典，包含以下键：
            - target_path: 目标导出路径
            - include_segmentation: 是否包含分割数据（rotate_34控件状态）
            - include_obb: 是否包含OBB数据（rotate_35控件状态）
            - include_pretrain: 是否包含预训练数据（rotate_152控件状态）
            - include_train: 是否包含训练数据（rotate_153控件状态）

        异常:
            ValueError: 当目标路径为空或无效时
            RuntimeError: 当UI控件访问失败时

        控件互斥规则:
            - rotate_34和rotate_35互斥（只能选择一种标注数据类型）
            - rotate_152和rotate_153只能有一种状态（预训练或训练）

        使用示例:
            config = self.__get_ui_control_values()
            print(f"导出路径: {config['target_path']}")
            print(f"包含分割数据: {config['include_segmentation']}")
            print(f"包含OBB数据: {config['include_obb']}")
            print(f"包含预训练数据: {config['include_pretrain']}")
            print(f"包含训练数据: {config['include_train']}")
        """
		try:
			self.__logger.debug( "开始获取UI控件数据" )

			# 获取目标路径
			target_path = ""
			try:
				target_path = self.__line_edit_manager.get_text( "lineEdit_7" )
				if not target_path or not target_path.strip():
					raise ValueError( "目标路径不能为空" )
				target_path = target_path.strip()
				self.__logger.debug( f"获取到目标路径: {target_path}" )
			except Exception as e:
				error_msg = f"获取目标路径失败: {str( e )}"
				self.__logger.error( error_msg )
				self.__log_output.append( error_msg, Colors.ERROR )
				raise ValueError( error_msg )

			# 获取分割数据选择状态 (rotate_34)
			include_segmentation = False
			try:
				checkbox = self.__checkbox_manager.get_checkbox_by_object_name( "rotate_34" )
				if checkbox is None:
					self.__logger.warning( "无法找到分割数据复选框(rotate_34)，默认为False" )
					include_segmentation = False
				else:
					include_segmentation = checkbox.isChecked()
				self.__logger.debug( f"分割数据选择状态: {include_segmentation}" )
			except Exception as e:
				error_msg = f"获取分割数据选择状态失败: {str( e )}"
				self.__logger.warning( error_msg )
				include_segmentation = False

			# 获取OBB数据选择状态 (rotate_35)
			include_obb = False
			try:
				checkbox = self.__checkbox_manager.get_checkbox_by_object_name( "rotate_35" )
				if checkbox is None:
					self.__logger.warning( "无法找到OBB数据复选框(rotate_35)，默认为False" )
					include_obb = False
				else:
					include_obb = checkbox.isChecked()
				self.__logger.debug( f"OBB数据选择状态: {include_obb}" )
			except Exception as e:
				error_msg = f"获取OBB数据选择状态失败: {str( e )}"
				self.__logger.warning( error_msg )
				include_obb = False

			# 获取预训练数据类型选择状态 (rotate_152)
			include_pretrain = False
			try:
				checkbox = self.__checkbox_manager.get_checkbox_by_object_name( "rotate_152" )
				if checkbox is None:
					self.__logger.warning( "无法找到预训练数据复选框(rotate_152)，默认为False" )
					include_pretrain = False
				else:
					include_pretrain = checkbox.isChecked()
				self.__logger.debug( f"预训练数据选择状态: {include_pretrain}" )
			except Exception as e:
				error_msg = f"获取预训练数据选择状态失败: {str( e )}"
				self.__logger.warning( error_msg )
				include_pretrain = False

			# 获取训练数据类型选择状态 (rotate_153)
			include_train = False
			try:
				checkbox = self.__checkbox_manager.get_checkbox_by_object_name( "rotate_153" )
				if checkbox is None:
					self.__logger.warning( "无法找到训练数据复选框(rotate_153)，默认为False" )
					include_train = False
				else:
					include_train = checkbox.isChecked()
				self.__logger.debug( f"训练数据选择状态: {include_train}" )
			except Exception as e:
				error_msg = f"获取训练数据选择状态失败: {str( e )}"
				self.__logger.warning( error_msg )
				include_train = False

			# 验证训练数据类型选择（rotate_152和rotate_153只能有一种状态）
			if not include_pretrain and not include_train:
				error_msg = "必须选择一种训练数据类型（预训练数据或训练数据）"
				self.__logger.error( error_msg )
				self.__log_output.append( error_msg, Colors.ERROR )
				raise ValueError( error_msg )

			# 验证只能选择一种标注数据类型
			if include_segmentation and include_obb:
				error_msg = "不能同时选择分割数据和OBB数据，只能选择其中一种"
				self.__logger.error( error_msg )
				self.__log_output.append( error_msg, Colors.ERROR )
				raise ValueError( error_msg )

			if not include_segmentation and not include_obb:
				error_msg = "必须选择一种标注数据类型（分割数据或OBB数据）"
				self.__logger.error( error_msg )
				self.__log_output.append( error_msg, Colors.ERROR )
				raise ValueError( error_msg )

			# 验证目标路径的有效性
			try:
				target_path_obj = Path( target_path )
				if target_path_obj.exists() and not target_path_obj.is_dir():
					error_msg = f"目标路径不是有效的目录: {target_path}"
					self.__logger.error( error_msg )
					self.__log_output.append( error_msg, Colors.ERROR )
					raise ValueError( error_msg )
			except Exception as e:
				error_msg = f"目标路径验证失败: {str( e )}"
				self.__logger.error( error_msg )
				self.__log_output.append( error_msg, Colors.ERROR )
				raise ValueError( error_msg )

			config = {
				"target_path":          target_path,
				"include_segmentation": include_segmentation,
				"include_obb":          include_obb,
				"include_pretrain":     include_pretrain,
				"include_train":        include_train
			}

			self.__logger.info( f"UI控件数据获取成功: {config}" )
			self.__log_output.append(
				f"导出配置 - 路径: {target_path}, 分割数据: {include_segmentation}, OBB数据: {include_obb}, 预训练: {include_pretrain}, 训练: {include_train}",
				Colors.INFO
			)

			return config

		except Exception as e:
			error_msg = f"获取UI控件数据时发生未预期错误: {str( e )}"
			self.__logger.error( error_msg )
			self.__logger.error( traceback.format_exc() )
			self.__log_output.append( error_msg, Colors.ERROR )
			raise RuntimeError( error_msg )

	def __connect_database( self, db_config: Dict[ str, str ] = None ) -> bool:
		"""
        连接PostgreSQL数据库

        参数:
            db_config: 数据库配置字典，包含database_name等信息。
                      如果为None，则从UI控件动态获取配置

        返回:
            连接成功返回True，否则返回False

        使用示例:
            # 使用动态配置连接
            success = self.__connect_database()

            # 使用指定配置连接
            config = {"database_name": "my_database", "table_name": "my_table", "data_type": "my_type"}
            success = self.__connect_database(config)
        """
		try:
			# 如果没有提供配置，则从UI控件获取
			if db_config is None:
				db_config = self.__get_database_config()

			db_name = db_config.get( "database_name", "" )
			if not db_name:
				error_msg = "数据库名称不能为空"
				self.__logger.error( error_msg )
				self.__log_output.append( error_msg, Colors.ERROR )
				return False

			self.__logger.debug( f"尝试连接数据库: {db_name}" )

			# 创建PostgreSQL客户端连接
			self.__db_client = PostgreSQLClient(
				host="localhost",
				port=5432,
				database=db_name,
				user="postgres",
				password="123456",
				min_connections=1,
				max_connections=5
			)

			# 验证表是否存在
			table_name = db_config.get( "table_name", "" )
			if table_name and not self.__check_table_exists( table_name ):
				error_msg = f"表 '{table_name}' 在数据库 '{db_name}' 中不存在"
				self.__logger.error( error_msg )
				self.__log_output.append( error_msg, Colors.ERROR )
				return False

			self.__log_output.append( f"成功连接到数据库: {db_name}", Colors.SUCCESS )
			self.__logger.info( f"成功连接到数据库: {db_name}" )

			if table_name:
				self.__logger.info( f"已验证表存在: {table_name}" )

			return True

		except Exception as e:
			error_msg = f"数据库连接失败: {str( e )}"
			self.__log_output.append( error_msg, Colors.ERROR )
			self.__logger.error( error_msg )
			self.__logger.error( traceback.format_exc() )
			return False

	def __check_table_exists( self, table_name: str ) -> bool:
		"""
        检查指定表是否存在于当前数据库中

        参数:
            table_name: 要检查的表名

        返回:
            表存在返回True，否则返回False

        使用示例:
            exists = self.__check_table_exists("detection_results")
            if exists:
                print("表存在")
        """
		try:
			if not self.__db_client:
				self.__logger.error( "数据库客户端未初始化" )
				return False

			# 查询表是否存在
			check_sql = """
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_name = %s
                );
            """

			result = self.__db_client.execute_query( check_sql, (table_name,), fetch=True )

			if result and len( result ) > 0:
				exists = result[ 0 ][ 0 ]  # 获取EXISTS查询的结果
				self.__logger.debug( f"表 '{table_name}' 存在性检查结果: {exists}" )
				return exists
			else:
				self.__logger.warning( f"无法检查表 '{table_name}' 的存在性" )
				return False

		except Exception as e:
			self.__logger.error( f"检查表存在性时出错: {str( e )}" )
			self.__logger.error( traceback.format_exc() )
			return False

	def __validate_data_source_exists( self, data_type: str, table_name: str ) -> bool:
		"""
        验证指定的数据类型在表中是否存在记录

        参数:
            data_type: 数据类型过滤条件
            table_name: 表名称

        返回:
            存在记录返回True，否则返回False

        使用示例:
            exists = self.__validate_data_source_exists("亚基矿", "detection_results")
        """
		try:
			if not self.__db_client:
				self.__logger.error( "数据库客户端未初始化" )
				return False

			# 查询指定数据类型是否存在记录
			check_sql = f"""
                SELECT EXISTS (
                    SELECT 1 FROM {table_name}
                    WHERE data_source = %s
                    LIMIT 1
                );
            """

			result = self.__db_client.execute_query( check_sql, (data_type,), fetch=True )

			if result and len( result ) > 0:
				exists = result[ 0 ][ 0 ]
				self.__logger.debug( f"数据类型 '{data_type}' 在表 '{table_name}' 中的存在性: {exists}" )
				return exists
			else:
				self.__logger.warning( f"无法检查数据类型 '{data_type}' 的存在性" )
				return False

		except Exception as e:
			self.__logger.error( f"验证数据源存在性时出错: {str( e )}" )
			self.__logger.error( traceback.format_exc() )
			return False

	def __handle_database_error( self, error: Exception, operation: str ) -> str:
		"""
        统一处理数据库相关错误，提供友好的错误信息

        参数:
            error: 异常对象
            operation: 操作描述

        返回:
            格式化的错误信息

        使用示例:
            try:
                # 数据库操作
                pass
            except Exception as e:
                error_msg = self.__handle_database_error(e, "连接数据库")
        """
		error_str = str( error ).lower()

		if "connection" in error_str or "connect" in error_str:
			return f"{operation}失败: 无法连接到数据库，请检查数据库服务是否运行"
		elif "authentication" in error_str or "password" in error_str:
			return f"{operation}失败: 数据库认证失败，请检查用户名和密码"
		elif "database" in error_str and "does not exist" in error_str:
			return f"{operation}失败: 指定的数据库不存在，请检查数据库名称"
		elif "table" in error_str and "does not exist" in error_str:
			return f"{operation}失败: 指定的表不存在，请检查表名称"
		elif "permission" in error_str or "access" in error_str:
			return f"{operation}失败: 权限不足，请检查数据库访问权限"
		elif "timeout" in error_str:
			return f"{operation}失败: 操作超时，请检查网络连接或数据库负载"
		else:
			return f"{operation}失败: {str( error )}"

	def __validate_table_name_safety( self, table_name: str ) -> bool:
		"""
        验证表名的安全性，防止SQL注入攻击

        参数:
            table_name: 要验证的表名

        返回:
            表名安全返回True，否则返回False

        使用示例:
            is_safe = self.__validate_table_name_safety("detection_results")
        """
		try:
			# 检查表名是否只包含字母、数字和下划线
			import re
			if not re.match( r'^[a-zA-Z_][a-zA-Z0-9_]*$', table_name ):
				self.__logger.error( f"表名包含不安全字符: {table_name}" )
				return False

			# 检查表名长度
			if len( table_name ) > 63:  # PostgreSQL标识符最大长度
				self.__logger.error( f"表名过长: {table_name}" )
				return False

			# 检查是否为SQL关键字
			sql_keywords = {
				'select', 'insert', 'update', 'delete', 'drop', 'create',
				'alter', 'table', 'database', 'index', 'view', 'trigger'
			}
			if table_name.lower() in sql_keywords:
				self.__logger.error( f"表名不能是SQL关键字: {table_name}" )
				return False

			return True

		except Exception as e:
			self.__logger.error( f"验证表名安全性时出错: {str( e )}" )
			return False

	def __get_table_record_count( self, table_name: str ) -> int:
		"""
        获取指定表的总记录数

        参数:
            table_name: 表名

        返回:
            记录数，出错时返回-1

        使用示例:
            count = self.__get_table_record_count("detection_results")
            print(f"表中有 {count} 条记录")
        """
		try:
			if not self.__db_client:
				self.__logger.error( "数据库客户端未初始化" )
				return -1

			# 使用安全的SQL构建方式（表名已通过安全验证）
			count_sql = "SELECT COUNT(*) FROM " + table_name
			result = self.__db_client.execute_query( count_sql, fetch=True )

			if result and len( result ) > 0:
				count = result[ 0 ][ 0 ]
				self.__logger.debug( f"表 '{table_name}' 总记录数: {count}" )
				return count
			else:
				self.__logger.warning( f"无法获取表 '{table_name}' 的记录数" )
				return -1

		except Exception as e:
			self.__logger.error( f"获取表记录数时出错: {str( e )}" )
			self.__logger.error( traceback.format_exc() )
			return -1

	def __get_data_source_count( self, table_name: str, data_source: str ) -> int:
		"""
        获取指定数据源的记录数

        参数:
            table_name: 表名
            data_source: 数据源名称

        返回:
            记录数，出错时返回-1

        使用示例:
            count = self.__get_data_source_count("detection_results", "亚基矿")
            print(f"数据源有 {count} 条记录")
        """
		try:
			if not self.__db_client:
				self.__logger.error( "数据库客户端未初始化" )
				return -1

			# 使用安全的SQL构建方式（表名已通过安全验证）
			count_sql = "SELECT COUNT(*) FROM " + table_name + " WHERE data_source = %s"
			result = self.__db_client.execute_query( count_sql, (data_source,), fetch=True )

			if result and len( result ) > 0:
				count = result[ 0 ][ 0 ]
				self.__logger.debug( f"数据源 '{data_source}' 在表 '{table_name}' 中的记录数: {count}" )
				return count
			else:
				self.__logger.warning( f"无法获取数据源 '{data_source}' 的记录数" )
				return -1

		except Exception as e:
			self.__logger.error( f"获取数据源记录数时出错: {str( e )}" )
			self.__logger.error( traceback.format_exc() )
			return -1

	def __build_fetch_condition(
			self, include_segmentation: bool, include_obb: bool,
			include_pretrain: bool = False, include_train: bool = False,
			data_type: str = None
	) -> str:
		"""
        构建fetch_data兼容的查询条件字符串

        参数:
            include_segmentation: 是否包含分割数据
            include_obb: 是否包含OBB数据
            include_pretrain: 是否包含预训练数据（新增）
            include_train: 是否包含训练数据（新增）
            data_type: 数据源过滤条件

        返回:
            fetch_data兼容的条件字符串

        查询逻辑:
            (数据类型条件) AND (训练类型条件) [AND (数据源条件)]

        使用示例:
            # 预训练分割数据
            condition = self.__build_fetch_condition(True, False, True, False, "亚基矿")
            # 返回: "(segmentation_data is not null and jsonb_typeof(segmentation_data) != 'null') and (training_type ->> 'pretrain_train' is not null or training_type ->> 'pretrain_val' is not null) and data_source = '亚基矿'"

            # 训练OBB数据
            condition = self.__build_fetch_condition(False, True, False, True)
            # 返回: "(obb_data is not null and jsonb_typeof(obb_data) != 'null') and (training_type ->> 'train' is not null or training_type ->> 'val' is not null)"
        """
		try:
			condition_parts = [ ]

			# 构建数据类型条件（分割数据或OBB数据）
			data_type_condition = None
			if include_segmentation:
				# 构建分割数据查询条件：检查JSONB字段是否包含有效数据
				data_type_condition = "segmentation_data is not null and jsonb_typeof(segmentation_data) != 'null'"
				self.__logger.debug( "添加分割数据查询条件" )
			elif include_obb:
				# 构建OBB数据查询条件：检查JSONB字段是否包含有效数据
				data_type_condition = "obb_data is not null and jsonb_typeof(obb_data) != 'null'"
				self.__logger.debug( "添加OBB数据查询条件" )
			else:
				error_msg = "必须选择一种标注数据类型"
				self.__logger.error( error_msg )
				raise ValueError( error_msg )

			# 构建训练类型条件（预训练数据或训练数据）
			training_type_condition = None
			if include_pretrain:
				# 构建预训练数据查询条件：检查training_type字段中的pretrain_train或pretrain_val
				training_type_condition = "training_type ->> 'pretrain_train' is not null or training_type ->> 'pretrain_val' is not null"
				self.__logger.debug( "添加预训练数据查询条件" )
			elif include_train:
				# 构建训练数据查询条件：检查training_type字段中的train或val
				training_type_condition = "training_type ->> 'train' is not null or training_type ->> 'val' is not null"
				self.__logger.debug( "添加训练数据查询条件" )
			else:
				error_msg = "必须选择一种训练数据类型"
				self.__logger.error( error_msg )
				raise ValueError( error_msg )

			# 组合数据类型条件和训练类型条件
			if data_type_condition:
				condition_parts.append( f"({data_type_condition})" )
			if training_type_condition:
				condition_parts.append( f"({training_type_condition})" )

			# 添加数据源过滤条件（如果指定）
			if data_type and data_type.strip():
				# 使用参数化方式避免SQL注入，fetch_data会自动处理
				condition_parts.append( f"data_source = '{data_type.strip()}'" )
				self.__logger.debug( f"添加数据源过滤条件: {data_type.strip()}" )

			final_condition = " and ".join( condition_parts )
			self.__logger.debug( f"构建的查询条件: {final_condition}" )

			return final_condition

		except Exception as e:
			error_msg = f"构建查询条件时发生错误: {str( e )}"
			self.__logger.error( error_msg )
			raise ValueError( error_msg )

	def __query_annotation_data(
			self, include_segmentation: bool, include_obb: bool,
			include_pretrain: bool = False, include_train: bool = False,
			db_config: Dict[ str, str ] = None
	) -> List[ Dict[ str, Any ] ]:
		"""
        根据标注数据类型和训练数据类型选择查询数据库中的标注数据

        重构说明：
            此方法已重构为使用PostgreSQLClient.fetch_data API替代直接SQL查询，
            提供更好的安全性、错误处理和性能优化。
            新增支持预训练数据和训练数据的区分查询功能。

        参数:
            include_segmentation: 是否包含分割数据（segmentation_data）
            include_obb: 是否包含OBB数据（obb_data）
            include_pretrain: 是否包含预训练数据（新增）
            include_train: 是否包含训练数据（新增）
            db_config: 数据库配置字典，包含table_name和data_type等信息。
                      如果为None，则使用缓存的配置

        返回:
            标注数据记录列表，每个记录包含图像路径、标注坐标、类别信息等
            记录格式: {
                "detection_id": int,
                "image_path": str,
                "image_width": int,
                "image_height": int,
                "obb_data": dict,
                "segmentation_data": dict,
                "training_type": str,
                "created_at": datetime
            }

        异常:
            ValueError: 当查询参数无效时（如表名不安全、数据类型选择错误等）
            RuntimeError: 当数据库查询失败时（如连接问题、查询执行失败等）

        技术实现:
            - 使用fetch_data API进行安全的参数化查询
            - 支持复杂的JSONB字段条件（jsonb_typeof等）
            - 支持训练类型条件（pretrain_train、pretrain_val、train、val）
            - 自动处理JSON字段反序列化
            - 内置连接池管理和性能优化

        查询逻辑:
            (数据类型条件) AND (训练类型条件) [AND (数据源条件)]

        使用示例:
            # 预训练分割数据
            records = self.__query_annotation_data(
                include_segmentation=True,
                include_obb=False,
                include_pretrain=True,
                include_train=False
            )

            # 训练OBB数据
            records = self.__query_annotation_data(
                include_segmentation=False,
                include_obb=True,
                include_pretrain=False,
                include_train=True
            )

            print(f"查询到 {len(records)} 条记录")

            # 访问记录数据
            for record in records:
                print(f"图像路径: {record['image_path']}")
                print(f"训练类型: {record['training_type']}")
        """
		try:
			if not self.__db_client:
				error_msg = "数据库未连接，无法执行查询"
				self.__logger.error( error_msg )
				self.__log_output.append( error_msg, Colors.ERROR )
				raise RuntimeError( error_msg )

			# 获取数据库配置
			if db_config is None:
				db_config = self.__database_config
				if not db_config or not db_config.get( "table_name" ):
					# 如果缓存为空，重新获取配置
					db_config = self.__get_database_config()

			table_name = db_config.get( "table_name", "" )
			data_type = db_config.get( "data_type", "" )

			if not table_name:
				error_msg = "表名称不能为空"
				self.__logger.error( error_msg )
				self.__log_output.append( error_msg, Colors.ERROR )
				raise ValueError( error_msg )

			# 验证表名安全性（防止SQL注入）
			if not self.__validate_table_name_safety( table_name ):
				error_msg = f"表名包含不安全字符: {table_name}"
				self.__logger.error( error_msg )
				self.__log_output.append( error_msg, Colors.ERROR )
				raise ValueError( error_msg )

			# 验证表是否存在
			if not self.__check_table_exists( table_name ):
				error_msg = f"表 '{table_name}' 不存在"
				self.__logger.error( error_msg )
				self.__log_output.append( error_msg, Colors.ERROR )
				raise ValueError( error_msg )

			# 首先检查表中是否有数据
			total_count = self.__get_table_record_count( table_name )
			self.__logger.info( f"表 '{table_name}' 总记录数: {total_count}" )

			if total_count == 0:
				self.__logger.warning( f"表 '{table_name}' 中没有任何数据" )
				self.__log_output.append( f"表 '{table_name}' 中没有数据", Colors.WARNING )
				return [ ]

			# 构建查询描述
			query_desc_parts = []

			# 数据类型描述
			if include_segmentation:
				query_desc_parts.append("分割数据")
			elif include_obb:
				query_desc_parts.append("OBB数据")
			else:
				error_msg = "必须选择一种标注数据类型"
				self.__logger.error( error_msg )
				raise ValueError( error_msg )

			# 训练类型描述
			if include_pretrain:
				query_desc_parts.append("预训练")
			elif include_train:
				query_desc_parts.append("训练")
			else:
				error_msg = "必须选择一种训练数据类型"
				self.__logger.error( error_msg )
				raise ValueError( error_msg )

			query_desc = " + ".join( query_desc_parts )

			# 添加数据源描述（如果指定）
			if data_type and data_type.strip():
				query_desc += f"（数据源: {data_type}）"

				# 检查指定数据源是否存在
				data_source_count = self.__get_data_source_count( table_name, data_type.strip() )
				self.__logger.info( f"数据源 '{data_type}' 的记录数: {data_source_count}" )

				if data_source_count == 0:
					self.__logger.warning( f"数据源 '{data_type}' 在表中没有记录" )
					self.__log_output.append( f"数据源 '{data_type}' 没有数据", Colors.WARNING )
					return [ ]

			# 构建fetch_data兼容的查询条件
			condition_str = self.__build_fetch_condition( include_segmentation, include_obb, include_pretrain, include_train, data_type )

			# 定义需要查询的列
			columns = [
				"detection_id", "image_path", "image_width", "image_height",
				"obb_data", "segmentation_data", "training_type", "created_at"
			]

			self.__logger.info( f"执行查询 - 类型: {query_desc}" )
			self.__logger.info( f"表名: {table_name}" )
			self.__logger.debug( f"查询条件: {condition_str}" )
			self.__logger.debug( f"查询列: {columns}" )

			# 使用fetch_data API执行查询
			result = self.__db_client.fetch_data(
				table_name=table_name,
				condition_str=condition_str,
				columns=columns,
				order_by="detection_id ASC"
			)

			# 处理fetch_data返回结果
			if result is None:
				self.__logger.warning( "fetch_data返回None，可能是数据库连接问题" )
				self.__log_output.append( "查询返回None，请检查数据库连接", Colors.WARNING )
				return [ ]

			# 检查fetch_data执行是否成功
			if not result.get( 'success', False ):  # type: ignore
				error_msg = f"查询失败: {result.get( 'error', '未知错误' )}"  # type: ignore
				self.__logger.error( error_msg )
				self.__log_output.append( error_msg, Colors.ERROR )
				raise RuntimeError( error_msg )

			# 获取查询结果数据
			records = result.get( 'data', [ ] )  # type: ignore
			if not records:
				self.__logger.warning( "查询返回空列表，没有匹配的数据" )
				self.__log_output.append( f"没有找到匹配的{query_desc}记录", Colors.WARNING )
				return [ ]

			# fetch_data已经返回字典格式数据，无需手动转换
			# 但需要验证数据完整性
			validated_records = [ ]
			for i, record in enumerate( records ):
				try:
					# 验证必要字段是否存在
					required_fields = [
						"detection_id", "image_path", "image_width", "image_height",
						"obb_data", "segmentation_data", "training_type", "created_at"
					]

					for field in required_fields:
						if field not in record:
							self.__logger.warning( f"记录 {i + 1} 缺少字段: {field}" )
							break
					else:
						# 所有字段都存在，添加到验证列表
						validated_records.append( record )

				except Exception as e:
					self.__logger.error( f"验证第{i + 1}行数据时出错: {str( e )}" )
					continue

			records = validated_records

			self.__export_stats[ "total_records" ] = len( records )

			self.__logger.info( f"查询完成 - {query_desc}: {len( records )} 条记录" )
			self.__log_output.append(
				f"从表 '{table_name}' 查询到 {len( records )} 条{query_desc}记录", Colors.SUCCESS
			)

			return records

		except ValueError as e:
			# 处理参数验证错误（如表名无效、数据类型选择错误等）
			error_msg = f"查询参数错误: {str( e )}"
			self.__logger.error( error_msg )
			self.__log_output.append( error_msg, Colors.ERROR )
			self.__export_stats[ "error_count" ] += 1
			self.__export_stats[ "errors" ].append( error_msg )
			raise ValueError( error_msg )
		except RuntimeError as e:
			# 处理运行时错误（如数据库连接问题、查询失败等）
			error_msg = f"查询执行错误: {str( e )}"
			self.__logger.error( error_msg )
			self.__logger.error( traceback.format_exc() )
			self.__log_output.append( error_msg, Colors.ERROR )
			self.__export_stats[ "error_count" ] += 1
			self.__export_stats[ "errors" ].append( error_msg )
			raise RuntimeError( error_msg )
		except Exception as e:
			# 处理其他未预期的错误
			error_msg = f"查询标注数据时发生未预期错误: {str( e )}"
			self.__logger.error( error_msg )
			self.__logger.error( traceback.format_exc() )
			self.__log_output.append( error_msg, Colors.ERROR )
			self.__export_stats[ "error_count" ] += 1
			self.__export_stats[ "errors" ].append( error_msg )
			raise RuntimeError( error_msg )

	def __create_directory_structure( self, target_path: str ) -> Dict[ str, str ]:
		"""
        按照YOLO标准创建目录结构

        参数:
            target_path: 目标根目录路径

        返回:
            包含各个目录路径的字典，键为目录类型，值为完整路径

        异常:
            OSError: 当目录创建失败时
            PermissionError: 当没有创建权限时

        使用示例:
            dirs = self.__create_directory_structure("/path/to/export")
            print(f"训练图像目录: {dirs['images_train']}")
            print(f"验证标注目录: {dirs['labels_val']}")
        """
		try:
			self.__logger.debug( f"开始创建YOLO目录结构: {target_path}" )

			# 定义目录结构
			directories = {
				"root":         target_path,
				"images":       os.path.join( target_path, "images" ),
				"labels":       os.path.join( target_path, "labels" ),
				"images_train": os.path.join( target_path, "images", "train" ),
				"images_val":   os.path.join( target_path, "images", "val" ),
				"labels_train": os.path.join( target_path, "labels", "train" ),
				"labels_val":   os.path.join( target_path, "labels", "val" )
			}

			# 创建所有目录
			created_dirs = [ ]
			for _, dir_path in directories.items():
				try:
					if not os.path.exists( dir_path ):
						os.makedirs( dir_path, exist_ok=True )
						created_dirs.append( dir_path )
						self.__logger.debug( f"创建目录: {dir_path}" )
					else:
						self.__logger.debug( f"目录已存在: {dir_path}" )

					# 验证目录是否可写
					if not os.access( dir_path, os.W_OK ):
						error_msg = f"目录没有写入权限: {dir_path}"
						self.__logger.error( error_msg )
						raise PermissionError( error_msg )

				except OSError as e:
					error_msg = f"创建目录失败 {dir_path}: {str( e )}"
					self.__logger.error( error_msg )
					raise OSError( error_msg )

			if created_dirs:
				self.__log_output.append( f"创建了 {len( created_dirs )} 个新目录", Colors.SUCCESS )
				self.__logger.info( f"成功创建目录结构，新建目录: {created_dirs}" )
			else:
				self.__log_output.append( "目录结构已存在，跳过创建", Colors.INFO )
				self.__logger.info( "所有目录已存在，跳过创建" )

			return directories

		except Exception as e:
			error_msg = f"创建目录结构时发生错误: {str( e )}"
			self.__logger.error( error_msg )
			self.__logger.error( traceback.format_exc() )
			self.__log_output.append( error_msg, Colors.ERROR )
			self.__export_stats[ "error_count" ] += 1
			self.__export_stats[ "errors" ].append( error_msg )
			raise

	def __determine_data_type(
		self, training_type: Any,
		include_pretrain: bool = False,
		include_train: bool = False
	) -> str:
		"""
        根据UI控件状态和training_type字段判断数据应该放入train还是val目录

        修改说明：
            此方法已从基于JSON优先级规则改为基于UI控件状态驱动的判断逻辑，
            确保数据类型判断完全基于用户在UI界面的选择。

        支持格式：
        1. JSON格式：根据控件状态选择相应字段进行判断
        2. 字符串格式：向后兼容的关键词匹配（当控件状态都为False时）

        参数:
            training_type: 数据库中的training_type字段值，可以是字符串或字典
            include_pretrain: 是否选择了预训练数据类型（rotate_152控件状态）
            include_train: 是否选择了训练数据类型（rotate_153控件状态）

        返回:
            "train" 或 "val"

        判断逻辑:
            - 如果include_pretrain=True：只查看pretrain_train和pretrain_val字段
            - 如果include_train=True：只查看train和val字段
            - 如果两个参数都为False：使用字符串关键词匹配（向后兼容）

        使用示例:
            # 预训练模式
            data_type = self.__determine_data_type(
                {"pretrain_train": "true", "train": "true"},
                include_pretrain=True,
                include_train=False
            )
            # 返回: "train" (只查看pretrain字段，忽略train字段)

            # 训练模式
            data_type = self.__determine_data_type(
                {"pretrain_train": "true", "val": "true"},
                include_pretrain=False,
                include_train=True
            )
            # 返回: "val" (只查看train字段，忽略pretrain字段)

            # 向后兼容模式
            data_type = self.__determine_data_type("validation_set")
            # 返回: "val" (使用字符串匹配)
        """
		# 参数验证
		if include_pretrain and include_train:
			error_msg = "include_pretrain和include_train不能同时为True"
			self.__logger.error( error_msg )
			raise ValueError( error_msg )

		self.__logger.debug( f"数据类型判断 - 控件状态: pretrain={include_pretrain}, train={include_train}" )
		self.__logger.debug( f"输入数据: {training_type}" )

		if not training_type:
			self.__logger.warning( "training_type为空，默认分配到train" )
			return "train"

		# 向后兼容：当两个参数都为False时，使用原有的字符串匹配逻辑
		if not include_pretrain and not include_train:
			self.__logger.debug( "未指定控件状态，使用字符串匹配逻辑" )
			return self.__determine_data_type_by_string( str( training_type ) )

		try:
			# 尝试JSON格式解析
			training_data = None
			if isinstance( training_type, str ):
				try:
					training_data = json.loads( training_type )
					self.__logger.debug( f"成功解析JSON格式的training_type: {training_data}" )
				except json.JSONDecodeError:
					# JSON解析失败，降级为字符串处理
					self.__logger.debug( f"training_type不是有效JSON，使用字符串处理: {training_type}" )
					return self.__determine_data_type_by_string( training_type )
			elif isinstance( training_type, dict ):
				training_data = training_type
				self.__logger.debug( f"接收到字典格式的training_type: {training_data}" )
			else:
				# 其他类型，转换为字符串处理
				self.__logger.debug( f"training_type类型未知，转换为字符串处理: {training_type}" )
				return self.__determine_data_type_by_string( str( training_type ) )

			# 控件状态驱动的JSON格式处理
			if isinstance( training_data, dict ):
				if include_pretrain:
					# 预训练模式：只查看pretrain相关字段
					if 'pretrain_train' in training_data and training_data[ 'pretrain_train' ]:
						self.__logger.debug( f"预训练模式识别为训练数据: {training_data} -> train" )
						return 'train'
					elif 'pretrain_val' in training_data and training_data[ 'pretrain_val' ]:
						self.__logger.debug( f"预训练模式识别为验证数据: {training_data} -> val" )
						return 'val'
					else:
						# 预训练模式下没有找到相关字段，默认为train
						self.__logger.warning( f"预训练模式下JSON中没有pretrain相关字段，默认分配到train: {training_data}" )
						return 'train'

				elif include_train:
					# 训练模式：只查看train相关字段
					if 'train' in training_data and training_data[ 'train' ]:
						self.__logger.debug( f"训练模式识别为训练数据: {training_data} -> train" )
						return 'train'
					elif 'val' in training_data and training_data[ 'val' ]:
						self.__logger.debug( f"训练模式识别为验证数据: {training_data} -> val" )
						return 'val'
					else:
						# 训练模式下没有找到相关字段，默认为train
						self.__logger.warning( f"训练模式下JSON中没有train相关字段，默认分配到train: {training_data}" )
						return 'train'
			else:
				# JSON解析成功但不是字典，降级为字符串处理
				self.__logger.debug( f"JSON解析结果不是字典，降级为字符串处理: {training_data}" )
				return self.__determine_data_type_by_string( str( training_data ) )

		except Exception as e:
			# 处理过程中出现异常，降级为字符串处理
			error_msg = f"处理training_type时出现异常，降级为字符串处理: {str( e )}"
			self.__logger.warning( error_msg )
			return self.__determine_data_type_by_string( str( training_type ) )

		# 默认返回值（理论上不应该到达这里）
		self.__logger.warning( "数据类型判断到达了意外的代码路径，默认分配到train" )
		return "train"

	def __determine_data_type_by_string( self, training_type: str ) -> str:
		"""
        通过字符串关键词匹配判断数据类型（降级处理方法）

        参数:
            training_type: 字符串格式的training_type

        返回:
            "train" 或 "val"
        """
		if not training_type:
			self.__logger.warning( "training_type字符串为空，默认分配到train" )
			return "train"

		training_type_lower = training_type.lower()

		# 判断是否为验证数据
		val_keywords = [ "val", "validation", "valid", "test", "testing" ]
		for keyword in val_keywords:
			if keyword in training_type_lower:
				self.__logger.debug( f"字符串匹配识别为验证数据: {training_type} -> val" )
				return "val"

		# 默认为训练数据
		self.__logger.debug( f"字符串匹配识别为训练数据: {training_type} -> train" )
		return "train"

	def __save_image_files(
		self, records: List[ Dict[ str, Any ] ], directories: Dict[ str, str ],
		include_pretrain: bool = False, include_train: bool = False
	) -> Dict[ str, int ]:
		"""
        将图像文件复制到对应的YOLO目录结构中

        参数:
            records: 标注数据记录列表
            directories: 目录结构字典
            include_pretrain: 是否选择了预训练数据类型（rotate_152控件状态）
            include_train: 是否选择了训练数据类型（rotate_153控件状态）

        返回:
            包含复制统计信息的字典

        异常:
            FileNotFoundError: 当源图像文件不存在时
            OSError: 当文件复制失败时

        使用示例:
            stats = self.__save_image_files(records, directories, include_pretrain=True)
            print(f"成功复制 {stats['success']} 个图像文件")
            print(f"失败 {stats['failed']} 个图像文件")
        """
		try:
			self.__logger.debug( "开始复制图像文件" )

			stats = {
				"success":     0,
				"failed":      0,
				"train_count": 0,
				"val_count":   0,
				"skipped":     0
			}

			for i, record in enumerate( records ):
				try:
					# 获取源图像路径
					source_image_path = record.get( "image_path", "" )
					if not source_image_path:
						self.__logger.warning( f"记录 {record.get( 'id', 'unknown' )} 缺少图像路径" )
						stats[ "failed" ] += 1
						continue

					# 检查源文件是否存在
					if not os.path.exists( source_image_path ):
						error_msg = f"源图像文件不存在: {source_image_path}"
						self.__logger.warning( error_msg )
						self.__export_stats[ "errors" ].append( error_msg )
						stats[ "failed" ] += 1
						continue

					# 确定数据类型
					training_type = record.get( "training_type", "" )
					data_type = self.__determine_data_type( training_type, include_pretrain, include_train )

					# 确定目标目录
					if data_type == "val":
						target_dir = directories[ "images_val" ]
						stats[ "val_count" ] += 1
					else:
						target_dir = directories[ "images_train" ]
						stats[ "train_count" ] += 1

					# 获取文件名
					image_filename = os.path.basename( source_image_path )
					target_image_path = os.path.join( target_dir, image_filename )

					# 检查目标文件是否已存在
					if os.path.exists( target_image_path ):
						# 比较文件大小，如果相同则跳过
						source_size = os.path.getsize( source_image_path )
						target_size = os.path.getsize( target_image_path )
						if source_size == target_size:
							self.__logger.debug( f"图像文件已存在且大小相同，跳过: {image_filename}" )
							stats[ "skipped" ] += 1
							stats[ "success" ] += 1
							continue

					# 复制文件
					shutil.copy2( source_image_path, target_image_path )
					self.__logger.debug( f"复制图像文件: {source_image_path} -> {target_image_path}" )
					stats[ "success" ] += 1

					# 更新进度
					if (i + 1) % 10 == 0:
						progress_msg = f"已处理图像 {i + 1}/{len( records )}"
						self.__log_output.append( progress_msg, Colors.INFO )

				except Exception as e:
					error_msg = f"复制图像文件失败 {source_image_path}: {str( e )}"
					self.__logger.error( error_msg )
					self.__export_stats[ "errors" ].append( error_msg )
					stats[ "failed" ] += 1
					continue

			# 更新统计信息
			self.__export_stats[ "processed_images" ] = stats[ "success" ]
			self.__export_stats[ "train_count" ] += stats[ "train_count" ]
			self.__export_stats[ "val_count" ] += stats[ "val_count" ]

			# 记录结果
			result_msg = f"图像文件复制完成 - 成功: {stats[ 'success' ]}, 失败: {stats[ 'failed' ]}, 跳过: {stats[ 'skipped' ]}"
			self.__logger.info( result_msg )
			self.__log_output.append( result_msg, Colors.SUCCESS if stats[ "failed" ] == 0 else Colors.WARNING )

			return stats

		except Exception as e:
			error_msg = f"复制图像文件时发生错误: {str( e )}"
			self.__logger.error( error_msg )
			self.__logger.error( traceback.format_exc() )
			self.__log_output.append( error_msg, Colors.ERROR )
			self.__export_stats[ "error_count" ] += 1
			self.__export_stats[ "errors" ].append( error_msg )
			raise

	def __save_label_files(
			self, records: List[ Dict[ str, Any ] ], directories: Dict[ str, str ],
			include_segmentation: bool, include_obb: bool,
			include_pretrain: bool = False, include_train: bool = False
	) -> Dict[ str, int ]:
		"""
        将数据库中的标注坐标转换为YOLO格式并保存为.txt文件

        数据库标注数据结构（基于实际数据格式）:
            detection_results表包含独立的JSONB字段:
            - obb_data: JSONB类型，对象数组格式：
              [{"class": 0, "points": [[x1,y1],[x2,y2],...], "label": "标签名", ...}]
            - segmentation_data: JSONB类型，对象数组格式：
              [{"class": 1, "points": [[x1,y1],[x2,y2],...], "label": "标签名", ...}]
            - image_width: INTEGER类型，图像宽度
            - image_height: INTEGER类型，图像高度

            注意：OBB和分割数据都使用相同的坐标格式[[x1,y1],[x2,y2],...]，
            区别在于OBB通常是4个点，分割数据可能是多个点

        YOLO格式说明:
            每行格式为: class_index x1 y1 x2 y2 ...
            - class_index: 类别索引（整数）
            - x1 y1 x2 y2 ...: 归一化坐标（0-1范围的浮点数）

        参数:
            records: 标注数据记录列表
            directories: 目录结构字典

        返回:
            包含标注文件生成统计信息的字典

        异常:
            ValueError: 当标注数据格式无效时
            OSError: 当文件写入失败时

        使用示例:
            stats = self.__save_label_files(records, directories)
            print(f"成功生成 {stats['success']} 个标注文件")
        """
		try:
			self.__logger.debug( "开始生成YOLO标注文件" )

			stats = {
				"success":           0,
				"failed":            0,
				"train_count":       0,
				"val_count":         0,
				"total_annotations": 0
			}

			for i, record in enumerate( records ):
				try:
					# 获取图像路径和相关数据
					image_path = record.get( "image_path", "" )
					image_width = record.get( "image_width", 640 )
					image_height = record.get( "image_height", 640 )
					obb_data = record.get( "obb_data", None )
					segmentation_data = record.get( "segmentation_data", None )

					if not image_path:
						self.__logger.warning( f"记录 {record.get( 'detection_id', 'unknown' )} 缺少图像路径" )
						stats[ "failed" ] += 1
						continue

					# 检查图像尺寸有效性
					if image_width <= 0 or image_height <= 0:
						self.__logger.warning( f"图像尺寸无效 ({image_width}x{image_height})，跳过此图像的所有标注" )
						stats[ "failed" ] += 1
						continue

					# 确定数据类型和目标目录
					training_type = record.get( "training_type", "" )
					data_type = self.__determine_data_type( training_type, include_pretrain, include_train )

					if data_type == "val":
						target_dir = directories[ "labels_val" ]
						stats[ "val_count" ] += 1
					else:
						target_dir = directories[ "labels_train" ]
						stats[ "train_count" ] += 1

					# 生成标注文件名（与图像文件名对应，但扩展名为.txt）
					image_filename = os.path.basename( image_path )
					label_filename = os.path.splitext( image_filename )[ 0 ] + ".txt"
					label_file_path = os.path.join( target_dir, label_filename )

					# 转换标注数据为YOLO格式
					yolo_lines = [ ]
					annotation_count = 0

					# 根据选择的数据类型处理对应的标注数据
					if include_obb and obb_data:
						# 处理OBB数据
						try:
							# obb_data是对象列表，每个对象包含class和points
							if isinstance( obb_data, list ):
								obb_list = obb_data
							else:
								self.__logger.warning( f"OBB数据格式不正确: {type( obb_data )}" )
								obb_list = [ ]

							for obb_item in obb_list:
								try:
									# 提取标注信息
									class_index = obb_item.get( "class", 0 )
									points = obb_item.get( "points", [ ] )

									if not points or not isinstance( points, list ):
										self.__logger.warning( f"OBB标注点数据不完整: {obb_item}" )
										continue

									# 处理points格式: [[x1,y1],[x2,y2],...]
									normalized_coords = [ ]
									for point in points:
										if isinstance( point, list ) and len( point ) >= 2:
											x, y = point[ 0 ], point[ 1 ]

											# 归一化坐标到0-1范围
											norm_x = x / image_width
											norm_y = y / image_height

											# 确保坐标在有效范围内
											norm_x = max( 0.0, min( 1.0, norm_x ) )
											norm_y = max( 0.0, min( 1.0, norm_y ) )

											normalized_coords.extend( [ norm_x, norm_y ] )
										else:
											self.__logger.warning( f"无效的点格式: {point}" )

									# 如果没有有效坐标，跳过
									if not normalized_coords:
										self.__logger.warning( "没有有效的坐标数据，跳过此标注" )
										continue

									# 生成YOLO格式行: "class_index x1 y1 x2 y2 ..."
									coords_str = ' '.join( f"{coord:.6f}" for coord in normalized_coords )
									yolo_line = f"{class_index} {coords_str}"
									yolo_lines.append( yolo_line )
									annotation_count += 1

								except Exception as e:
									error_msg = f"处理单个OBB数据时出错: {str( e )}"
									self.__logger.warning( error_msg )
									continue

						except Exception as e:
							error_msg = f"处理OBB数据时出错: {str( e )}"
							self.__logger.warning( error_msg )

					elif include_segmentation and segmentation_data:
						# 处理分割数据
						try:
							# segmentation_data是对象列表，每个对象包含class和points
							if isinstance( segmentation_data, list ):
								seg_list = segmentation_data
							else:
								self.__logger.warning( f"分割数据格式不正确: {type( segmentation_data )}" )
								seg_list = [ ]

							for seg_item in seg_list:
								try:
									# 提取标注信息
									class_index = seg_item.get( "class", 0 )
									points = seg_item.get( "points", [ ] )

									if not points or not isinstance( points, list ):
										self.__logger.warning( f"分割标注点数据不完整: {seg_item}" )
										continue

									# 处理points格式: [[x1,y1],[x2,y2],...]
									normalized_coords = [ ]
									for point in points:
										if isinstance( point, list ) and len( point ) >= 2:
											x, y = point[ 0 ], point[ 1 ]

											# 归一化坐标到0-1范围
											norm_x = x / image_width
											norm_y = y / image_height

											# 确保坐标在有效范围内
											norm_x = max( 0.0, min( 1.0, norm_x ) )
											norm_y = max( 0.0, min( 1.0, norm_y ) )

											normalized_coords.extend( [ norm_x, norm_y ] )
										else:
											self.__logger.warning( f"无效的点格式: {point}" )

									# 如果没有有效坐标，跳过
									if not normalized_coords:
										self.__logger.warning( "没有有效的坐标数据，跳过此标注" )
										continue

									# 生成YOLO格式行: "class_index x1 y1 x2 y2 ..."
									coords_str = ' '.join( f"{coord:.6f}" for coord in normalized_coords )
									yolo_line = f"{class_index} {coords_str}"
									yolo_lines.append( yolo_line )
									annotation_count += 1

								except Exception as e:
									error_msg = f"处理单个分割数据时出错: {str( e )}"
									self.__logger.warning( error_msg )
									continue

						except Exception as e:
							error_msg = f"处理分割数据时出错: {str( e )}"
							self.__logger.warning( error_msg )

					# 写入标注文件
					try:
						with open( label_file_path, 'w', encoding='utf-8' ) as f:
							f.write( '\n'.join( yolo_lines ) )

						self.__logger.debug( f"生成标注文件: {label_file_path} ({annotation_count} 个标注)" )
						stats[ "success" ] += 1
						stats[ "total_annotations" ] += annotation_count

					except OSError as e:
						error_msg = f"写入标注文件失败 {label_file_path}: {str( e )}"
						self.__logger.error( error_msg )
						self.__export_stats[ "errors" ].append( error_msg )
						stats[ "failed" ] += 1
						continue

					# 更新进度
					if (i + 1) % 10 == 0:
						progress_msg = f"已处理标注 {i + 1}/{len( records )}"
						self.__log_output.append( progress_msg, Colors.INFO )

				except Exception as e:
					error_msg = f"处理标注记录失败 {record.get( 'id', 'unknown' )}: {str( e )}"
					self.__logger.error( error_msg )
					self.__export_stats[ "errors" ].append( error_msg )
					stats[ "failed" ] += 1
					continue

			# 更新统计信息
			self.__export_stats[ "processed_labels" ] = stats[ "success" ]

			# 记录结果
			result_msg = f"标注文件生成完成 - 成功: {stats[ 'success' ]}, 失败: {stats[ 'failed' ]}, 总标注数: {stats[ 'total_annotations' ]}"
			self.__logger.info( result_msg )
			self.__log_output.append( result_msg, Colors.SUCCESS if stats[ "failed" ] == 0 else Colors.WARNING )

			return stats

		except Exception as e:
			error_msg = f"生成标注文件时发生错误: {str( e )}"
			self.__logger.error( error_msg )
			self.__logger.error( traceback.format_exc() )
			self.__log_output.append( error_msg, Colors.ERROR )
			self.__export_stats[ "error_count" ] += 1
			self.__export_stats[ "errors" ].append( error_msg )
			raise

	def export_training_data( self, db_config: Dict[ str, str ] = None ) -> bool:
		"""
        执行完整的训练数据导出流程

        该方法协调所有模块完成以下步骤：
        1. 获取UI控件配置参数（包括数据库配置）
        2. 连接数据库
        3. 查询标注数据
        4. 创建YOLO目录结构
        5. 复制图像文件
        6. 生成YOLO标注文件
        7. 生成最终统计报告

        参数:
            db_config: 数据库配置字典，包含database_name、table_name、data_type等信息。
                      如果为None，则从UI控件动态获取配置

        返回:
            导出成功返回True，否则返回False

        使用示例:
            exporter = TrainingDataExporter(
                line_edit_manager, checkbox_manager, log_output, logger
            )

            # 使用动态配置执行导出
            success = exporter.export_training_data()

            # 使用指定配置执行导出
            config = {
                "database_name": "my_database",
                "table_name": "my_table",
                "data_type": "my_type"
            }
            success = exporter.export_training_data(config)

            if success:
                print("训练数据导出成功完成")
            else:
                print("训练数据导出失败，请检查日志")
        """
		start_time = None
		database_config = None
		try:
			import time
			start_time = time.time()

			self.__logger.info( "=" * 60 )
			self.__logger.info( "开始训练数据导出流程" )
			self.__logger.info( "=" * 60 )

			self.__log_output.append( "开始训练数据导出...", Colors.INFO )

			# 重置统计信息
			self.__export_stats = {
				"total_records":    0,
				"processed_images": 0,
				"processed_labels": 0,
				"train_count":      0,
				"val_count":        0,
				"success_count":    0,
				"error_count":      0,
				"errors":           [ ]
			}

			# 步骤1: 获取UI控件配置参数（包括数据库配置）
			self.__log_output.append( "步骤1: 获取导出配置...", Colors.INFO )

			# 获取数据库配置
			if db_config is None:
				database_config = self.__get_database_config()
			else:
				database_config = db_config.copy()
				# 验证提供的配置
				if not self.__validate_database_config( database_config ):
					error_msg = "提供的数据库配置无效"
					self.__logger.error( error_msg )
					self.__log_output.append( error_msg, Colors.ERROR )
					return False

			# 获取导出路径和数据类型选择配置
			ui_config = self.__get_ui_control_values()
			target_path = ui_config[ "target_path" ]
			include_segmentation = ui_config[ "include_segmentation" ]
			include_obb = ui_config[ "include_obb" ]

			# 步骤2: 连接数据库
			self.__log_output.append( "步骤2: 连接数据库...", Colors.INFO )
			if not self.__connect_database( database_config ):
				error_msg = "数据库连接失败，无法继续导出"
				self.__logger.error( error_msg )
				self.__log_output.append( error_msg, Colors.ERROR )
				self.__clear_progress_display()
				return False

			# 步骤3: 查询标注数据
			self.__log_output.append( "步骤3: 查询标注数据...", Colors.INFO )
			records = self.__query_annotation_data(
				include_segmentation, include_obb,
				ui_config["include_pretrain"], ui_config["include_train"],
				database_config
			)

			if not records:
				warning_msg = "未查询到任何标注数据，导出终止"
				self.__logger.warning( warning_msg )
				self.__log_output.append( warning_msg, Colors.WARNING )
				self.__clear_progress_display()
				return False

			# 设置总进度 - 从数据库获取到数据，开始转换成YOLO格式
			self.__set_total_progress(f"正在处理 {len(records)} 条数据，转换为YOLO格式...")

			# 步骤4: 创建YOLO目录结构
			self.__log_output.append( "步骤4: 创建目录结构...", Colors.INFO )
			directories = self.__create_directory_structure( target_path )

			# 步骤5: 复制图像文件
			self.__log_output.append( "步骤5: 复制图像文件...", Colors.INFO )
			image_stats = self.__save_image_files( records, directories, ui_config["include_pretrain"], ui_config["include_train"] )

			# 步骤6: 生成YOLO标注文件
			self.__log_output.append( "步骤6: 生成标注文件...", Colors.INFO )
			label_stats = self.__save_label_files( records, directories, include_segmentation, include_obb, ui_config["include_pretrain"], ui_config["include_train"] )

			# 步骤7: 生成最终统计报告
			self.__log_output.append( "步骤7: 生成统计报告...", Colors.INFO )
			self.__generate_final_report( start_time, image_stats, label_stats, target_path, database_config )

			# 判断导出是否成功
			total_errors = self.__export_stats[ "error_count" ] + image_stats.get( "failed", 0 ) + label_stats.get(
				"failed", 0
			)

			if total_errors == 0:
				self.__set_completion_status("导出完成")
				self.__log_output.append( "训练数据导出成功完成！", Colors.SUCCESS )
				self.__logger.info( "训练数据导出成功完成" )
				return True
			else:
				self.__set_completion_status(f"完成但有{total_errors}个错误")
				warning_msg = f"训练数据导出完成，但有 {total_errors} 个错误"
				self.__log_output.append( warning_msg, Colors.WARNING )
				self.__logger.warning( warning_msg )
				return False

		except Exception as e:
			error_msg = f"训练数据导出过程中发生严重错误: {str( e )}"
			self.__logger.error( error_msg )
			self.__logger.error( traceback.format_exc() )
			self.__log_output.append( error_msg, Colors.ERROR )

			# 清空进度显示
			self.__clear_progress_display()

			# 生成错误报告
			if start_time:
				self.__generate_error_report( start_time, str( e ) )

			return False

		finally:
			# 清理数据库连接
			if self.__db_client:
				try:
					self.__db_client.close()
					self.__logger.debug( "数据库连接已关闭" )
				except Exception as e:
					self.__logger.warning( f"关闭数据库连接时出错: {str( e )}" )

			self.__logger.info( "=" * 60 )
			self.__logger.info( "训练数据导出流程结束" )
			self.__logger.info( "=" * 60 )

	def __generate_final_report(
			self, start_time: float, image_stats: Dict[ str, int ],
			label_stats: Dict[ str, int ], target_path: str,
			database_config: Dict[ str, str ] = None
	) -> None:
		"""
        生成最终的导出统计报告

        参数:
            start_time: 开始时间戳
            image_stats: 图像文件处理统计
            label_stats: 标注文件处理统计
            target_path: 导出目标路径
            database_config: 数据库配置信息，用于报告中显示
        """
		try:
			import time
			end_time = time.time()
			duration = end_time - start_time

			# 计算总体统计
			total_success = image_stats.get( "success", 0 ) + label_stats.get( "success", 0 )
			total_failed = image_stats.get( "failed", 0 ) + label_stats.get( "failed", 0 )

			# 生成详细报告
			report_lines = [
				"",
				"=" * 50,
				"训练数据导出完成报告",
				"=" * 50,
				f"导出路径: {target_path}",
				f"总耗时: {duration:.2f} 秒",
				""
			]

			# 添加数据库配置信息
			if database_config:
				report_lines.extend(
					[
						"数据库配置:",
						f"  数据库名称: {database_config.get( 'database_name', 'N/A' )}",
						f"  表名称: {database_config.get( 'table_name', 'N/A' )}",
						f"  数据类型: {database_config.get( 'data_type', 'N/A' )}",
						""
					]
				)

			report_lines.extend(
				[
					"数据统计:",
					f"  总记录数: {self.__export_stats[ 'total_records' ]}",
					f"  训练数据: {self.__export_stats[ 'train_count' ]}",
					f"  验证数据: {self.__export_stats[ 'val_count' ]}",
					"",
					"图像文件:",
					f"  成功复制: {image_stats.get( 'success', 0 )}",
					f"  复制失败: {image_stats.get( 'failed', 0 )}",
					f"  跳过文件: {image_stats.get( 'skipped', 0 )}",
					"",
					"标注文件:",
					f"  成功生成: {label_stats.get( 'success', 0 )}",
					f"  生成失败: {label_stats.get( 'failed', 0 )}",
					f"  总标注数: {label_stats.get( 'total_annotations', 0 )}",
					"",
					"总体结果:",
					f"  成功操作: {total_success}",
					f"  失败操作: {total_failed}",
					f"  错误总数: {len( self.__export_stats[ 'errors' ] )}",
					"=" * 50
				]
			)

			# 输出到日志
			for line in report_lines:
				self.__logger.info( line )

			# 输出到UI（简化版本）
			summary = f"导出完成 - 成功: {total_success}, 失败: {total_failed}, 耗时: {duration:.1f}s"
			self.__log_output.append( summary, Colors.SUCCESS if total_failed == 0 else Colors.WARNING )

			# 如果有错误，输出错误详情
			if self.__export_stats[ "errors" ]:
				self.__logger.warning( "错误详情:" )
				for i, error in enumerate( self.__export_stats[ "errors" ][ :10 ], 1 ):  # 只显示前10个错误
					self.__logger.warning( f"  {i}. {error}" )

				if len( self.__export_stats[ "errors" ] ) > 10:
					self.__logger.warning( f"  ... 还有 {len( self.__export_stats[ 'errors' ] ) - 10} 个错误" )

		except Exception as e:
			self.__logger.error( f"生成最终报告时出错: {str( e )}" )

	def __generate_error_report( self, start_time: float, error_message: str ) -> None:
		"""
        生成错误报告

        参数:
            start_time: 开始时间戳
            error_message: 错误消息
        """
		try:
			import time
			end_time = time.time()
			duration = end_time - start_time

			error_report = [
				"",
				"=" * 50,
				"训练数据导出错误报告",
				"=" * 50,
				f"导出失败时间: {time.strftime( '%Y-%m-%d %H:%M:%S' )}",
				f"运行时长: {duration:.2f} 秒",
				f"主要错误: {error_message}",
				"",
				"已处理统计:",
				f"  总记录数: {self.__export_stats[ 'total_records' ]}",
				f"  已处理图像: {self.__export_stats[ 'processed_images' ]}",
				f"  已处理标注: {self.__export_stats[ 'processed_labels' ]}",
				"",
				"错误详情:",
			]

			for i, error in enumerate( self.__export_stats[ "errors" ], 1 ):
				error_report.append( f"  {i}. {error}" )

			error_report.append( "=" * 50 )

			# 输出错误报告
			for line in error_report:
				self.__logger.error( line )

		except Exception as e:
			self.__logger.error( f"生成错误报告时出错: {str( e )}" )

	def get_export_statistics( self ) -> Dict[ str, Any ]:
		"""
        获取导出统计信息

        返回:
            包含详细统计信息的字典

        使用示例:
            exporter = TrainingDataExporter(...)
            exporter.export_training_data()

            stats = exporter.get_export_statistics()
            print(f"总记录数: {stats['total_records']}")
            print(f"成功处理: {stats['success_count']}")
            print(f"错误数量: {stats['error_count']}")
        """
		return self.__export_stats.copy()

	def get_last_errors( self, limit: int = 10 ) -> List[ str ]:
		"""
        获取最近的错误信息

        参数:
            limit: 返回的错误数量限制，默认为10

        返回:
            错误信息列表

        使用示例:
            errors = exporter.get_last_errors(5)
            for error in errors:
                print(f"错误: {error}")
        """
		return self.__export_stats[ "errors" ][ -limit: ] if self.__export_stats[ "errors" ] else [ ]

	def reset_statistics( self ) -> None:
		"""
        重置统计信息

        使用示例:
            exporter.reset_statistics()  # 清空之前的统计数据
            exporter.export_training_data()  # 重新开始导出
        """
		self.__export_stats = {
			"total_records":    0,
			"processed_images": 0,
			"processed_labels": 0,
			"train_count":      0,
			"val_count":        0,
			"success_count":    0,
			"error_count":      0,
			"errors":           [ ]
		}
		self.__logger.debug( "导出统计信息已重置" )

	def validate_export_path( self, path: str ) -> Tuple[ bool, str ]:
		"""
        验证导出路径的有效性

        参数:
            path: 要验证的路径

        返回:
            (是否有效, 错误信息或成功信息)

        使用示例:
            is_valid, message = exporter.validate_export_path("/path/to/export")
            if is_valid:
                print(f"路径有效: {message}")
            else:
                print(f"路径无效: {message}")
        """
		try:
			if not path or not path.strip():
				return False, "路径不能为空"

			path = path.strip()
			path_obj = Path( path )

			# 检查路径是否存在
			if path_obj.exists():
				if not path_obj.is_dir():
					return False, "路径存在但不是目录"

				# 检查写入权限
				if not os.access( path, os.W_OK ):
					return False, "目录没有写入权限"

				return True, "路径有效且可写"
			else:
				# 检查父目录是否存在且可写
				parent_dir = path_obj.parent
				if not parent_dir.exists():
					return False, f"父目录不存在: {parent_dir}"

				if not os.access( str( parent_dir ), os.W_OK ):
					return False, f"父目录没有写入权限: {parent_dir}"

				return True, "路径可以创建"

		except Exception as e:
			return False, f"路径验证时出错: {str( e )}"


class JsonAnnotationClassIndexProcessor:
	"""
    JSON标注文件类索引补全工具类

    该类用于为缺少class索引的标注数据自动添加正确的类别索引。
    它从UI控件获取JSON文件夹路径和YAML配置文件路径，读取JSON文件中shapes列表的标注数据，
    并根据YAML文件中的names映射为缺失class字段的shape添加正确的类别索引。

    支持的JSON文件格式：
    {
        "shapes": [
            {"label": "主人物", "points": [...], ...},
            {"label": "亚基矿", "points": [...], ...}
        ],
        "imagePath": "...",
        "imageHeight": 700,
        "imageWidth": 950,
        ...
    }

    主要功能：
    1. 从UI控件获取文件路径配置
    2. 扫描并读取JSON文件（包含shapes列表的dict对象格式）
    3. 解析YAML配置文件中的names字段
    4. 为缺少class键的shape添加对应的类别索引
    5. 保持原始文件格式和结构不变
    6. 提供详细的处理进度和错误日志

    属性:
        __line_edit_manager: LineEditManager实例，用于获取UI控件数据
        __log_output: LogOutput实例，用于在UI界面显示重点日志信息
        __logger: Logger实例，用于在控制台输出详细日志
        __control_config: 控件配置字典，包含文件夹路径和YAML文件路径控件名称
        __stats: 处理统计信息字典

    使用示例:
        # 创建实例
        processor = JsonAnnotationClassIndexProcessor(
            line_edit_manager=my_line_edit_manager,
            log_output=my_log_output,
            logger=my_logger
        )

        # 执行类索引补全
        success = processor.process_json_files()

        # 检查结果
        if success:
            print("类索引补全成功完成")
            stats = processor.get_processing_statistics()
            print(f"处理了 {stats['total_files']} 个文件")
        else:
            print("类索引补全过程中出现错误")
    """

	def __init__(
			self,
			line_edit_manager: LineEditManager,
			log_output: LogOutput,
			logger: Logger,
			control_config: dict = None
	):
		"""
        初始化JSON标注文件类索引补全处理器

        参数:
            line_edit_manager: LineEditManager实例，用于获取UI控件数据
            log_output: LogOutput实例，用于在UI界面显示重点日志信息
            logger: Logger实例，用于在控制台输出详细日志
            control_config: 控件配置字典，默认值为：
                {
                    "folder_path_control": "lineEdit_6",      # 文件夹路径控件
                    "yaml_file_control": "lineEdit_12"       # YAML文件路径控件
                }

        使用示例:
            processor = JsonAnnotationClassIndexProcessor(
                line_edit_manager=my_line_edit_manager,
                log_output=my_log_output,
                logger=my_logger,
                control_config={
                    "folder_path_control": "lineEdit_6",
                    "yaml_file_control": "lineEdit_12"
                }
            )
        """
		self.__line_edit_manager = line_edit_manager
		self.__log_output = log_output
		self.__logger = logger

		# 设置默认控件配置
		self.__control_config = control_config or {
			"folder_path_control": "lineEdit_6",  # 文件夹路径控件
			"yaml_file_control":   "lineEdit_12"  # YAML文件路径控件
		}

		# 初始化处理统计信息
		self.__stats = {
			"total_files":     0,  # 总文件数
			"processed_files": 0,  # 已处理文件数
			"updated_files":   0,  # 已更新文件数
			"total_shapes":    0,  # 总shape数量
			"updated_shapes":  0,  # 已更新shape数量
			"skipped_shapes":  0,  # 跳过的shape数量（已有class字段）
			"error_count":     0,  # 错误计数
			"errors":          [ ]  # 错误详情列表
		}

		self.__logger.info( "JsonAnnotationClassIndexProcessor 初始化完成" )
		self.__log_output.append( "JSON标注文件类索引补全工具已初始化", Colors.INFO )

	def __get_ui_control_values( self ) -> Dict[ str, str ]:
		"""
        从UI控件获取文件路径配置参数

        返回:
            包含文件路径配置的字典，包含以下键：
            - folder_path: JSON文件所在文件夹路径
            - yaml_file_path: YAML配置文件路径

        异常:
            ValueError: 当路径为空或无效时
            RuntimeError: 当UI控件访问失败时

        使用示例:
            config = self.__get_ui_control_values()
            print(f"JSON文件夹路径: {config['folder_path']}")
            print(f"YAML文件路径: {config['yaml_file_path']}")
        """
		try:
			self.__logger.debug( "开始获取UI控件数据" )

			# 获取JSON文件夹路径
			folder_path = ""
			try:
				folder_path_control = self.__control_config[ "folder_path_control" ]
				folder_path = self.__line_edit_manager.get_text( folder_path_control )
				if not folder_path or not folder_path.strip():
					raise ValueError( "JSON文件夹路径不能为空" )
				folder_path = folder_path.strip()
				self.__logger.debug( f"获取到JSON文件夹路径: {folder_path}" )
			except Exception as e:
				error_msg = f"获取JSON文件夹路径失败: {str( e )}"
				self.__logger.error( error_msg )
				self.__log_output.append( error_msg, Colors.ERROR )
				raise ValueError( error_msg )

			# 获取YAML文件路径
			yaml_file_path = ""
			try:
				yaml_file_control = self.__control_config[ "yaml_file_control" ]
				yaml_file_path = self.__line_edit_manager.get_text( yaml_file_control )
				if not yaml_file_path or not yaml_file_path.strip():
					raise ValueError( "YAML文件路径不能为空" )
				yaml_file_path = yaml_file_path.strip()
				self.__logger.debug( f"获取到YAML文件路径: {yaml_file_path}" )
			except Exception as e:
				error_msg = f"获取YAML文件路径失败: {str( e )}"
				self.__logger.error( error_msg )
				self.__log_output.append( error_msg, Colors.ERROR )
				raise ValueError( error_msg )

			# 验证文件夹路径的有效性
			try:
				folder_path_obj = Path( folder_path )
				if not folder_path_obj.exists():
					error_msg = f"JSON文件夹不存在: {folder_path}"
					self.__logger.error( error_msg )
					self.__log_output.append( error_msg, Colors.ERROR )
					raise ValueError( error_msg )

				if not folder_path_obj.is_dir():
					error_msg = f"JSON文件夹路径不是有效的目录: {folder_path}"
					self.__logger.error( error_msg )
					self.__log_output.append( error_msg, Colors.ERROR )
					raise ValueError( error_msg )
			except Exception as e:
				error_msg = f"JSON文件夹路径验证失败: {str( e )}"
				self.__logger.error( error_msg )
				self.__log_output.append( error_msg, Colors.ERROR )
				raise ValueError( error_msg )

			# 验证YAML文件路径的有效性
			try:
				yaml_file_obj = Path( yaml_file_path )
				if not yaml_file_obj.exists():
					error_msg = f"YAML文件不存在: {yaml_file_path}"
					self.__logger.error( error_msg )
					self.__log_output.append( error_msg, Colors.ERROR )
					raise ValueError( error_msg )

				if not yaml_file_obj.is_file():
					error_msg = f"YAML路径不是有效的文件: {yaml_file_path}"
					self.__logger.error( error_msg )
					self.__log_output.append( error_msg, Colors.ERROR )
					raise ValueError( error_msg )

				if yaml_file_obj.suffix.lower() not in [ '.yaml', '.yml' ]:
					error_msg = f"文件不是有效的YAML格式: {yaml_file_path}"
					self.__logger.error( error_msg )
					self.__log_output.append( error_msg, Colors.ERROR )
					raise ValueError( error_msg )
			except Exception as e:
				error_msg = f"YAML文件路径验证失败: {str( e )}"
				self.__logger.error( error_msg )
				self.__log_output.append( error_msg, Colors.ERROR )
				raise ValueError( error_msg )

			config = {
				"folder_path":    folder_path,
				"yaml_file_path": yaml_file_path
			}

			self.__logger.info( f"UI控件数据获取成功: {config}" )
			self.__log_output.append(
				f"配置获取成功 - JSON文件夹: {folder_path}, YAML文件: {yaml_file_path}",
				Colors.INFO
			)

			return config

		except Exception as e:
			error_msg = f"获取UI控件数据时发生未预期错误: {str( e )}"
			self.__logger.error( error_msg )
			self.__logger.error( traceback.format_exc() )
			self.__log_output.append( error_msg, Colors.ERROR )
			raise RuntimeError( error_msg )

	def __load_yaml_names_mapping( self, yaml_file_path: str ) -> Dict[ str, int ]:
		"""
        从YAML文件中加载names字段的标签名到类别索引的映射

        参数:
            yaml_file_path: YAML文件路径

        返回:
            标签名到类别索引的映射字典，格式为 {"label_name": class_index, ...}

        异常:
            FileNotFoundError: 当YAML文件不存在时
            ValueError: 当YAML文件格式错误或缺少names字段时
            RuntimeError: 当文件读取失败时

        使用示例:
            names_mapping = self.__load_yaml_names_mapping("/path/to/config.yaml")
            # 返回: {"person": 0, "car": 1, "bicycle": 2, ...}
        """
		try:
			self.__logger.debug( f"开始加载YAML文件: {yaml_file_path}" )

			# 读取YAML文件
			import yaml
			with open( yaml_file_path, 'r', encoding='utf-8' ) as f:
				yaml_data = yaml.safe_load( f )

			if yaml_data is None:
				error_msg = f"YAML文件为空或格式错误: {yaml_file_path}"
				self.__logger.error( error_msg )
				raise ValueError( error_msg )

			# 检查是否存在names字段
			if 'names' not in yaml_data:
				error_msg = f"YAML文件中缺少'names'字段: {yaml_file_path}"
				self.__logger.error( error_msg )
				raise ValueError( error_msg )

			names_data = yaml_data[ 'names' ]
			if not isinstance( names_data, dict ):
				error_msg = f"YAML文件中'names'字段格式错误，应为字典格式: {yaml_file_path}"
				self.__logger.error( error_msg )
				raise ValueError( error_msg )

			# 构建标签名到类别索引的映射
			# YAML格式: {class_index: "label_name", ...} -> {"label_name": class_index, ...}
			names_mapping = { }
			for class_index, label_name in names_data.items():
				if not isinstance( label_name, str ):
					self.__logger.warning( f"跳过非字符串标签: {label_name} (索引: {class_index})" )
					continue

				# 确保class_index是整数
				try:
					class_index_int = int( class_index )
					names_mapping[ label_name ] = class_index_int
				except (ValueError, TypeError):
					self.__logger.warning( f"跳过无效的类别索引: {class_index} (标签: {label_name})" )
					continue

			if not names_mapping:
				error_msg = f"YAML文件中没有有效的标签映射: {yaml_file_path}"
				self.__logger.error( error_msg )
				raise ValueError( error_msg )

			self.__logger.info( f"成功加载YAML标签映射，共 {len( names_mapping )} 个标签" )
			self.__logger.debug( f"标签映射: {names_mapping}" )

			return names_mapping

		except FileNotFoundError:
			error_msg = f"YAML文件不存在: {yaml_file_path}"
			self.__logger.error( error_msg )
			raise FileNotFoundError( error_msg )
		except yaml.YAMLError as e:
			error_msg = f"YAML文件解析错误: {str( e )}"
			self.__logger.error( error_msg )
			raise ValueError( error_msg )
		except Exception as e:
			error_msg = f"加载YAML文件时发生错误: {str( e )}"
			self.__logger.error( error_msg )
			self.__logger.error( traceback.format_exc() )
			raise RuntimeError( error_msg )

	def __scan_json_files( self, folder_path: str ) -> List[ str ]:
		"""
        扫描指定文件夹中的所有JSON文件

        参数:
            folder_path: 要扫描的文件夹路径

        返回:
            JSON文件路径列表

        异常:
            OSError: 当文件夹访问失败时

        使用示例:
            json_files = self.__scan_json_files("/path/to/json/folder")
            print(f"找到 {len(json_files)} 个JSON文件")
        """
		try:
			self.__logger.debug( f"开始扫描JSON文件: {folder_path}" )

			folder_path_obj = Path( folder_path )
			json_files = [ ]

			# 扫描所有.json文件
			for json_file in folder_path_obj.glob( "*.json" ):
				if json_file.is_file():
					json_files.append( str( json_file ) )
					self.__logger.debug( f"找到JSON文件: {json_file}" )

			self.__logger.info( f"扫描完成，共找到 {len( json_files )} 个JSON文件" )
			return json_files

		except Exception as e:
			error_msg = f"扫描JSON文件时发生错误: {str( e )}"
			self.__logger.error( error_msg )
			raise OSError( error_msg )

	def __process_single_json_file( self, json_file_path: str, names_mapping: Dict[ str, int ] ) -> Tuple[
		bool, int, int ]:
		"""
        处理单个JSON文件，为缺少class字段的shape添加类别索引

        该方法处理包含shapes列表的JSON文件格式：
        {
            "shapes": [
                {"label": "主人物", "points": [...], ...},
                {"label": "亚基矿", "points": [...], ...}
            ],
            "imagePath": "...",
            ...
        }

        参数:
            json_file_path: JSON文件路径
            names_mapping: 标签名到类别索引的映射字典

        返回:
            (是否有更新, 总shape数量, 更新的shape数量)

        异常:
            FileNotFoundError: 当JSON文件不存在时
            ValueError: 当JSON文件格式错误时
            RuntimeError: 当文件处理失败时

        使用示例:
            updated, total, changed = self.__process_single_json_file(
                "/path/to/file.json",
                {"主人物": 0, "亚基矿": 1}
            )
            print(f"文件处理完成: 总计{total}个shape，更新了{changed}个")
        """
		try:
			self.__logger.debug( f"开始处理JSON文件: {json_file_path}" )

			# 读取JSON文件
			with open( json_file_path, 'r', encoding='utf-8' ) as f:
				json_data = json.load( f )

			# 验证JSON数据格式（应为包含shapes键的dict对象）
			if not isinstance( json_data, dict ):
				error_msg = f"JSON文件格式错误，应为dict对象格式: {json_file_path}"
				self.__logger.error( error_msg )
				raise ValueError( error_msg )

			# 检查是否存在shapes字段
			if 'shapes' not in json_data:
				self.__logger.warning( f"JSON文件中没有'shapes'字段，跳过处理: {json_file_path}" )
				return False, 0, 0

			shapes_list = json_data[ 'shapes' ]
			if not isinstance( shapes_list, list ):
				error_msg = f"JSON文件中'shapes'字段不是列表格式: {json_file_path}"
				self.__logger.error( error_msg )
				raise ValueError( error_msg )

			total_shapes = len( shapes_list )
			updated_shapes = 0
			file_modified = False

			# 处理每个shape元素
			for i, shape in enumerate( shapes_list ):
				if not isinstance( shape, dict ):
					self.__logger.warning( f"跳过非dict的shape元素 (索引 {i}): {type( shape )}" )
					continue

				# 检查是否已存在class字段
				if 'class' in shape:
					self.__logger.debug( f"shape {i} 已有class字段，跳过" )
					continue

				# 检查是否存在label字段
				if 'label' not in shape:
					self.__logger.warning( f"shape {i} 缺少label字段，无法添加class索引" )
					continue

				label_value = shape[ 'label' ]
				if not isinstance( label_value, str ):
					self.__logger.warning( f"shape {i} 的label字段不是字符串: {label_value}" )
					continue

				# 在names映射中查找对应的class_index
				if label_value in names_mapping:
					class_index = names_mapping[ label_value ]
					shape[ 'class' ] = class_index
					updated_shapes += 1
					file_modified = True
					self.__logger.debug( f"为shape {i} 添加class索引: {label_value} -> {class_index}" )
				else:
					self.__logger.warning( f"shape {i} 的label '{label_value}' 在YAML映射中未找到" )

			# 如果文件有修改，保存回原文件
			if file_modified:
				# 备份原文件（可选）
				# backup_path = json_file_path + ".backup"
				# shutil.copy2(json_file_path, backup_path)

				# 保存修改后的数据，保持原始格式
				with open( json_file_path, 'w', encoding='utf-8' ) as f:
					json.dump( json_data, f, ensure_ascii=False, indent=2 )

				self.__logger.info( f"文件已更新: {json_file_path} (更新了 {updated_shapes}/{total_shapes} 个shape)" )
			else:
				self.__logger.debug( f"文件无需更新: {json_file_path}" )

			return file_modified, total_shapes, updated_shapes

		except FileNotFoundError:
			error_msg = f"JSON文件不存在: {json_file_path}"
			self.__logger.error( error_msg )
			raise FileNotFoundError( error_msg )
		except json.JSONDecodeError as e:
			error_msg = f"JSON文件解析错误: {str( e )}"
			self.__logger.error( error_msg )
			raise ValueError( error_msg )
		except Exception as e:
			error_msg = f"处理JSON文件时发生错误: {str( e )}"
			self.__logger.error( error_msg )
			self.__logger.error( traceback.format_exc() )
			raise RuntimeError( error_msg )

	def process_json_files( self ) -> bool:
		"""
        执行完整的JSON文件类索引补全流程

        该方法协调所有模块完成以下步骤：
        1. 获取UI控件配置参数
        2. 加载YAML文件中的names映射
        3. 扫描JSON文件夹中的所有JSON文件
        4. 逐个处理JSON文件，添加缺失的class字段
        5. 生成最终统计报告

        返回:
            处理成功返回True，否则返回False

        使用示例:
            processor = JsonAnnotationClassIndexProcessor(
                line_edit_manager, log_output, logger
            )

            # 执行处理
            success = processor.process_json_files()

            if success:
                print("JSON文件类索引补全成功完成")
                stats = processor.get_processing_statistics()
                print(f"处理了 {stats['total_files']} 个文件")
            else:
                print("JSON文件类索引补全失败，请检查日志")
        """
		start_time = None
		try:
			import time
			start_time = time.time()

			self.__logger.info( "=" * 60 )
			self.__logger.info( "开始JSON文件类索引补全流程" )
			self.__logger.info( "=" * 60 )

			self.__log_output.append( "开始JSON文件类索引补全...", Colors.INFO )

			# 重置统计信息
			self.__stats = {
				"total_files":     0,
				"processed_files": 0,
				"updated_files":   0,
				"total_shapes":    0,
				"updated_shapes":  0,
				"skipped_shapes":  0,
				"error_count":     0,
				"errors":          [ ]
			}

			# 步骤1: 获取UI控件配置参数
			self.__log_output.append( "步骤1: 获取配置参数...", Colors.INFO )
			config = self.__get_ui_control_values()

			folder_path = config[ "folder_path" ]
			yaml_file_path = config[ "yaml_file_path" ]

			# 步骤2: 加载YAML文件中的names映射
			self.__log_output.append( "步骤2: 加载YAML配置...", Colors.INFO )
			names_mapping = self.__load_yaml_names_mapping( yaml_file_path )

			# 步骤3: 扫描JSON文件
			self.__log_output.append( "步骤3: 扫描JSON文件...", Colors.INFO )
			json_files = self.__scan_json_files( folder_path )

			if not json_files:
				warning_msg = "未找到任何JSON文件，处理终止"
				self.__logger.warning( warning_msg )
				self.__log_output.append( warning_msg, Colors.WARNING )
				return False

			self.__stats[ "total_files" ] = len( json_files )

			# 步骤4: 逐个处理JSON文件
			self.__log_output.append( "步骤4: 处理JSON文件...", Colors.INFO )
			for i, json_file_path in enumerate( json_files ):
				try:
					self.__logger.debug( f"处理文件 {i + 1}/{len( json_files )}: {json_file_path}" )

					file_updated, total_shapes, updated_shapes = self.__process_single_json_file(
						json_file_path, names_mapping
					)

					self.__stats[ "processed_files" ] += 1
					self.__stats[ "total_shapes" ] += total_shapes
					self.__stats[ "updated_shapes" ] += updated_shapes
					self.__stats[ "skipped_shapes" ] += (total_shapes - updated_shapes)

					if file_updated:
						self.__stats[ "updated_files" ] += 1

					# 更新进度
					if (i + 1) % 5 == 0 or (i + 1) == len( json_files ):
						progress_msg = f"已处理 {i + 1}/{len( json_files )} 个文件"
						self.__log_output.append( progress_msg, Colors.INFO )

				except Exception as e:
					error_msg = f"处理文件失败 {json_file_path}: {str( e )}"
					self.__logger.error( error_msg )
					self.__stats[ "error_count" ] += 1
					self.__stats[ "errors" ].append( error_msg )
					continue

			# 步骤5: 生成最终统计报告
			self.__log_output.append( "步骤5: 生成统计报告...", Colors.INFO )
			self.__generate_final_report( start_time )

			# 判断处理是否成功
			if self.__stats[ "error_count" ] == 0:
				self.__log_output.append( "JSON文件类索引补全成功完成！", Colors.SUCCESS )
				self.__logger.info( "JSON文件类索引补全成功完成" )
				return True
			else:
				warning_msg = f"JSON文件类索引补全完成，但有 {self.__stats[ 'error_count' ]} 个错误"
				self.__log_output.append( warning_msg, Colors.WARNING )
				self.__logger.warning( warning_msg )
				return False

		except Exception as e:
			error_msg = f"JSON文件类索引补全过程中发生严重错误: {str( e )}"
			self.__logger.error( error_msg )
			self.__logger.error( traceback.format_exc() )
			self.__log_output.append( error_msg, Colors.ERROR )

			# 生成错误报告
			if start_time:
				self.__generate_error_report( start_time, str( e ) )

			return False

		finally:
			self.__logger.info( "=" * 60 )
			self.__logger.info( "JSON文件类索引补全流程结束" )
			self.__logger.info( "=" * 60 )

	def __generate_final_report( self, start_time: float ) -> None:
		"""
        生成最终的处理统计报告

        参数:
            start_time: 开始时间戳
        """
		try:
			import time
			end_time = time.time()
			duration = end_time - start_time

			# 生成详细报告
			report_lines = [
				"",
				"=" * 50,
				"JSON文件类索引补全完成报告",
				"=" * 50,
				f"总耗时: {duration:.2f} 秒",
				"",
				"文件统计:",
				f"  总文件数: {self.__stats[ 'total_files' ]}",
				f"  已处理文件: {self.__stats[ 'processed_files' ]}",
				f"  已更新文件: {self.__stats[ 'updated_files' ]}",
				"",
				"数据统计:",
				f"  总shape数量: {self.__stats[ 'total_shapes' ]}",
				f"  已更新shape: {self.__stats[ 'updated_shapes' ]}",
				f"  跳过shape: {self.__stats[ 'skipped_shapes' ]}",
				"",
				"错误统计:",
				f"  错误总数: {self.__stats[ 'error_count' ]}",
				"=" * 50
			]

			# 输出到日志
			for line in report_lines:
				self.__logger.info( line )

			# 输出到UI（简化版本）
			summary = f"处理完成 - 文件: {self.__stats[ 'updated_files' ]}/{self.__stats[ 'total_files' ]}, " \
			          f"shape: {self.__stats[ 'updated_shapes' ]}/{self.__stats[ 'total_shapes' ]}, " \
			          f"耗时: {duration:.1f}s"
			self.__log_output.append(
				summary, Colors.SUCCESS if self.__stats[ 'error_count' ] == 0 else Colors.WARNING
			)

			# 如果有错误，输出错误详情
			if self.__stats[ "errors" ]:
				self.__logger.warning( "错误详情:" )
				for i, error in enumerate( self.__stats[ "errors" ][ :10 ], 1 ):  # 只显示前10个错误
					self.__logger.warning( f"  {i}. {error}" )

				if len( self.__stats[ "errors" ] ) > 10:
					self.__logger.warning( f"  ... 还有 {len( self.__stats[ 'errors' ] ) - 10} 个错误" )

		except Exception as e:
			self.__logger.error( f"生成最终报告时出错: {str( e )}" )

	def __generate_error_report( self, start_time: float, error_message: str ) -> None:
		"""
        生成错误报告

        参数:
            start_time: 开始时间戳
            error_message: 错误消息
        """
		try:
			import time
			end_time = time.time()
			duration = end_time - start_time

			error_report = [
				"",
				"=" * 50,
				"JSON文件类索引补全错误报告",
				"=" * 50,
				f"处理失败时间: {time.strftime( '%Y-%m-%d %H:%M:%S' )}",
				f"运行时长: {duration:.2f} 秒",
				f"主要错误: {error_message}",
				"",
				"已处理统计:",
				f"  总文件数: {self.__stats[ 'total_files' ]}",
				f"  已处理文件: {self.__stats[ 'processed_files' ]}",
				f"  已更新文件: {self.__stats[ 'updated_files' ]}",
				"",
				"错误详情:",
			]

			for i, error in enumerate( self.__stats[ "errors" ], 1 ):
				error_report.append( f"  {i}. {error}" )

			error_report.append( "=" * 50 )

			# 输出错误报告
			for line in error_report:
				self.__logger.error( line )

		except Exception as e:
			self.__logger.error( f"生成错误报告时出错: {str( e )}" )

	def get_processing_statistics( self ) -> Dict[ str, Any ]:
		"""
        获取处理统计信息

        返回:
            包含详细统计信息的字典

        使用示例:
            processor = JsonAnnotationClassIndexProcessor(...)
            processor.process_json_files()

            stats = processor.get_processing_statistics()
            print(f"总文件数: {stats['total_files']}")
            print(f"已更新文件: {stats['updated_files']}")
            print(f"错误数量: {stats['error_count']}")
        """
		return self.__stats.copy()

	def get_last_errors( self, limit: int = 10 ) -> List[ str ]:
		"""
        获取最近的错误信息

        参数:
            limit: 返回的错误数量限制，默认为10

        返回:
            错误信息列表

        使用示例:
            errors = processor.get_last_errors(5)
            for error in errors:
                print(f"错误: {error}")
        """
		return self.__stats[ "errors" ][ -limit: ] if self.__stats[ "errors" ] else [ ]

	def reset_statistics( self ) -> None:
		"""
        重置统计信息

        使用示例:
            processor.reset_statistics()  # 清空之前的统计数据
            processor.process_json_files()  # 重新开始处理
        """
		self.__stats = {
			"total_files":     0,
			"processed_files": 0,
			"updated_files":   0,
			"total_shapes":    0,
			"updated_shapes":  0,
			"skipped_shapes":  0,
			"error_count":     0,
			"errors":          [ ]
		}
		self.__logger.debug( "处理统计信息已重置" )
